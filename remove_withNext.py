#!/usr/bin/env python3

# GET routes that perform DB writes (need withTransaction)
WRITE_GET_ROUTES = {
    "/api/v1/app-auth/token",
    "/api/v1/app-ctx", 
    "/api/v1/auth/oidc/guidewire-apac/callback",
    "/api/v1/auth/oidc/guidewire-eu/callback",
    "/api/v1/auth/oidc/guidewire/callback",
    "/api/v1/auth/oidc/test/callback",
    "/api/v1/hosting-hit",
    "/api/v1/oauth2/authorize",
    "/api/v1/saml/sso/:tenantId",
    "/api/v1/auth/google/callback",
    "/api/v1/auth/sso/test/callback",
    "/api/v1/auth/sso/:tenantId",
    "/api/v1/auth/google-sheets/callback",
    "/api/v1/auth/airtable/callback",
    "/api/v1/auth/email-verification",
    "/api/v1/auth/email-verification-link",
    "/api/v1/auth/confirmation",
    "/api/v1/auth/reset/:resetPasswordToken",
    "/api/v1/auth/logout",
    "/api/v1/auth/forgot",
    "/api/v1/data/source/:dataSourceId/execute/:operationName",
    "/api/v1/actions/:actionId/execute",
    "/api/v1/cmse/views",
    "/api/v1/authorize/viewer",
    "/api/v1/analytics/:teamId",
    "/api/v1/github/install",
    "/api/v1/copilot-feedback",
    "/api/v1/billing/subscription/:teamId"
}

import re
import sys

def should_remove_withNext(route_path):
    """Check if withNext should be removed from this GET route."""
    # Normalize the route path
    normalized = route_path.strip('"').strip("'")
    
    # Check if this route is NOT in the write list
    return normalized not in WRITE_GET_ROUTES

def process_file(filepath):
    with open(filepath, 'r') as f:
        lines = f.readlines()
    
    modified = False
    for i, line in enumerate(lines):
        # Look for app.get with withNext
        match = re.search(r'app\.get\s*\(\s*["\']([^"\']+)["\'][^,]*,\s*withNext\s*\(([^)]+)\)\s*\)', line)
        if match:
            route_path = match.group(1)
            handler = match.group(2)
            
            if should_remove_withNext(route_path):
                # Remove withNext wrapper
                new_line = re.sub(
                    r'(app\.get\s*\(\s*["\'][^"\']+["\'][^,]*,\s*)withNext\s*\(([^)]+)\)\s*\)',
                    r'\1\2)',
                    line
                )
                if new_line != line:
                    lines[i] = new_line
                    modified = True
                    print(f"Removed withNext from GET {route_path}")
    
    if modified:
        with open(filepath, 'w') as f:
            f.writelines(lines)
        print(f"Modified {filepath}")
    else:
        print(f"No changes needed in {filepath}")

if __name__ == "__main__":
    # Process AppServer.ts
    process_file("/workspace/platform/wab/src/wab/server/AppServer.ts")
    
    # Process custom-routes.ts
    process_file("/workspace/platform/wab/src/wab/server/routes/custom-routes.ts")
    
    # Process comments.ts
    process_file("/workspace/platform/wab/src/wab/server/routes/comments.ts")