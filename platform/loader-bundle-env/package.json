{"name": "loader-bundle-env", "private": true, "dependenciesComments": {"@ant-design/pro-components": "Must be 2.6.4. Earlier doesn't support newer antd, later incurs a disallowed dynamic require of antd/es/layout/Sider. https://app.shortcut.com/plasmic/story/37043/richlayout-always-initially-loads-with-dark-background", "rc-util": "Must be ^5.44.4. Earlier versions doesn't support React 19+ by attempting to use findDOMNode from react-dom. https://linear.app/plasmic/issue/PLA-11800/investigate-issue-with-rich-components-table-in-react-19"}, "scripts": {"postinstall": "patch-package"}, "dependencies": {"@ant-design/icons": "^5.1.4", "@ant-design/pro-components": "2.6.4", "@emotion/react": "^11.10.4", "@emotion/styled": "^11.10.4", "@faker-js/faker": "^8.2.0", "@plasmicapp/auth-react": "^0.0.23", "@plasmicapp/data-sources-context": "^0.1.22", "@plasmicapp/host": "^1.0.224", "@plasmicapp/react-web": "^0.2.399", "@plasmicpkgs/airtable": "^0.0.238", "@plasmicpkgs/antd": "^2.0.146", "@plasmicpkgs/antd5": "^0.0.301", "@plasmicpkgs/commerce": "^0.0.222", "@plasmicpkgs/commerce-commercetools": "^0.0.172", "@plasmicpkgs/commerce-local": "^0.0.222", "@plasmicpkgs/commerce-saleor": "^0.0.186", "@plasmicpkgs/commerce-shopify": "^0.0.230", "@plasmicpkgs/commerce-swell": "^0.0.231", "@plasmicpkgs/fetch": "^0.0.14", "@plasmicpkgs/framer-motion": "^0.0.222", "@plasmicpkgs/lottie-react": "^0.0.216", "@plasmicpkgs/plasmic-basic-components": "^0.0.251", "@plasmicpkgs/plasmic-chakra-ui": "^0.0.54", "@plasmicpkgs/plasmic-cms": "^0.0.289", "@plasmicpkgs/plasmic-content-stack": "^0.0.178", "@plasmicpkgs/plasmic-contentful": "^0.0.166", "@plasmicpkgs/plasmic-embed-css": "^0.1.209", "@plasmicpkgs/plasmic-graphcms": "^0.0.195", "@plasmicpkgs/plasmic-link-preview": "^1.0.121", "@plasmicpkgs/plasmic-nav": "^0.0.194", "@plasmicpkgs/plasmic-query": "^0.0.243", "@plasmicpkgs/plasmic-rich-components": "^1.0.220", "@plasmicpkgs/plasmic-sanity-io": "^1.0.203", "@plasmicpkgs/plasmic-strapi": "^0.1.176", "@plasmicpkgs/plasmic-tabs": "^0.0.65", "@plasmicpkgs/plasmic-wordpress": "^0.0.146", "@plasmicpkgs/plasmic-wordpress-graphql": "^0.0.140", "@plasmicpkgs/radix-ui": "^0.0.82", "@plasmicpkgs/react-aria": "^0.0.150", "@plasmicpkgs/react-awesome-reveal": "^3.8.226", "@plasmicpkgs/react-chartjs-2": "^1.0.134", "@plasmicpkgs/react-parallax-tilt": "^0.0.224", "@plasmicpkgs/react-quill": "^1.0.87", "@plasmicpkgs/react-scroll-parallax": "^0.0.232", "@plasmicpkgs/react-slick": "^0.0.245", "@plasmicpkgs/react-twitter-widgets": "^0.0.222", "@plasmicpkgs/react-youtube": "^7.13.228", "@plasmicpkgs/rive": "^0.0.11", "@plasmicpkgs/tiptap": "^0.0.3", "ant-design-pro-form-stub": "link:./internal_pkgs/ant-design-pro-form-stub", "antd": "^5.7.3", "axios": "^1.5.1", "chart.js": "^4.2.1", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.10", "enquire-js": "link:./internal_pkgs/enquire-js", "fast-stringify": "^2.0.0", "framer-motion": "^7.6.1", "immer": "^10.0.3", "isomorphic-fetch": "^3.0.0", "jquery": "^3.7.1", "lodash": "^4.17.21", "marked": "^9.1.1", "md5": "^2.3.0", "nanoid": "^5.0.2", "papaparse": "^5.4.1", "plasmic-internal-noop-func": "link:./internal_pkgs/noop-func", "pluralize": "^8.0.0", "random": "^4.1.0", "rc-util": "^5.44.4", "react-chartjs-2": "^5.2.0", "semver": "^7.5.4", "slick-carousel": "^1.8.1", "slick-carousel-theme": "link:./internal_pkgs/slick-carousel-theme", "tinycolor2": "^1.6.0", "uuid": "^9.0.1", "zod": "^3.22.4"}, "resolutions": {"@plasmicpkgs/antd/antd": "4.19.3", "@plasmicpkgs/react-slick/antd": "4.19.3", "@plasmicpkgs/antd5/antd": "^5.0.0", "@ant-design/pro-components/antd": "^5.0.0", "@ant-design/pro-table/antd": "^5.0.0", "@ant-design/pro-list/antd": "^5.0.0", "rc-util": "^5.44.4"}, "devDependencies": {"patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0"}}