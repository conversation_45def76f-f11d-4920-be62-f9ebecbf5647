{"name": "react-web-bundle", "dependencies": {"@plasmicapp/data-sources-context": "^0.1.22", "@plasmicapp/react-web": "^0.2.399"}, "scripts": {"build": "NODE_ENV=production rollup -c rollup.config.js", "watch": "rollup -c rollup.config.js --watch"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/preset-react": "^7.14.5", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-commonjs": "^14.0.0", "@rollup/plugin-node-resolve": "^8.4.0", "@rollup/plugin-replace": "^2.3.3", "@rollup/plugin-sucrase": "^3.1.0", "@rollup/plugin-typescript": "^5.0.2", "@types/react": "^16.9.44", "@types/react-dom": "^16.9.8", "rollup": "^2.23.0", "rollup-plugin-postcss": "^3.1.3", "rollup-plugin-string": "^3.0.0", "rollup-plugin-terser": "^6.1.0", "typescript": "^3.9.7"}}