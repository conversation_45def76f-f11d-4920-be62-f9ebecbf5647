import {
  ComponentMeta,
  ComponentRenderData,
  extractPlasmicQueryData,
  initPlasmicLoader,
  PlasmicComponent,
  PlasmicRootProvider,
} from "@plasmicapp/loader-nextjs";
import type { GetStaticPaths, GetStaticProps } from "next";
import Head from "next/head";
import { useRouter } from "next/router";

import { getMetadataForSite } from "api";
import {
  BADGE_PROJECT,
  BADGE_TOKEN,
  FORCE_BADGE,
  HAS_BADGE_PROJECT,
  NOT_FOUND_REVALIDATE_PERIOD,
  PLASMIC_API_HOST,
  PLASMIC_CODEGEN_HOST,
  REVALIDATE_PERIOD,
} from "../../../constants";

async function timed<T>(label: string, f: () => Promise<T>) {
  const start = performance.now();
  try {
    return await f();
  } finally {
    console.log(`TIMED: ${label} took ${performance.now() - start}`);
  }
}

function getPlasmic(projectId: string, token: string) {
  const userProject = {
    id: projectId,
    token: token,
  };
  return initPlasmicLoader({
    // undefined to use codegen.plasmic.app instead
    host: PLASMIC_CODEGEN_HOST || undefined,
    // Always include the badge project so that we can see it in analytics
    // even if we don't show it, the additional bundle size is minimal
    projects: [
      userProject,
      {
        id: BADGE_PROJECT,
        token: BADGE_TOKEN,
      },
    ],
  });
}

/**
 * We generate all routes lazily.
 */
export const getStaticPaths: GetStaticPaths = async () => {
  return {
    paths: [],
    fallback: "blocking",
  };
};

type Props = {
  plasmicData: ComponentRenderData;
  pageMeta: ComponentMeta & {
    params?: Record<string, string>;
  };
  queryCache: Record<string, any>;
  projectId: string;
  token: string;
  showBadge: boolean;
  favicon: { url: string; mimeType?: string } | null;
};

async function reportPlasmicHit(plasmicHit: {
  hit: boolean;
  site: string;
  path: string;
  projects: { projectId: string; version: string }[];
}) {
  const url = new URL(`${PLASMIC_API_HOST}/api/v1/hosting-hit`);
  url.searchParams.set("site", plasmicHit.site);
  url.searchParams.set("path", plasmicHit.path);
  try {
    const body = JSON.stringify(plasmicHit);
    await timed("hosting-hit", () =>
      fetch(url.toString(), {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      })
    );
    console.log("successful hosting-hit", body);
  } catch (error) {
    console.error("failed hosting-hit", error);
    // Continue with rendering; we don't treat this as catastrophic.
  }
}

function isSuspiciousPath(path: string) {
  return (
    path.includes("wp-admin") ||
    path.includes("wp-content") ||
    path.includes("wp-includes") ||
    path.endsWith(".js") ||
    path.endsWith(".php")
  );
}

/**
 * For each page, pre-fetch the data we need to render it
 */
export const getStaticProps: GetStaticProps<Props> = async (context) => {
  const { params } = context;

  if (!params) {
    throw new Error("No path parameters found");
  }

  const { site, catchall } = params;
  // Convert the catchall param into a path string
  const plasmicPath =
    typeof catchall === "string"
      ? catchall
      : Array.isArray(catchall)
      ? `/${catchall.join("/")}`
      : "/";

  if (isSuspiciousPath(plasmicPath)) {
    console.log("Suspicious path", plasmicPath);
    // Don't even bother reporting this hit
    return {
      notFound: true,
      revalidate: false, // never revalidate
    };
  }

  const {
    projectId,
    token,
    showBadge: wantToShowBadge,
    favicon,
    hasAppAuth,
  } = await timed("project-token-for-domain", async () => {
    return getMetadataForSite(site as string);
  });
  // If getMetadataForSite doesn't return a projectId and token,
  // then this domain has probably been deleted.
  // Return 404 so Vercel stops serving it.
  if (!projectId || !token) {
    return {
      notFound: true,
      revalidate: NOT_FOUND_REVALIDATE_PERIOD,
    };
  }

  const showBadge = (HAS_BADGE_PROJECT && wantToShowBadge) || FORCE_BADGE;

  // Fetch project.
  const PLASMIC = getPlasmic(projectId, token);
  const plasmicData = await timed("maybeFetchComponentData", () =>
    PLASMIC.maybeFetchComponentData(
      showBadge
        ? [
            plasmicPath,
            {
              name: "madeInPlasmic",
              projectId: BADGE_PROJECT,
            },
          ]
        : [plasmicPath],
      { deferChunks: true }
    )
  );

  // Report Plasmic hit to our servers (await it later)
  const hitPromise = reportPlasmicHit({
    hit: !!plasmicData,
    site: site as string,
    path: plasmicPath,
    projects:
      plasmicData?.bundle.projects.map((p) => ({
        projectId: p.id,
        version: p.version,
      })) || [],
  });

  // Find page in project.
  const pageMeta = plasmicData?.entryCompMetas[0];

  // Return 404 if project or page is not found.
  if (!pageMeta) {
    return {
      notFound: true,
      revalidate: NOT_FOUND_REVALIDATE_PERIOD,
    };
  }

  // This is a path that Plasmic knows about.

  // Cache the necessary data fetched for the page.
  const queryCache = await timed("extractPlasmicQueryData", async () => {
    if (hasAppAuth) {
      return {};
    }

    return extractPlasmicQueryData(
      <PlasmicRootProvider
        loader={PLASMIC}
        prefetchedData={plasmicData!}
        pageRoute={pageMeta.path}
        pageParams={pageMeta.params}
      >
        <PlasmicComponent component={pageMeta.displayName} />
      </PlasmicRootProvider>
    );
  });

  // Ensure reportPlasmicHit settles, but don't care if it fails.
  try {
    await hitPromise;
  } catch (err) {
    console.error("reportPlasmicHit failed", err);
  }

  // Pass the data in as props.
  return {
    props: {
      plasmicData,
      pageMeta,
      queryCache,
      projectId,
      token,
      showBadge,
      favicon: favicon ?? null,
    },

    // Using incremental static regeneration, we invalidate this page
    // after REVALIDATE_PERIOD.
    //
    // If hits always make it to Plasmic app servers, we wouldn't need this expiry.
    // But sometimes maybe Plasmic app server is down.
    // So we use this as a fallback.
    // You can think of it also as a "retry period" for the hits - if a hit fails, then in the next visit after 5
    // minutes, we'll try another hit.
    //
    // It doesn't have to be a very tight period (like 10 seconds), since we still count on hit-based explicit
    // invalidation as the primary invalidation mechanism rather than expiration. We just accept the risk that
    // occasionally, users whose will get stale pages for a few minutes after they
    // hit Publish. This should be rare since it would require that the page never have been visited.
    //
    // One downside is that for the page's data dependencies, these will be unpredictably refreshed in the background.
    // We don't want users to become dependent on this behavior either.... Hopefully soon we'll have a better system in
    // place.
    revalidate: REVALIDATE_PERIOD,
  };
};

/**
 * Actually render the page!
 */
export default function CatchallPage({
  plasmicData,
  pageMeta,
  queryCache,
  projectId,
  token,
  showBadge,
  favicon,
}: Props) {
  const router = useRouter();
  return (
    // Pass in the data fetched in getStaticProps as prefetchedData
    <PlasmicRootProvider
      loader={getPlasmic(projectId, token)}
      prefetchedData={plasmicData}
      prefetchedQueryData={queryCache}
      pageRoute={pageMeta.path}
      pageParams={pageMeta.params}
      pageQuery={router.query as Record<string, string>}
    >
      {favicon && (
        <Head>
          <link
            key="plasmic-favicon"
            rel="icon"
            type={favicon.mimeType}
            href={favicon.url}
          />
        </Head>
      )}
      <PlasmicComponent component={pageMeta.displayName} />
      {showBadge && (
        <div
          style={{
            position: "fixed",
            bottom: 12,
            right: 12,
            zIndex: "9999999999999999",
          }}
        >
          <PlasmicComponent
            component={"madeInPlasmic"}
            projectId={BADGE_PROJECT}
            componentProps={{ dark: true }}
          />
        </div>
      )}
    </PlasmicRootProvider>
  );
}
