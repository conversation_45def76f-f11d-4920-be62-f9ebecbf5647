/** How long before a 200 page should be revalidated. */
export const REVALIDATE_PERIOD = process.env.REVALIDATE_PERIOD
  ? +process.env.REVALIDATE_PERIOD
  : 600; // default: 10 minutes
/** How long before a 404 page should be revalidated. */
export const NOT_FOUND_REVALIDATE_PERIOD = process.env
  .NOT_FOUND_REVALIDATE_PERIOD
  ? +process.env.NOT_FOUND_REVALIDATE_PERIOD
  : 30; // default: 30 seconds

const DEFAULT_PLASMIC_API_HOST = "https://studio.plasmic.app";
// NEXT_PUBLIC to allow variable to be exposed in browser code
export const PLASMIC_API_HOST =
  process.env.NEXT_PUBLIC_PLASMIC_HOST || DEFAULT_PLASMIC_API_HOST;
export const PLASMIC_CODEGEN_HOST =
  process.env.NEXT_PUBLIC_PLASMIC_HOST || "https://codegen.plasmic.app";
export const BADGE_PROJECT =
  process.env.BADGE_PROJECT || "bR9hw24Z3CaJmESyNv8MvK";
export const BADGE_TOKEN =
  process.env.BADGE_TOKEN ||
  "a0QDcyE5h0ojFYGdj5BV8rgo76rp4be8jlNZNLbGZvdL4y04jSwBh9PpaAWlVAGr7mscUdccfF7z6IKA2Q";
export const FORCE_BADGE = process.env.FORCE_BADGE === "true";
// Normally, the badge can only be shown if we are using the production host,
// which is where the actual badge component project exists.
export const HAS_BADGE_PROJECT = PLASMIC_API_HOST === DEFAULT_PLASMIC_API_HOST;
