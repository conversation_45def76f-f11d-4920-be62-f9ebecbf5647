{"name": "hosting", "private": true, "engines": {"node": ">=22"}, "scripts": {"build": "next build", "dev": "next dev", "format:write": "prettier --write \"**/*.{css,js,json,jsx,ts,tsx}\"", "format": "prettier \"**/*.{css,js,json,jsx,ts,tsx}\"", "lint": "next lint", "start": "next start", "type-check": "tsc"}, "dependencies": {"@plasmicapp/loader-nextjs": "^1.0.431", "@sentry/nextjs": "^7.120.4", "next": "^12.3.7", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/node": "^24.2.1", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "eslint": "8.11.0", "eslint-config-next": "^12.3.7", "prettier": "^2.5.1", "typescript": "^5.9.2"}}