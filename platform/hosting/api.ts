import { PLASMIC_API_HOST } from "./constants";

export async function getMetadataForSite(site: string) {
  const url =
    `${PLASMIC_API_HOST}/api/v1/project-token-for-domain?domain=` +
    encodeURIComponent(site as string);
  const resp = await fetch(url, {
    headers: {
      Accept: "application/json; charset=UTF-8",
    },
  });
  if (resp.ok) {
    return (await resp.json()) as {
      projectId?: string;
      token?: string;
      showBadge: boolean;
      favicon:
        | {
            url: string;
            mimeType?: string;
          }
        | undefined;
      hasAppAuth: boolean;
      allowRobots: boolean;
    };
  } else {
    throw new Error(
      `GET ${url} responded not OK: ${resp.status} ${resp.statusText}`
    );
  }
}
