.root {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: auto;
  justify-content: flex-start;
  align-items: flex-end;
  min-width: 0;
}
.freeBox__ku0Ns {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: auto;
  max-width: 100%;
  column-gap: 4px;
  min-width: 0;
}
.freeBox__zcSyX {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 2px;
  max-width: 100%;
  background: var(--token-Ik3bdE1e1Uy);
  min-width: 0;
}
.freeBoxpasswordStrength__1__zcSyXYcnAi {
  background: var(--token-kjjxGw1qkB3t);
}
.freeBoxpasswordStrength__2__zcSyXXwdro {
  background: var(--token-MqYzqoRgx_vr);
}
.freeBoxpasswordStrength__3__zcSyX5USzc {
  background: var(--token-yNpyEV13BHoo);
}
.freeBoxpasswordStrength__4__zcSyXoY4OQ {
  background: var(--token-oI9RmKl5Rl_y);
}
.freeBox__lx7Wk {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 2px;
  max-width: 100%;
  background: var(--token-Ik3bdE1e1Uy);
  min-width: 0;
}
.freeBoxpasswordStrength__2__lx7WkXwdro {
  background: var(--token-MqYzqoRgx_vr);
}
.freeBoxpasswordStrength__3__lx7Wk5USzc {
  background: var(--token-yNpyEV13BHoo);
}
.freeBoxpasswordStrength__4__lx7WkoY4OQ {
  background: var(--token-oI9RmKl5Rl_y);
}
.freeBox__gI4Sd {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 2px;
  max-width: 100%;
  background: var(--token-Ik3bdE1e1Uy);
  min-width: 0;
}
.freeBoxpasswordStrength__3__gI4Sd5USzc {
  background: var(--token-yNpyEV13BHoo);
}
.freeBoxpasswordStrength__4__gI4SDoY4OQ {
  background: var(--token-oI9RmKl5Rl_y);
}
.freeBox__s6Y6J {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 2px;
  max-width: 100%;
  background: var(--token-Ik3bdE1e1Uy);
  min-width: 0;
}
.freeBoxpasswordStrength__4__s6Y6JoY4OQ {
  background: var(--token-oI9RmKl5Rl_y);
}
.text {
  position: relative;
  width: auto;
  height: auto;
  max-width: 800px;
  color: var(--token-fVn5vRhXJxQ);
  font-size: 10px;
}
