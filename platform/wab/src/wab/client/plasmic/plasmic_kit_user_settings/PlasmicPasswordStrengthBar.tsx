/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: aaggSgVS8yYsAwQffVQB4p
// Component: m5hJqED4tX

import * as React from "react";

import {
  Flex as Flex__,
  SingleChoiceArg,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  hasVariant,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_design_system_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "../PP__plasmickit_settings.module.css"; // plasmic-import: aaggSgVS8yYsAwQffVQB4p/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import sty from "./PlasmicPasswordStrengthBar.module.css"; // plasmic-import: m5hJqED4tX/css

createPlasmicElementProxy;

export type PlasmicPasswordStrengthBar__VariantMembers = {
  passwordStrength: "_0" | "_1" | "_2" | "_3" | "_4";
};
export type PlasmicPasswordStrengthBar__VariantsArgs = {
  passwordStrength?: SingleChoiceArg<"_0" | "_1" | "_2" | "_3" | "_4">;
};
type VariantPropType = keyof PlasmicPasswordStrengthBar__VariantsArgs;
export const PlasmicPasswordStrengthBar__VariantProps =
  new Array<VariantPropType>("passwordStrength");

export type PlasmicPasswordStrengthBar__ArgsType = { password?: string };
type ArgPropType = keyof PlasmicPasswordStrengthBar__ArgsType;
export const PlasmicPasswordStrengthBar__ArgProps = new Array<ArgPropType>(
  "password"
);

export type PlasmicPasswordStrengthBar__OverridesType = {
  root?: Flex__<"div">;
  text?: Flex__<"div">;
};

export interface DefaultPasswordStrengthBarProps {
  password?: string;
  passwordStrength?: SingleChoiceArg<"_0" | "_1" | "_2" | "_3" | "_4">;
  className?: string;
}

const $$ = {};

function PlasmicPasswordStrengthBar__RenderFunc(props: {
  variants: PlasmicPasswordStrengthBar__VariantsArgs;
  args: PlasmicPasswordStrengthBar__ArgsType;
  overrides: PlasmicPasswordStrengthBar__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {
          password: undefined,
        },
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "passwordStrength",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.passwordStrength,
      },
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        sty.root,
        {
          [sty.rootpasswordStrength__1]: hasVariant(
            $state,
            "passwordStrength",
            "_1"
          ),
          [sty.rootpasswordStrength__2]: hasVariant(
            $state,
            "passwordStrength",
            "_2"
          ),
          [sty.rootpasswordStrength__3]: hasVariant(
            $state,
            "passwordStrength",
            "_3"
          ),
        }
      )}
    >
      <div
        className={classNames(projectcss.all, sty.freeBox__ku0Ns, {
          [sty.freeBoxpasswordStrength__1__ku0NsYcnAi]: hasVariant(
            $state,
            "passwordStrength",
            "_1"
          ),
          [sty.freeBoxpasswordStrength__2__ku0NsXwdro]: hasVariant(
            $state,
            "passwordStrength",
            "_2"
          ),
          [sty.freeBoxpasswordStrength__3__ku0Ns5USzc]: hasVariant(
            $state,
            "passwordStrength",
            "_3"
          ),
        })}
      >
        <div
          className={classNames(projectcss.all, sty.freeBox__zcSyX, {
            [sty.freeBoxpasswordStrength__1__zcSyXYcnAi]: hasVariant(
              $state,
              "passwordStrength",
              "_1"
            ),
            [sty.freeBoxpasswordStrength__2__zcSyXXwdro]: hasVariant(
              $state,
              "passwordStrength",
              "_2"
            ),
            [sty.freeBoxpasswordStrength__3__zcSyX5USzc]: hasVariant(
              $state,
              "passwordStrength",
              "_3"
            ),
            [sty.freeBoxpasswordStrength__4__zcSyXoY4OQ]: hasVariant(
              $state,
              "passwordStrength",
              "_4"
            ),
          })}
        />

        <div
          className={classNames(projectcss.all, sty.freeBox__lx7Wk, {
            [sty.freeBoxpasswordStrength__2__lx7WkXwdro]: hasVariant(
              $state,
              "passwordStrength",
              "_2"
            ),
            [sty.freeBoxpasswordStrength__3__lx7Wk5USzc]: hasVariant(
              $state,
              "passwordStrength",
              "_3"
            ),
            [sty.freeBoxpasswordStrength__4__lx7WkoY4OQ]: hasVariant(
              $state,
              "passwordStrength",
              "_4"
            ),
          })}
        />

        <div
          className={classNames(projectcss.all, sty.freeBox__gI4Sd, {
            [sty.freeBoxpasswordStrength__3__gI4Sd5USzc]: hasVariant(
              $state,
              "passwordStrength",
              "_3"
            ),
            [sty.freeBoxpasswordStrength__4__gI4SDoY4OQ]: hasVariant(
              $state,
              "passwordStrength",
              "_4"
            ),
          })}
        />

        <div
          className={classNames(projectcss.all, sty.freeBox__s6Y6J, {
            [sty.freeBoxpasswordStrength__4__s6Y6JoY4OQ]: hasVariant(
              $state,
              "passwordStrength",
              "_4"
            ),
          })}
        />
      </div>
      <div
        data-plasmic-name={"text"}
        data-plasmic-override={overrides.text}
        className={classNames(projectcss.all, projectcss.__wab_text, sty.text, {
          [sty.textpasswordStrength__1]: hasVariant(
            $state,
            "passwordStrength",
            "_1"
          ),
          [sty.textpasswordStrength__2]: hasVariant(
            $state,
            "passwordStrength",
            "_2"
          ),
          [sty.textpasswordStrength__3]: hasVariant(
            $state,
            "passwordStrength",
            "_3"
          ),
          [sty.textpasswordStrength__4]: hasVariant(
            $state,
            "passwordStrength",
            "_4"
          ),
        })}
      >
        {hasVariant($state, "passwordStrength", "_4")
          ? "strong"
          : hasVariant($state, "passwordStrength", "_3")
          ? "good"
          : hasVariant($state, "passwordStrength", "_2")
          ? "okay"
          : hasVariant($state, "passwordStrength", "_1")
          ? "weak"
          : "too short"}
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "text"],
  text: ["text"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  text: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicPasswordStrengthBar__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicPasswordStrengthBar__VariantsArgs;
    args?: PlasmicPasswordStrengthBar__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicPasswordStrengthBar__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicPasswordStrengthBar__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicPasswordStrengthBar__ArgProps,
          internalVariantPropNames: PlasmicPasswordStrengthBar__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicPasswordStrengthBar__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicPasswordStrengthBar";
  } else {
    func.displayName = `PlasmicPasswordStrengthBar.${nodeName}`;
  }
  return func;
}

export const PlasmicPasswordStrengthBar = Object.assign(
  // Top-level PlasmicPasswordStrengthBar renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    text: makeNodeComponent("text"),

    // Metadata about props expected for PlasmicPasswordStrengthBar
    internalVariantProps: PlasmicPasswordStrengthBar__VariantProps,
    internalArgProps: PlasmicPasswordStrengthBar__ArgProps,
  }
);

export default PlasmicPasswordStrengthBar;
/* prettier-ignore-end */
