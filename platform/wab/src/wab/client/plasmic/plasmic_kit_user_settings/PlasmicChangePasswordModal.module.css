.root {
  box-shadow: 0px 8px 32px -8px var(--token-XeFw4MGauXBT),
    0px 8px 20px -16px var(--token-JrjdlBU-a5Ju);
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: auto;
  background: #ffffff;
  justify-content: center;
  row-gap: 16px;
  min-width: 0;
  border-radius: 8px;
  padding: 1.5rem;
}
.text__xFwGm {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.25;
  position: relative;
}
.form {
  display: flex;
  position: relative;
  flex-direction: column;
  row-gap: 16px;
}
.label__z95AF {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  align-content: unset;
  column-gap: 16px;
}
.text__otKoM {
  position: relative;
  white-space: pre;
  width: 128px;
  color: var(--token-0IloF6TmFvF);
  flex-shrink: 0;
}
.svg__pVxSh {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 1.25rem;
  height: 1.25rem;
}
.svg__cIPu7 {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 1.25rem;
  height: 1.25rem;
}
.freeBox__wJrGa {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
}
.label__cfedi {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  align-content: unset;
  column-gap: 16px;
}
.text__nfn3B {
  position: relative;
  white-space: pre;
  width: 128px;
  color: var(--token-0IloF6TmFvF);
  flex-shrink: 0;
}
.svg__lsdOz {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 1.25rem;
  height: 1.25rem;
}
.svg___6Sz0X {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 1.25rem;
  height: 1.25rem;
}
.label__tGq6R {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  align-content: unset;
  column-gap: 16px;
}
.freeBox__ssBqb {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  width: 128px;
  height: 0px;
  max-width: 100%;
  flex-shrink: 0;
}
.passwordStrengthBar:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  margin-top: 4px;
  align-self: flex-end;
}
.label__gCzX2 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  align-content: unset;
  column-gap: 16px;
}
.text__pG1Hs {
  position: relative;
  white-space: pre;
  width: 128px;
  color: var(--token-0IloF6TmFvF);
  flex-shrink: 0;
}
.svg___3BWpE {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 1.25rem;
  height: 1.25rem;
}
.svg__aQd0A {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 1.25rem;
  height: 1.25rem;
}
.submitButton:global(.__wab_instance) {
  position: relative;
}
.svg__lJUqf {
  display: flex;
  position: relative;
  object-fit: cover;
  height: 1em;
}
.text__ftmVc {
  width: auto;
}
.svg__aHNii {
  display: flex;
  position: relative;
  object-fit: cover;
  height: 1em;
}
.slotTargetError {
  color: var(--token-Y2CWh0ci95a);
  font-weight: 500;
  text-align: center;
}
.feedback {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
}
.text__cVxa0 {
  position: relative;
  color: var(--token-0WgitNN_nh);
}
