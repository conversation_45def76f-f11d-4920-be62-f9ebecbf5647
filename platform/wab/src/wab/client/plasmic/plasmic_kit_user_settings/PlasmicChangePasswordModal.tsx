/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: aaggSgVS8yYsAwQffVQB4p
// Component: 61Ev5d6FaD

import * as React from "react";

import {
  Flex as Flex__,
  SingleBooleanChoiceArg,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  hasVariant,
  renderPlasmicSlot,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import PasswordStrengthBar from "../../components/PasswordStrengthBar"; // plasmic-import: m5hJqED4tX/component
import Button from "../../components/widgets/Button"; // plasmic-import: SEF-sRmSoqV5c/component
import Textbox from "../../components/widgets/Textbox"; // plasmic-import: pA22NEzDCsn_/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_design_system_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "../PP__plasmickit_settings.module.css"; // plasmic-import: aaggSgVS8yYsAwQffVQB4p/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import sty from "./PlasmicChangePasswordModal.module.css"; // plasmic-import: 61Ev5d6FaD/css

createPlasmicElementProxy;

export type PlasmicChangePasswordModal__VariantMembers = {
  isFeedback: "isFeedback";
  hasError: "hasError";
};
export type PlasmicChangePasswordModal__VariantsArgs = {
  isFeedback?: SingleBooleanChoiceArg<"isFeedback">;
  hasError?: SingleBooleanChoiceArg<"hasError">;
};
type VariantPropType = keyof PlasmicChangePasswordModal__VariantsArgs;
export const PlasmicChangePasswordModal__VariantProps =
  new Array<VariantPropType>("isFeedback", "hasError");

export type PlasmicChangePasswordModal__ArgsType = { error?: React.ReactNode };
type ArgPropType = keyof PlasmicChangePasswordModal__ArgsType;
export const PlasmicChangePasswordModal__ArgProps = new Array<ArgPropType>(
  "error"
);

export type PlasmicChangePasswordModal__OverridesType = {
  root?: Flex__<"div">;
  form?: Flex__<"form">;
  oldPasswordInput?: Flex__<typeof Textbox>;
  newPasswordInput?: Flex__<typeof Textbox>;
  passwordStrengthBar?: Flex__<typeof PasswordStrengthBar>;
  confirmPasswordInput?: Flex__<typeof Textbox>;
  submitButton?: Flex__<typeof Button>;
  feedback?: Flex__<"div">;
};

export interface DefaultChangePasswordModalProps {
  error?: React.ReactNode;
  isFeedback?: SingleBooleanChoiceArg<"isFeedback">;
  hasError?: SingleBooleanChoiceArg<"hasError">;
  className?: string;
}

const $$ = {};

function PlasmicChangePasswordModal__RenderFunc(props: {
  variants: PlasmicChangePasswordModal__VariantsArgs;
  args: PlasmicChangePasswordModal__ArgsType;
  overrides: PlasmicChangePasswordModal__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "isFeedback",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.isFeedback,
      },
      {
        path: "hasError",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.hasError,
      },
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        sty.root,
        {
          [sty.roothasError]: hasVariant($state, "hasError", "hasError"),
          [sty.rootisFeedback]: hasVariant($state, "isFeedback", "isFeedback"),
        }
      )}
    >
      <div
        className={classNames(
          projectcss.all,
          projectcss.__wab_text,
          sty.text__xFwGm
        )}
      >
        {"Change password"}
      </div>
      {(hasVariant($state, "isFeedback", "isFeedback") ? false : true) ? (
        <form
          data-plasmic-name={"form"}
          data-plasmic-override={overrides.form}
          className={classNames(projectcss.all, sty.form, {
            [sty.formhasError]: hasVariant($state, "hasError", "hasError"),
            [sty.formisFeedback]: hasVariant(
              $state,
              "isFeedback",
              "isFeedback"
            ),
          })}
        >
          <label className={classNames(projectcss.all, sty.label__z95AF)}>
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__otKoM
              )}
            >
              {"Old password"}
            </div>
            <Textbox
              data-plasmic-name={"oldPasswordInput"}
              data-plasmic-override={overrides.oldPasswordInput}
              styleType={["bordered"]}
            />
          </label>
          <div className={classNames(projectcss.all, sty.freeBox__wJrGa)}>
            <label className={classNames(projectcss.all, sty.label__cfedi)}>
              <div
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.text__nfn3B
                )}
              >
                {"New password"}
              </div>
              <Textbox
                data-plasmic-name={"newPasswordInput"}
                data-plasmic-override={overrides.newPasswordInput}
                styleType={["bordered"]}
              />
            </label>
            <label className={classNames(projectcss.all, sty.label__tGq6R)}>
              <div className={classNames(projectcss.all, sty.freeBox__ssBqb)} />

              <PasswordStrengthBar
                data-plasmic-name={"passwordStrengthBar"}
                data-plasmic-override={overrides.passwordStrengthBar}
                className={classNames(
                  "__wab_instance",
                  sty.passwordStrengthBar
                )}
              />
            </label>
          </div>
          <label className={classNames(projectcss.all, sty.label__gCzX2)}>
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__pG1Hs
              )}
            >
              {"Confirm new password"}
            </div>
            <Textbox
              data-plasmic-name={"confirmPasswordInput"}
              data-plasmic-override={overrides.confirmPasswordInput}
              styleType={["bordered"]}
            />
          </label>
          <Button
            data-plasmic-name={"submitButton"}
            data-plasmic-override={overrides.submitButton}
            className={classNames("__wab_instance", sty.submitButton, {
              [sty.submitButtonhasError]: hasVariant(
                $state,
                "hasError",
                "hasError"
              ),
            })}
            type={["primary"]}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__ftmVc
              )}
            >
              {"Change password"}
            </div>
          </Button>
          {(hasVariant($state, "hasError", "hasError") ? true : false)
            ? renderPlasmicSlot({
                defaultContents: (
                  <div
                    className={classNames(
                      projectcss.all,
                      projectcss.__wab_text,
                      sty.text__xa4AX
                    )}
                  >
                    {"The passwords do not match."}
                  </div>
                ),
                value: args.error,
                className: classNames(sty.slotTargetError, {
                  [sty.slotTargetErrorhasError]: hasVariant(
                    $state,
                    "hasError",
                    "hasError"
                  ),
                }),
              })
            : null}
        </form>
      ) : null}
      {(hasVariant($state, "isFeedback", "isFeedback") ? true : false) ? (
        <div
          data-plasmic-name={"feedback"}
          data-plasmic-override={overrides.feedback}
          className={classNames(projectcss.all, sty.feedback, {
            [sty.feedbackisFeedback]: hasVariant(
              $state,
              "isFeedback",
              "isFeedback"
            ),
          })}
        >
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__cVxa0
            )}
          >
            {"Your password has been changed successfully."}
          </div>
        </div>
      ) : null}
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "form",
    "oldPasswordInput",
    "newPasswordInput",
    "passwordStrengthBar",
    "confirmPasswordInput",
    "submitButton",
    "feedback",
  ],
  form: [
    "form",
    "oldPasswordInput",
    "newPasswordInput",
    "passwordStrengthBar",
    "confirmPasswordInput",
    "submitButton",
  ],
  oldPasswordInput: ["oldPasswordInput"],
  newPasswordInput: ["newPasswordInput"],
  passwordStrengthBar: ["passwordStrengthBar"],
  confirmPasswordInput: ["confirmPasswordInput"],
  submitButton: ["submitButton"],
  feedback: ["feedback"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  form: "form";
  oldPasswordInput: typeof Textbox;
  newPasswordInput: typeof Textbox;
  passwordStrengthBar: typeof PasswordStrengthBar;
  confirmPasswordInput: typeof Textbox;
  submitButton: typeof Button;
  feedback: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicChangePasswordModal__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicChangePasswordModal__VariantsArgs;
    args?: PlasmicChangePasswordModal__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicChangePasswordModal__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicChangePasswordModal__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicChangePasswordModal__ArgProps,
          internalVariantPropNames: PlasmicChangePasswordModal__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicChangePasswordModal__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicChangePasswordModal";
  } else {
    func.displayName = `PlasmicChangePasswordModal.${nodeName}`;
  }
  return func;
}

export const PlasmicChangePasswordModal = Object.assign(
  // Top-level PlasmicChangePasswordModal renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    form: makeNodeComponent("form"),
    oldPasswordInput: makeNodeComponent("oldPasswordInput"),
    newPasswordInput: makeNodeComponent("newPasswordInput"),
    passwordStrengthBar: makeNodeComponent("passwordStrengthBar"),
    confirmPasswordInput: makeNodeComponent("confirmPasswordInput"),
    submitButton: makeNodeComponent("submitButton"),
    feedback: makeNodeComponent("feedback"),

    // Metadata about props expected for PlasmicChangePasswordModal
    internalVariantProps: PlasmicChangePasswordModal__VariantProps,
    internalArgProps: PlasmicChangePasswordModal__ArgProps,
  }
);

export default PlasmicChangePasswordModal;
/* prettier-ignore-end */
