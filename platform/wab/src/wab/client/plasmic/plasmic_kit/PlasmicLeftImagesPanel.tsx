/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: aukbrhkegRkQ6KizvhdUPT
// Component: ECu8FUyP0f3

import * as React from "react";

import {
  Flex as Flex__,
  SingleBooleanChoiceArg,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  hasVariant,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import TextWithInfo from "../../components/TextWithInfo"; // plasmic-import: -EsDm7v023/component
import LeftPaneHeader from "../../components/studio/LeftPaneHeader"; // plasmic-import: XLa52PvduIy/component
import LeftSearchPanel from "../../components/studio/LeftSearchPanel"; // plasmic-import: TqAPn0srTq/component
import Button from "../../components/widgets/Button"; // plasmic-import: SEF-sRmSoqV5c/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_design_system_deprecated_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "../PP__plasmickit_left_pane.module.css"; // plasmic-import: aukbrhkegRkQ6KizvhdUPT/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_new_design_system_former_style_controls_css from "../plasmic_kit_style_controls/plasmic_plasmic_kit_styles_pane.module.css"; // plasmic-import: gYEVvAzCcLMHDVPvuYxkFh/projectcss
import sty from "./PlasmicLeftImagesPanel.module.css"; // plasmic-import: ECu8FUyP0f3/css

import ChevronDownSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ChevronDownSvg"; // plasmic-import: xZrB9_0ir/icon
import PlusIcon from "./PlasmicIcon__Plus"; // plasmic-import: -k064DlQ8k8-L/icon

createPlasmicElementProxy;

export type PlasmicLeftImagesPanel__VariantMembers = {
  compact: "compact";
};
export type PlasmicLeftImagesPanel__VariantsArgs = {
  compact?: SingleBooleanChoiceArg<"compact">;
};
type VariantPropType = keyof PlasmicLeftImagesPanel__VariantsArgs;
export const PlasmicLeftImagesPanel__VariantProps = new Array<VariantPropType>(
  "compact"
);

export type PlasmicLeftImagesPanel__ArgsType = {};
type ArgPropType = keyof PlasmicLeftImagesPanel__ArgsType;
export const PlasmicLeftImagesPanel__ArgProps = new Array<ArgPropType>();

export type PlasmicLeftImagesPanel__OverridesType = {
  root?: Flex__<"div">;
  leftSearchPanel?: Flex__<typeof LeftSearchPanel>;
  iconsHeader?: Flex__<typeof LeftPaneHeader>;
  newIconButton?: Flex__<typeof Button>;
  iconInfo?: Flex__<typeof TextWithInfo>;
  iconsContent?: Flex__<"div">;
  imagesHeader?: Flex__<typeof LeftPaneHeader>;
  newImageButton?: Flex__<typeof Button>;
  imageInfo?: Flex__<typeof TextWithInfo>;
  imagesContent?: Flex__<"div">;
};

export interface DefaultLeftImagesPanelProps {
  compact?: SingleBooleanChoiceArg<"compact">;
  className?: string;
}

const $$ = {};

function PlasmicLeftImagesPanel__RenderFunc(props: {
  variants: PlasmicLeftImagesPanel__VariantsArgs;
  args: PlasmicLeftImagesPanel__ArgsType;
  overrides: PlasmicLeftImagesPanel__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "compact",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.compact,
      },
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_deprecated_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        plasmic_plasmic_kit_new_design_system_former_style_controls_css.plasmic_tokens,
        sty.root,
        { [sty.rootcompact]: hasVariant($state, "compact", "compact") }
      )}
    >
      <LeftSearchPanel
        data-plasmic-name={"leftSearchPanel"}
        data-plasmic-override={overrides.leftSearchPanel}
        className={classNames("__wab_instance", sty.leftSearchPanel, {
          [sty.leftSearchPanelcompact]: hasVariant(
            $state,
            "compact",
            "compact"
          ),
        })}
        rightOptions={"filterControls"}
      />

      <LeftPaneHeader
        data-plasmic-name={"iconsHeader"}
        data-plasmic-override={overrides.iconsHeader}
        actions={
          <Button
            data-plasmic-name={"newIconButton"}
            data-plasmic-override={overrides.newIconButton}
            endIcon={
              <ChevronDownSvgIcon
                className={classNames(projectcss.all, sty.svg__tPc16)}
                role={"img"}
              />
            }
            size={hasVariant($state, "compact", "compact") ? "small" : "wide"}
            startIcon={
              <PlusIcon
                className={classNames(projectcss.all, sty.svg__rksSt)}
                role={"img"}
              />
            }
            type={["secondary"]}
            withIcons={["startIcon"]}
          >
            {"New icon"}
          </Button>
        }
        className={classNames("__wab_instance", sty.iconsHeader, {
          [sty.iconsHeadercompact]: hasVariant($state, "compact", "compact"),
        })}
        compact={hasVariant($state, "compact", "compact") ? true : undefined}
        compactTitle={
          <TextWithInfo
            data-plasmic-name={"iconInfo"}
            data-plasmic-override={overrides.iconInfo}
            className={classNames("__wab_instance", sty.iconInfo, {
              [sty.iconInfocompact]: hasVariant($state, "compact", "compact"),
            })}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__k0Tq1,
                {
                  [sty.textcompact__k0Tq1HgWq9]: hasVariant(
                    $state,
                    "compact",
                    "compact"
                  ),
                }
              )}
            >
              {hasVariant($state, "compact", "compact")
                ? "Icons"
                : "Color tokens"}
            </div>
          </TextWithInfo>
        }
        description={
          "Icons are colorable SVGs that you can use throughout your designs."
        }
        expandState={"expanded"}
        title={"Icons"}
      />

      <div
        data-plasmic-name={"iconsContent"}
        data-plasmic-override={overrides.iconsContent}
        className={classNames(projectcss.all, sty.iconsContent, {
          [sty.iconsContentcompact]: hasVariant($state, "compact", "compact"),
        })}
      />

      <LeftPaneHeader
        data-plasmic-name={"imagesHeader"}
        data-plasmic-override={overrides.imagesHeader}
        actions={
          <Button
            data-plasmic-name={"newImageButton"}
            data-plasmic-override={overrides.newImageButton}
            endIcon={
              <ChevronDownSvgIcon
                className={classNames(projectcss.all, sty.svg___0VoGg)}
                role={"img"}
              />
            }
            size={"wide"}
            startIcon={
              <PlusIcon
                className={classNames(projectcss.all, sty.svg___5CEB)}
                role={"img"}
              />
            }
            type={["secondary"]}
            withIcons={["startIcon"]}
          >
            {"New image"}
          </Button>
        }
        className={classNames("__wab_instance", sty.imagesHeader, {
          [sty.imagesHeadercompact]: hasVariant($state, "compact", "compact"),
        })}
        compact={hasVariant($state, "compact", "compact") ? true : undefined}
        compactTitle={
          <TextWithInfo
            data-plasmic-name={"imageInfo"}
            data-plasmic-override={overrides.imageInfo}
            className={classNames("__wab_instance", sty.imageInfo, {
              [sty.imageInfocompact]: hasVariant($state, "compact", "compact"),
            })}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__hnR4J,
                {
                  [sty.textcompact__hnR4JHgWq9]: hasVariant(
                    $state,
                    "compact",
                    "compact"
                  ),
                }
              )}
            >
              {hasVariant($state, "compact", "compact")
                ? "Images"
                : "Color tokens"}
            </div>
          </TextWithInfo>
        }
        description={
          "Images are any PNGs, JPGs, or non-colorable SVGs that you can use throughout your designs as pictures or background images."
        }
        expandState={
          hasVariant($state, "compact", "compact") ? "expanded" : undefined
        }
        title={"Images"}
      />

      <div
        data-plasmic-name={"imagesContent"}
        data-plasmic-override={overrides.imagesContent}
        className={classNames(projectcss.all, sty.imagesContent, {
          [sty.imagesContentcompact]: hasVariant($state, "compact", "compact"),
        })}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "leftSearchPanel",
    "iconsHeader",
    "newIconButton",
    "iconInfo",
    "iconsContent",
    "imagesHeader",
    "newImageButton",
    "imageInfo",
    "imagesContent",
  ],
  leftSearchPanel: ["leftSearchPanel"],
  iconsHeader: ["iconsHeader", "newIconButton", "iconInfo"],
  newIconButton: ["newIconButton"],
  iconInfo: ["iconInfo"],
  iconsContent: ["iconsContent"],
  imagesHeader: ["imagesHeader", "newImageButton", "imageInfo"],
  newImageButton: ["newImageButton"],
  imageInfo: ["imageInfo"],
  imagesContent: ["imagesContent"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  leftSearchPanel: typeof LeftSearchPanel;
  iconsHeader: typeof LeftPaneHeader;
  newIconButton: typeof Button;
  iconInfo: typeof TextWithInfo;
  iconsContent: "div";
  imagesHeader: typeof LeftPaneHeader;
  newImageButton: typeof Button;
  imageInfo: typeof TextWithInfo;
  imagesContent: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicLeftImagesPanel__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicLeftImagesPanel__VariantsArgs;
    args?: PlasmicLeftImagesPanel__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicLeftImagesPanel__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicLeftImagesPanel__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicLeftImagesPanel__ArgProps,
          internalVariantPropNames: PlasmicLeftImagesPanel__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicLeftImagesPanel__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicLeftImagesPanel";
  } else {
    func.displayName = `PlasmicLeftImagesPanel.${nodeName}`;
  }
  return func;
}

export const PlasmicLeftImagesPanel = Object.assign(
  // Top-level PlasmicLeftImagesPanel renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    leftSearchPanel: makeNodeComponent("leftSearchPanel"),
    iconsHeader: makeNodeComponent("iconsHeader"),
    newIconButton: makeNodeComponent("newIconButton"),
    iconInfo: makeNodeComponent("iconInfo"),
    iconsContent: makeNodeComponent("iconsContent"),
    imagesHeader: makeNodeComponent("imagesHeader"),
    newImageButton: makeNodeComponent("newImageButton"),
    imageInfo: makeNodeComponent("imageInfo"),
    imagesContent: makeNodeComponent("imagesContent"),

    // Metadata about props expected for PlasmicLeftImagesPanel
    internalVariantProps: PlasmicLeftImagesPanel__VariantProps,
    internalArgProps: PlasmicLeftImagesPanel__ArgProps,
  }
);

export default PlasmicLeftImagesPanel;
/* prettier-ignore-end */
