.root {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
  flex-direction: column;
  overflow: hidden;
  background: #ffffff;
  min-width: 0;
  min-height: 0;
}
.leftSearchPanel:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
}
.leftSearchPanelstate_refreshing:global(.__wab_instance) {
  flex-shrink: 0;
}
.importsHeader:global(.__wab_instance) {
  position: relative;
}
.freeBox {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  flex-wrap: wrap;
  column-gap: 4px;
  row-gap: 4px;
  min-width: 0;
}
.svg__iQt2K {
  display: flex;
  position: relative;
  object-fit: cover;
  max-width: 100%;
  max-height: 100%;
  width: 16px;
  height: 16px;
}
.svg__loisF {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 16px;
  height: 1em;
}
.svg___8RJy {
  display: flex;
  position: relative;
  object-fit: cover;
  max-width: 100%;
  max-height: 100%;
  width: 16px;
  height: 16px;
}
.svg__ehFcE {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 16px;
  height: 1em;
}
.updateButtonwithUpdateAll:global(.__wab_instance) {
  display: flex;
}
.svg__elSwl {
  display: flex;
  position: relative;
  object-fit: cover;
  max-width: 100%;
  max-height: 100%;
  width: 16px;
  height: 16px;
}
.svg__jX552 {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 16px;
  height: 1em;
}
.textWithInfo__tdlcr:global(.__wab_instance) {
  max-width: 100%;
}
.text__xVuFc {
  font-weight: 600;
  font-size: 14px;
}
.content {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  flex-grow: 1;
  overflow: hidden;
  min-width: 0;
  min-height: 0;
}
