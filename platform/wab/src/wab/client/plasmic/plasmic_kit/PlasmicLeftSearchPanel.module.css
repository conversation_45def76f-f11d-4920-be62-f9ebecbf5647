.searchPanel {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  width: 100%;
  height: 50px;
  column-gap: 4px;
  min-width: 0;
  padding: 0px 8px;
  border-bottom: 1px solid var(--token-hoA5qaM-91G);
}
.searchbox:global(.__wab_instance):global(.__wab_instance) {
  position: relative;
}
.freeBox {
  display: flex;
  flex-direction: row;
  position: relative;
  align-items: center;
  justify-content: space-between;
  width: auto;
  flex-shrink: 0;
  column-gap: 3px;
}
.expandButton:global(.__wab_instance) {
  display: none;
}
.expandButtonrightOptions_groupingControls:global(.__wab_instance) {
  display: flex;
}
.svg__pHvlx {
  display: flex;
  position: relative;
  object-fit: cover;
  height: 1em;
}
.svg__uzldM {
  object-fit: cover;
  max-width: 100%;
  width: 20px;
  height: 24px;
  display: none;
}
.svgrightOptions_groupingControls__uzldMwWi {
  display: block;
}
.svg___0R83 {
  display: flex;
  position: relative;
  object-fit: cover;
  height: 1em;
}
.collapseButtonrightOptions_groupingControls:global(.__wab_instance) {
  display: flex;
}
.svg__stOqr {
  display: flex;
  position: relative;
  object-fit: cover;
  height: 1em;
}
.svg___65HqT {
  object-fit: cover;
  max-width: 100%;
  width: 20px;
  height: 24px;
}
.svg__zxCn1 {
  display: flex;
  position: relative;
  object-fit: cover;
  height: 1em;
}
.filterButton:global(.__wab_instance):global(.__wab_instance) {
  position: relative;
}
.filterButtonrightOptions_filterControls:global(.__wab_instance):global(
    .__wab_instance
  ) {
  display: block;
}
