/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: aukbrhkegRkQ6KizvhdUPT
// Component: ZsFxxgE4E8

import * as React from "react";

import {
  Flex as Flex__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import LeftPaneHeader from "../../components/studio/LeftPaneHeader"; // plasmic-import: XLa52PvduIy/component
import LeftSearchPanel from "../../components/studio/LeftSearchPanel"; // plasmic-import: TqAPn0srTq/component
import Button from "../../components/widgets/Button"; // plasmic-import: SEF-sRmSoqV5c/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_design_system_deprecated_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "../PP__plasmickit_left_pane.module.css"; // plasmic-import: aukbrhkegRkQ6KizvhdUPT/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_new_design_system_former_style_controls_css from "../plasmic_kit_style_controls/plasmic_plasmic_kit_styles_pane.module.css"; // plasmic-import: gYEVvAzCcLMHDVPvuYxkFh/projectcss
import sty from "./PlasmicLeftMixinsPanel.module.css"; // plasmic-import: ZsFxxgE4E8/css

import ChevronDownSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ChevronDownSvg"; // plasmic-import: xZrB9_0ir/icon
import PlusIcon from "./PlasmicIcon__Plus"; // plasmic-import: -k064DlQ8k8-L/icon

createPlasmicElementProxy;

export type PlasmicLeftMixinsPanel__VariantMembers = {};
export type PlasmicLeftMixinsPanel__VariantsArgs = {};
type VariantPropType = keyof PlasmicLeftMixinsPanel__VariantsArgs;
export const PlasmicLeftMixinsPanel__VariantProps =
  new Array<VariantPropType>();

export type PlasmicLeftMixinsPanel__ArgsType = {};
type ArgPropType = keyof PlasmicLeftMixinsPanel__ArgsType;
export const PlasmicLeftMixinsPanel__ArgProps = new Array<ArgPropType>();

export type PlasmicLeftMixinsPanel__OverridesType = {
  root?: Flex__<"div">;
  leftSearchPanel?: Flex__<typeof LeftSearchPanel>;
  mixinsHeader?: Flex__<typeof LeftPaneHeader>;
  newMixinButton?: Flex__<typeof Button>;
  content?: Flex__<"div">;
};

export interface DefaultLeftMixinsPanelProps {
  className?: string;
}

const $$ = {};

function PlasmicLeftMixinsPanel__RenderFunc(props: {
  variants: PlasmicLeftMixinsPanel__VariantsArgs;
  args: PlasmicLeftMixinsPanel__ArgsType;
  overrides: PlasmicLeftMixinsPanel__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_deprecated_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        plasmic_plasmic_kit_new_design_system_former_style_controls_css.plasmic_tokens,
        sty.root
      )}
    >
      <LeftSearchPanel
        data-plasmic-name={"leftSearchPanel"}
        data-plasmic-override={overrides.leftSearchPanel}
        className={classNames("__wab_instance", sty.leftSearchPanel)}
        rightOptions={"filterControls"}
      />

      <LeftPaneHeader
        data-plasmic-name={"mixinsHeader"}
        data-plasmic-override={overrides.mixinsHeader}
        actions={
          <Button
            data-plasmic-name={"newMixinButton"}
            data-plasmic-override={overrides.newMixinButton}
            endIcon={
              <ChevronDownSvgIcon
                className={classNames(projectcss.all, sty.svg__h3UK)}
                role={"img"}
              />
            }
            size={"wide"}
            startIcon={
              <PlusIcon
                className={classNames(projectcss.all, sty.svg__yD01M)}
                role={"img"}
              />
            }
            type={["secondary"]}
            withIcons={["startIcon"]}
          >
            {"New style preset"}
          </Button>
        }
        className={classNames("__wab_instance", sty.mixinsHeader)}
        description={
          "Style presets are bundles of styles that you can apply to any element. You might create style presets to define consistent styles for typography, backgrounds, shadows, layout, and more."
        }
        title={"Style presets"}
      />

      <div
        data-plasmic-name={"content"}
        data-plasmic-override={overrides.content}
        className={classNames(projectcss.all, sty.content)}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "leftSearchPanel",
    "mixinsHeader",
    "newMixinButton",
    "content",
  ],
  leftSearchPanel: ["leftSearchPanel"],
  mixinsHeader: ["mixinsHeader", "newMixinButton"],
  newMixinButton: ["newMixinButton"],
  content: ["content"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  leftSearchPanel: typeof LeftSearchPanel;
  mixinsHeader: typeof LeftPaneHeader;
  newMixinButton: typeof Button;
  content: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicLeftMixinsPanel__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicLeftMixinsPanel__VariantsArgs;
    args?: PlasmicLeftMixinsPanel__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicLeftMixinsPanel__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicLeftMixinsPanel__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicLeftMixinsPanel__ArgProps,
          internalVariantPropNames: PlasmicLeftMixinsPanel__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicLeftMixinsPanel__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicLeftMixinsPanel";
  } else {
    func.displayName = `PlasmicLeftMixinsPanel.${nodeName}`;
  }
  return func;
}

export const PlasmicLeftMixinsPanel = Object.assign(
  // Top-level PlasmicLeftMixinsPanel renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    leftSearchPanel: makeNodeComponent("leftSearchPanel"),
    mixinsHeader: makeNodeComponent("mixinsHeader"),
    newMixinButton: makeNodeComponent("newMixinButton"),
    content: makeNodeComponent("content"),

    // Metadata about props expected for PlasmicLeftMixinsPanel
    internalVariantProps: PlasmicLeftMixinsPanel__VariantProps,
    internalArgProps: PlasmicLeftMixinsPanel__ArgProps,
  }
);

export default PlasmicLeftMixinsPanel;
/* prettier-ignore-end */
