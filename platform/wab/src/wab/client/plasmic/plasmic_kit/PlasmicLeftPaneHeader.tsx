/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: aukbrhkegRkQ6KizvhdUPT
// Component: XLa52PvduIy

import * as React from "react";

import {
  Flex as Flex__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  hasVariant,
  renderPlasmicSlot,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import TextWithInfo from "../../components/TextWithInfo"; // plasmic-import: -EsDm7v023/component
import But<PERSON> from "../../components/widgets/Button"; // plasmic-import: SEF-sRmSoqV5c/component
import ExpandButton from "../../components/widgets/ExpandButton"; // plasmic-import: JJhv0MV9DH/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_design_system_deprecated_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "../PP__plasmickit_left_pane.module.css"; // plasmic-import: aukbrhkegRkQ6KizvhdUPT/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_new_design_system_former_style_controls_css from "../plasmic_kit_style_controls/plasmic_plasmic_kit_styles_pane.module.css"; // plasmic-import: gYEVvAzCcLMHDVPvuYxkFh/projectcss
import sty from "./PlasmicLeftPaneHeader.module.css"; // plasmic-import: XLa52PvduIy/css

import ChevronDownSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ChevronDownSvg"; // plasmic-import: xZrB9_0ir/icon
import PlusIcon from "./PlasmicIcon__Plus"; // plasmic-import: -k064DlQ8k8-L/icon

createPlasmicElementProxy;

export type PlasmicLeftPaneHeader__VariantMembers = {
  noActions: "noActions";
  showAlert: "showAlert";
  compact: "compact";
  expandState: "expanded" | "collapsed";
  hasTitleActions: "hasTitleActions";
  noDescription: "noDescription";
};
export type PlasmicLeftPaneHeader__VariantsArgs = {
  noActions?: SingleBooleanChoiceArg<"noActions">;
  showAlert?: SingleBooleanChoiceArg<"showAlert">;
  compact?: SingleBooleanChoiceArg<"compact">;
  expandState?: SingleChoiceArg<"expanded" | "collapsed">;
  hasTitleActions?: SingleBooleanChoiceArg<"hasTitleActions">;
  noDescription?: SingleBooleanChoiceArg<"noDescription">;
};
type VariantPropType = keyof PlasmicLeftPaneHeader__VariantsArgs;
export const PlasmicLeftPaneHeader__VariantProps = new Array<VariantPropType>(
  "noActions",
  "showAlert",
  "compact",
  "expandState",
  "hasTitleActions",
  "noDescription"
);

export type PlasmicLeftPaneHeader__ArgsType = {
  title?: React.ReactNode;
  description?: React.ReactNode;
  actions?: React.ReactNode;
  alert?: React.ReactNode;
  compactTitle?: React.ReactNode;
  titleActions?: React.ReactNode;
};
type ArgPropType = keyof PlasmicLeftPaneHeader__ArgsType;
export const PlasmicLeftPaneHeader__ArgProps = new Array<ArgPropType>(
  "title",
  "description",
  "actions",
  "alert",
  "compactTitle",
  "titleActions"
);

export type PlasmicLeftPaneHeader__OverridesType = {
  header?: Flex__<"div">;
  titleContainer?: Flex__<"div">;
  titleActionsContainer?: Flex__<"div">;
  expandButton?: Flex__<typeof ExpandButton>;
  descriptionContainer?: Flex__<"div">;
  alertContainer?: Flex__<"div">;
  actionsContainer?: Flex__<"div">;
  expandButton2?: Flex__<typeof ExpandButton>;
};

export interface DefaultLeftPaneHeaderProps {
  title?: React.ReactNode;
  description?: React.ReactNode;
  actions?: React.ReactNode;
  alert?: React.ReactNode;
  compactTitle?: React.ReactNode;
  titleActions?: React.ReactNode;
  noActions?: SingleBooleanChoiceArg<"noActions">;
  showAlert?: SingleBooleanChoiceArg<"showAlert">;
  compact?: SingleBooleanChoiceArg<"compact">;
  expandState?: SingleChoiceArg<"expanded" | "collapsed">;
  hasTitleActions?: SingleBooleanChoiceArg<"hasTitleActions">;
  noDescription?: SingleBooleanChoiceArg<"noDescription">;
  className?: string;
}

const $$ = {};

function PlasmicLeftPaneHeader__RenderFunc(props: {
  variants: PlasmicLeftPaneHeader__VariantsArgs;
  args: PlasmicLeftPaneHeader__ArgsType;
  overrides: PlasmicLeftPaneHeader__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "noActions",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.noActions,
      },
      {
        path: "showAlert",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.showAlert,
      },
      {
        path: "compact",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.compact,
      },
      {
        path: "expandState",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.expandState,
      },
      {
        path: "hasTitleActions",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.hasTitleActions,
      },
      {
        path: "noDescription",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.noDescription,
      },
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <div
      data-plasmic-name={"header"}
      data-plasmic-override={overrides.header}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_deprecated_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        plasmic_plasmic_kit_new_design_system_former_style_controls_css.plasmic_tokens,
        sty.header,
        {
          [sty.headercompact]: hasVariant($state, "compact", "compact"),
          [sty.headercompact_expandState_collapsed]:
            hasVariant($state, "compact", "compact") &&
            hasVariant($state, "expandState", "collapsed"),
          [sty.headerexpandState_collapsed]: hasVariant(
            $state,
            "expandState",
            "collapsed"
          ),
          [sty.headernoActions]: hasVariant($state, "noActions", "noActions"),
          [sty.headershowAlert]: hasVariant($state, "showAlert", "showAlert"),
        }
      )}
    >
      {(hasVariant($state, "compact", "compact") ? false : true) ? (
        <div
          className={classNames(projectcss.all, sty.freeBox__rhIgq, {
            [sty.freeBoxcompact__rhIgqZQlq]: hasVariant(
              $state,
              "compact",
              "compact"
            ),
            [sty.freeBoxshowAlert__rhIgq7K1Nc]: hasVariant(
              $state,
              "showAlert",
              "showAlert"
            ),
          })}
        >
          <div
            className={classNames(projectcss.all, sty.freeBox__f9FwI, {
              [sty.freeBoxcompact__f9FwIzQlq]: hasVariant(
                $state,
                "compact",
                "compact"
              ),
              [sty.freeBoxnoDescription__f9FwIcTtUc]: hasVariant(
                $state,
                "noDescription",
                "noDescription"
              ),
              [sty.freeBoxshowAlert__f9FwI7K1Nc]: hasVariant(
                $state,
                "showAlert",
                "showAlert"
              ),
            })}
          >
            <div
              data-plasmic-name={"titleContainer"}
              data-plasmic-override={overrides.titleContainer}
              className={classNames(projectcss.all, sty.titleContainer, {
                [sty.titleContainercompact]: hasVariant(
                  $state,
                  "compact",
                  "compact"
                ),
                [sty.titleContainerexpandState_collapsed]: hasVariant(
                  $state,
                  "expandState",
                  "collapsed"
                ),
                [sty.titleContainerexpandState_expanded]: hasVariant(
                  $state,
                  "expandState",
                  "expanded"
                ),
                [sty.titleContainershowAlert]: hasVariant(
                  $state,
                  "showAlert",
                  "showAlert"
                ),
              })}
            >
              {renderPlasmicSlot({
                defaultContents: "Color tokens",
                value: args.title,
                className: classNames(sty.slotTargetTitle, {
                  [sty.slotTargetTitlecompact]: hasVariant(
                    $state,
                    "compact",
                    "compact"
                  ),
                  [sty.slotTargetTitleshowAlert]: hasVariant(
                    $state,
                    "showAlert",
                    "showAlert"
                  ),
                }),
              })}
              <div
                data-plasmic-name={"titleActionsContainer"}
                data-plasmic-override={overrides.titleActionsContainer}
                className={classNames(
                  projectcss.all,
                  sty.titleActionsContainer,
                  {
                    [sty.titleActionsContainercompact]: hasVariant(
                      $state,
                      "compact",
                      "compact"
                    ),
                    [sty.titleActionsContainerhasTitleActions]: hasVariant(
                      $state,
                      "hasTitleActions",
                      "hasTitleActions"
                    ),
                  }
                )}
              >
                {renderPlasmicSlot({
                  defaultContents: null,
                  value: args.titleActions,
                })}
              </div>
              {(
                hasVariant($state, "expandState", "collapsed")
                  ? true
                  : hasVariant($state, "expandState", "expanded")
                  ? true
                  : false
              ) ? (
                <ExpandButton
                  data-plasmic-name={"expandButton"}
                  data-plasmic-override={overrides.expandButton}
                  className={classNames("__wab_instance", sty.expandButton, {
                    [sty.expandButtonexpandState_collapsed]: hasVariant(
                      $state,
                      "expandState",
                      "collapsed"
                    ),
                    [sty.expandButtonexpandState_expanded]: hasVariant(
                      $state,
                      "expandState",
                      "expanded"
                    ),
                  })}
                  isExpanded={
                    hasVariant($state, "expandState", "expanded")
                      ? true
                      : undefined
                  }
                />
              ) : null}
            </div>
            {(
              hasVariant($state, "noDescription", "noDescription")
                ? false
                : hasVariant($state, "expandState", "collapsed")
                ? true
                : hasVariant($state, "compact", "compact")
                ? true
                : true
            ) ? (
              <div
                data-plasmic-name={"descriptionContainer"}
                data-plasmic-override={overrides.descriptionContainer}
                className={classNames(
                  projectcss.all,
                  sty.descriptionContainer,
                  {
                    [sty.descriptionContainercompact]: hasVariant(
                      $state,
                      "compact",
                      "compact"
                    ),
                    [sty.descriptionContainerexpandState_collapsed]: hasVariant(
                      $state,
                      "expandState",
                      "collapsed"
                    ),
                    [sty.descriptionContainernoDescription]: hasVariant(
                      $state,
                      "noDescription",
                      "noDescription"
                    ),
                    [sty.descriptionContainershowAlert]: hasVariant(
                      $state,
                      "showAlert",
                      "showAlert"
                    ),
                  }
                )}
              >
                {renderPlasmicSlot({
                  defaultContents:
                    "Color tokens are reusable color values that you can name and apply anywhere there you have a color picker (fill color, text color, etc.).  You can even define color tokens in terms of other color tokens.",
                  value: args.description,
                })}
              </div>
            ) : null}
          </div>
          {(
            hasVariant($state, "compact", "compact")
              ? true
              : hasVariant($state, "showAlert", "showAlert")
              ? true
              : false
          ) ? (
            <div
              data-plasmic-name={"alertContainer"}
              data-plasmic-override={overrides.alertContainer}
              className={classNames(projectcss.all, sty.alertContainer, {
                [sty.alertContainercompact]: hasVariant(
                  $state,
                  "compact",
                  "compact"
                ),
                [sty.alertContainershowAlert]: hasVariant(
                  $state,
                  "showAlert",
                  "showAlert"
                ),
              })}
            >
              {renderPlasmicSlot({
                defaultContents: "Be aware of something. [Dismiss]",
                value: args.alert,
                className: classNames(sty.slotTargetAlert, {
                  [sty.slotTargetAlertshowAlert]: hasVariant(
                    $state,
                    "showAlert",
                    "showAlert"
                  ),
                }),
              })}
            </div>
          ) : null}
        </div>
      ) : null}
      {(
        hasVariant($state, "compact", "compact") &&
        hasVariant($state, "expandState", "collapsed")
          ? true
          : hasVariant($state, "expandState", "collapsed")
          ? true
          : hasVariant($state, "noActions", "noActions")
          ? false
          : true
      ) ? (
        <div
          data-plasmic-name={"actionsContainer"}
          data-plasmic-override={overrides.actionsContainer}
          className={classNames(projectcss.all, sty.actionsContainer, {
            [sty.actionsContainercompact]: hasVariant(
              $state,
              "compact",
              "compact"
            ),
            [sty.actionsContainercompact_expandState_collapsed]:
              hasVariant($state, "compact", "compact") &&
              hasVariant($state, "expandState", "collapsed"),
            [sty.actionsContainerexpandState_collapsed]: hasVariant(
              $state,
              "expandState",
              "collapsed"
            ),
            [sty.actionsContainernoActions]: hasVariant(
              $state,
              "noActions",
              "noActions"
            ),
            [sty.actionsContainernoDescription]: hasVariant(
              $state,
              "noDescription",
              "noDescription"
            ),
            [sty.actionsContainershowAlert]: hasVariant(
              $state,
              "showAlert",
              "showAlert"
            ),
          })}
        >
          {(hasVariant($state, "compact", "compact") ? true : false)
            ? renderPlasmicSlot({
                defaultContents: (
                  <TextWithInfo
                    className={classNames(
                      "__wab_instance",
                      sty.textWithInfo___3D2OQ
                    )}
                  >
                    <div
                      className={classNames(
                        projectcss.all,
                        projectcss.__wab_text,
                        sty.text__loEe2
                      )}
                    >
                      {"Color tokens"}
                    </div>
                  </TextWithInfo>
                ),
                value: args.compactTitle,
              })
            : null}
          <div className={classNames(projectcss.all, sty.freeBox___1SjU4)}>
            {renderPlasmicSlot({
              defaultContents: (
                <Button
                  endIcon={
                    <ChevronDownSvgIcon
                      className={classNames(projectcss.all, sty.svg__ne1Zy)}
                      role={"img"}
                    />
                  }
                  size={"wide"}
                  startIcon={
                    <PlusIcon
                      className={classNames(projectcss.all, sty.svg__xTd68)}
                      role={"img"}
                    />
                  }
                  type={["secondary"]}
                  withIcons={["startIcon"]}
                >
                  {"New token"}
                </Button>
              ),
              value: args.actions,
            })}
            <ExpandButton
              data-plasmic-name={"expandButton2"}
              data-plasmic-override={overrides.expandButton2}
              className={classNames("__wab_instance", sty.expandButton2, {
                [sty.expandButton2compact]: hasVariant(
                  $state,
                  "compact",
                  "compact"
                ),
                [sty.expandButton2compact_expandState_collapsed]:
                  hasVariant($state, "compact", "compact") &&
                  hasVariant($state, "expandState", "collapsed"),
                [sty.expandButton2compact_expandState_expanded]:
                  hasVariant($state, "compact", "compact") &&
                  hasVariant($state, "expandState", "expanded"),
              })}
              isExpanded={
                hasVariant($state, "compact", "compact") &&
                hasVariant($state, "expandState", "expanded")
                  ? true
                  : undefined
              }
              size={"small"}
            />
          </div>
        </div>
      ) : null}
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  header: [
    "header",
    "titleContainer",
    "titleActionsContainer",
    "expandButton",
    "descriptionContainer",
    "alertContainer",
    "actionsContainer",
    "expandButton2",
  ],
  titleContainer: ["titleContainer", "titleActionsContainer", "expandButton"],
  titleActionsContainer: ["titleActionsContainer"],
  expandButton: ["expandButton"],
  descriptionContainer: ["descriptionContainer"],
  alertContainer: ["alertContainer"],
  actionsContainer: ["actionsContainer", "expandButton2"],
  expandButton2: ["expandButton2"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  header: "div";
  titleContainer: "div";
  titleActionsContainer: "div";
  expandButton: typeof ExpandButton;
  descriptionContainer: "div";
  alertContainer: "div";
  actionsContainer: "div";
  expandButton2: typeof ExpandButton;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicLeftPaneHeader__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicLeftPaneHeader__VariantsArgs;
    args?: PlasmicLeftPaneHeader__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicLeftPaneHeader__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicLeftPaneHeader__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicLeftPaneHeader__ArgProps,
          internalVariantPropNames: PlasmicLeftPaneHeader__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicLeftPaneHeader__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "header") {
    func.displayName = "PlasmicLeftPaneHeader";
  } else {
    func.displayName = `PlasmicLeftPaneHeader.${nodeName}`;
  }
  return func;
}

export const PlasmicLeftPaneHeader = Object.assign(
  // Top-level PlasmicLeftPaneHeader renders the root element
  makeNodeComponent("header"),
  {
    // Helper components rendering sub-elements
    titleContainer: makeNodeComponent("titleContainer"),
    titleActionsContainer: makeNodeComponent("titleActionsContainer"),
    expandButton: makeNodeComponent("expandButton"),
    descriptionContainer: makeNodeComponent("descriptionContainer"),
    alertContainer: makeNodeComponent("alertContainer"),
    actionsContainer: makeNodeComponent("actionsContainer"),
    expandButton2: makeNodeComponent("expandButton2"),

    // Metadata about props expected for PlasmicLeftPaneHeader
    internalVariantProps: PlasmicLeftPaneHeader__VariantProps,
    internalArgProps: PlasmicLeftPaneHeader__ArgProps,
  }
);

export default PlasmicLeftPaneHeader;
/* prettier-ignore-end */
