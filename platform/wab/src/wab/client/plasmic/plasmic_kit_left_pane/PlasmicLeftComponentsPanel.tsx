/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: aukbrhkegRkQ6KizvhdUPT
// Component: 7Wsvgu6cRd

import * as React from "react";

import {
  Flex as Flex__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import LeftPaneHeader from "../../components/studio/LeftPaneHeader"; // plasmic-import: XLa52PvduIy/component
import LeftSearchPanel from "../../components/studio/LeftSearchPanel"; // plasmic-import: TqAPn0srTq/component
import Button from "../../components/widgets/Button"; // plasmic-import: SEF-sRmSoqV5c/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_design_system_deprecated_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "../PP__plasmickit_left_pane.module.css"; // plasmic-import: aukbrhkegRkQ6KizvhdUPT/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_new_design_system_former_style_controls_css from "../plasmic_kit_style_controls/plasmic_plasmic_kit_styles_pane.module.css"; // plasmic-import: gYEVvAzCcLMHDVPvuYxkFh/projectcss
import sty from "./PlasmicLeftComponentsPanel.module.css"; // plasmic-import: 7Wsvgu6cRd/css

import PlusIcon from "../plasmic_kit/PlasmicIcon__Plus"; // plasmic-import: -k064DlQ8k8-L/icon
import ChevronDownSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ChevronDownSvg"; // plasmic-import: xZrB9_0ir/icon

createPlasmicElementProxy;

export type PlasmicLeftComponentsPanel__VariantMembers = {};
export type PlasmicLeftComponentsPanel__VariantsArgs = {};
type VariantPropType = keyof PlasmicLeftComponentsPanel__VariantsArgs;
export const PlasmicLeftComponentsPanel__VariantProps =
  new Array<VariantPropType>();

export type PlasmicLeftComponentsPanel__ArgsType = {};
type ArgPropType = keyof PlasmicLeftComponentsPanel__ArgsType;
export const PlasmicLeftComponentsPanel__ArgProps = new Array<ArgPropType>();

export type PlasmicLeftComponentsPanel__OverridesType = {
  root?: Flex__<"div">;
  leftSearchPanel?: Flex__<typeof LeftSearchPanel>;
  leftPaneHeader?: Flex__<typeof LeftPaneHeader>;
  newComponentButton?: Flex__<typeof Button>;
  content?: Flex__<"div">;
};

export interface DefaultLeftComponentsPanelProps {
  className?: string;
}

const $$ = {};

function PlasmicLeftComponentsPanel__RenderFunc(props: {
  variants: PlasmicLeftComponentsPanel__VariantsArgs;
  args: PlasmicLeftComponentsPanel__ArgsType;
  overrides: PlasmicLeftComponentsPanel__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_deprecated_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        plasmic_plasmic_kit_new_design_system_former_style_controls_css.plasmic_tokens,
        sty.root
      )}
    >
      <LeftSearchPanel
        data-plasmic-name={"leftSearchPanel"}
        data-plasmic-override={overrides.leftSearchPanel}
        className={classNames("__wab_instance", sty.leftSearchPanel)}
      />

      <LeftPaneHeader
        data-plasmic-name={"leftPaneHeader"}
        data-plasmic-override={overrides.leftPaneHeader}
        actions={
          <Button
            data-plasmic-name={"newComponentButton"}
            data-plasmic-override={overrides.newComponentButton}
            endIcon={
              <ChevronDownSvgIcon
                className={classNames(projectcss.all, sty.svg__fs072)}
                role={"img"}
              />
            }
            size={"wide"}
            startIcon={
              <PlusIcon
                className={classNames(projectcss.all, sty.svg__ao8JE)}
                role={"img"}
              />
            }
            type={["secondary"]}
            withIcons={["startIcon"]}
          >
            {"New component"}
          </Button>
        }
        className={classNames("__wab_instance", sty.leftPaneHeader)}
        description={
          "Components are chunks of UI you can reuse. Instances of a component can be customized with different props, variants or slot contents."
        }
        title={"Components"}
      />

      <div
        data-plasmic-name={"content"}
        data-plasmic-override={overrides.content}
        className={classNames(projectcss.all, sty.content)}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "leftSearchPanel",
    "leftPaneHeader",
    "newComponentButton",
    "content",
  ],
  leftSearchPanel: ["leftSearchPanel"],
  leftPaneHeader: ["leftPaneHeader", "newComponentButton"],
  newComponentButton: ["newComponentButton"],
  content: ["content"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  leftSearchPanel: typeof LeftSearchPanel;
  leftPaneHeader: typeof LeftPaneHeader;
  newComponentButton: typeof Button;
  content: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicLeftComponentsPanel__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicLeftComponentsPanel__VariantsArgs;
    args?: PlasmicLeftComponentsPanel__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicLeftComponentsPanel__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicLeftComponentsPanel__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicLeftComponentsPanel__ArgProps,
          internalVariantPropNames: PlasmicLeftComponentsPanel__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicLeftComponentsPanel__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicLeftComponentsPanel";
  } else {
    func.displayName = `PlasmicLeftComponentsPanel.${nodeName}`;
  }
  return func;
}

export const PlasmicLeftComponentsPanel = Object.assign(
  // Top-level PlasmicLeftComponentsPanel renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    leftSearchPanel: makeNodeComponent("leftSearchPanel"),
    leftPaneHeader: makeNodeComponent("leftPaneHeader"),
    newComponentButton: makeNodeComponent("newComponentButton"),
    content: makeNodeComponent("content"),

    // Metadata about props expected for PlasmicLeftComponentsPanel
    internalVariantProps: PlasmicLeftComponentsPanel__VariantProps,
    internalArgProps: PlasmicLeftComponentsPanel__ArgProps,
  }
);

export default PlasmicLeftComponentsPanel;
/* prettier-ignore-end */
