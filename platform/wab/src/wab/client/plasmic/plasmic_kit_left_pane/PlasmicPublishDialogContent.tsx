/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: aukbrhkegRkQ6KizvhdUPT
// Component: V25hk8i--ck

import * as React from "react";

import {
  Flex as Flex__,
  PlasmicImg as PlasmicImg__,
  SingleChoiceArg,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  hasVariant,
  renderPlasmicSlot,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import Button from "../../components/widgets/Button"; // plasmic-import: SEF-sRmSoqV5c/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_design_system_deprecated_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "../PP__plasmickit_left_pane.module.css"; // plasmic-import: aukbrhkegRkQ6KizvhdUPT/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_new_design_system_former_style_controls_css from "../plasmic_kit_style_controls/plasmic_plasmic_kit_styles_pane.module.css"; // plasmic-import: gYEVvAzCcLMHDVPvuYxkFh/projectcss
import sty from "./PlasmicPublishDialogContent.module.css"; // plasmic-import: V25hk8i--ck/css

import TrashIcon from "../plasmic_kit/PlasmicIcon__Trash"; // plasmic-import: 7bxap5bzcUODa/icon
import ChevronDownSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ChevronDownSvg"; // plasmic-import: xZrB9_0ir/icon

createPlasmicElementProxy;

export type PlasmicPublishDialogContent__VariantMembers = {
  state: "disabled" | "nochanges";
};
export type PlasmicPublishDialogContent__VariantsArgs = {
  state?: SingleChoiceArg<"disabled" | "nochanges">;
};
type VariantPropType = keyof PlasmicPublishDialogContent__VariantsArgs;
export const PlasmicPublishDialogContent__VariantProps =
  new Array<VariantPropType>("state");

export type PlasmicPublishDialogContent__ArgsType = {
  versionNumber?: React.ReactNode;
};
type ArgPropType = keyof PlasmicPublishDialogContent__ArgsType;
export const PlasmicPublishDialogContent__ArgProps = new Array<ArgPropType>(
  "versionNumber"
);

export type PlasmicPublishDialogContent__OverridesType = {
  root?: Flex__<"div">;
  closeButton?: Flex__<typeof PlasmicImg__>;
  hint?: Flex__<"div">;
  title?: Flex__<"input">;
  publishHint?: Flex__<"div">;
  openButton?: Flex__<typeof Button>;
  img?: Flex__<typeof PlasmicImg__>;
  publishButton?: Flex__<typeof Button>;
};

export interface DefaultPublishDialogContentProps {
  versionNumber?: React.ReactNode;
  state?: SingleChoiceArg<"disabled" | "nochanges">;
  className?: string;
}

const $$ = {};

function PlasmicPublishDialogContent__RenderFunc(props: {
  variants: PlasmicPublishDialogContent__VariantsArgs;
  args: PlasmicPublishDialogContent__ArgsType;
  overrides: PlasmicPublishDialogContent__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "state",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.state,
      },
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_deprecated_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        plasmic_plasmic_kit_new_design_system_former_style_controls_css.plasmic_tokens,
        sty.root,
        {
          [sty.rootstate_disabled]: hasVariant($state, "state", "disabled"),
          [sty.rootstate_nochanges]: hasVariant($state, "state", "nochanges"),
        }
      )}
    >
      {false ? (
        <div
          className={classNames(projectcss.all, sty.freeBox__qwLxK, {
            [sty.freeBoxstate_disabled__qwLxKc5URt]: hasVariant(
              $state,
              "state",
              "disabled"
            ),
            [sty.freeBoxstate_nochanges__qwLxKxcr0E]: hasVariant(
              $state,
              "state",
              "nochanges"
            ),
          })}
        >
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__wzTni,
              {
                [sty.textstate_disabled__wzTnic5URt]: hasVariant(
                  $state,
                  "state",
                  "disabled"
                ),
              }
            )}
          >
            {"Publish new version"}
          </div>
          <div
            className={classNames(projectcss.all, sty.freeBox__ad0B7, {
              [sty.freeBoxstate_disabled__ad0B7C5URt]: hasVariant(
                $state,
                "state",
                "disabled"
              ),
            })}
          >
            <PlasmicImg__
              data-plasmic-name={"closeButton"}
              data-plasmic-override={overrides.closeButton}
              alt={""}
              className={classNames(sty.closeButton, {
                [sty.closeButtonstate_disabled]: hasVariant(
                  $state,
                  "state",
                  "disabled"
                ),
              })}
              displayHeight={"16px"}
              displayMaxHeight={"none"}
              displayMaxWidth={"none"}
              displayMinHeight={"0"}
              displayMinWidth={"0"}
              displayWidth={"16px"}
              src={
                "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAxNiAxNiIgaGVpZ2h0PSIxNiIgd2lkdGg9IjE2Ij4KPHBhdGggZmlsbD0iI0JDQzBDNCIgZD0iTTQuOTM1OSAzLjk5MzA2TDMuOTkzMDkgNC45MzU4N0w3LjA1NzIxIDhMMy45OTMwOCAxMS4wNjQxTDQuOTM1ODkgMTIuMDA2OUw4LjAwMDAyIDguOTQyODFMMTEuMDY0MiAxMi4wMDY5TDEyLjAwNyAxMS4wNjQxTDguOTQyODMgOEwxMi4wMDcgNC45MzU4N0wxMS4wNjQyIDMuOTkzMDZMOC4wMDAwMiA3LjA1NzE5TDQuOTM1OSAzLjk5MzA2WiIgY2xpcC1ydWxlPSJldmVub2RkIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiLz4KPC9zdmc+Cg=="
              }
            />
          </div>
        </div>
      ) : null}
      <div
        className={classNames(projectcss.all, sty.freeBox__hspE3, {
          [sty.freeBoxstate_disabled__hspE3C5URt]: hasVariant(
            $state,
            "state",
            "disabled"
          ),
          [sty.freeBoxstate_nochanges__hspE3Xcr0E]: hasVariant(
            $state,
            "state",
            "nochanges"
          ),
        })}
      >
        <div
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text__bitqw,
            {
              [sty.textstate_disabled__bitqwc5URt]: hasVariant(
                $state,
                "state",
                "disabled"
              ),
              [sty.textstate_nochanges__bitqWxcr0E]: hasVariant(
                $state,
                "state",
                "nochanges"
              ),
            }
          )}
        >
          {hasVariant($state, "state", "nochanges")
            ? "There have been no new changes since your last published version."
            : "When you publish a new version, you let other developers and designers know that your components are ready for use."}
        </div>
        {(hasVariant($state, "state", "nochanges") ? false : true) ? (
          <div
            className={classNames(projectcss.all, sty.freeBox__torLq, {
              [sty.freeBoxstate_nochanges__torLqxcr0E]: hasVariant(
                $state,
                "state",
                "nochanges"
              ),
            })}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__pPoTy
              )}
            >
              {"Version number"}
            </div>
            <div className={classNames(projectcss.all, sty.freeBox__epD6O)}>
              <div className={classNames(projectcss.all, sty.freeBox__iv4LQ)}>
                {renderPlasmicSlot({
                  defaultContents: "1.0.0",
                  value: args.versionNumber,
                  className: classNames(sty.slotTargetVersionNumber),
                })}
              </div>
              <div
                data-plasmic-name={"hint"}
                data-plasmic-override={overrides.hint}
                className={classNames(
                  projectcss.all,
                  projectcss.__wab_text,
                  sty.hint,
                  {
                    [sty.hintstate_disabled]: hasVariant(
                      $state,
                      "state",
                      "disabled"
                    ),
                  }
                )}
              >
                {
                  "Version numbers are automatically calculated using semantic versioning."
                }
              </div>
            </div>
          </div>
        ) : null}
        <div
          className={classNames(projectcss.all, sty.freeBox___67EiX, {
            [sty.freeBoxstate_disabled___67EiXc5URt]: hasVariant(
              $state,
              "state",
              "disabled"
            ),
            [sty.freeBoxstate_nochanges___67EiXxcr0E]: hasVariant(
              $state,
              "state",
              "nochanges"
            ),
          })}
        >
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__lY3Fv,
              {
                [sty.textstate_nochanges__lY3Fvxcr0E]: hasVariant(
                  $state,
                  "state",
                  "nochanges"
                ),
              }
            )}
          >
            {"Description"}
          </div>
          <input
            data-plasmic-name={"title"}
            data-plasmic-override={overrides.title}
            className={classNames(projectcss.all, projectcss.input, sty.title, {
              [sty.titlestate_disabled]: hasVariant(
                $state,
                "state",
                "disabled"
              ),
              [sty.titlestate_nochanges]: hasVariant(
                $state,
                "state",
                "nochanges"
              ),
            })}
            disabled={
              hasVariant($state, "state", "disabled") ? true : undefined
            }
            placeholder={"Description (optional)…"}
            ref={(ref) => {
              $refs["title"] = ref;
            }}
            size={1}
            type={"text"}
          />
        </div>
        {(hasVariant($state, "state", "nochanges") ? false : true) ? (
          <div
            data-plasmic-name={"publishHint"}
            data-plasmic-override={overrides.publishHint}
            className={classNames(projectcss.all, sty.publishHint, {
              [sty.publishHintstate_nochanges]: hasVariant(
                $state,
                "state",
                "nochanges"
              ),
            })}
          >
            {false ? (
              <Button
                data-plasmic-name={"openButton"}
                data-plasmic-override={overrides.openButton}
                className={classNames("__wab_instance", sty.openButton)}
                endIcon={
                  <ChevronDownSvgIcon
                    className={classNames(projectcss.all, sty.svg__zwFhP)}
                    role={"img"}
                  />
                }
                startIcon={
                  <PlasmicImg__
                    data-plasmic-name={"img"}
                    data-plasmic-override={overrides.img}
                    alt={""}
                    className={classNames(sty.img)}
                    displayHeight={"16px"}
                    displayMaxHeight={"none"}
                    displayMaxWidth={"none"}
                    displayMinHeight={"0"}
                    displayMinWidth={"0"}
                    displayWidth={"16px"}
                    src={
                      "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAxNiAxNiIgaGVpZ2h0PSIxNiIgd2lkdGg9IjE2Ij4KPGcgY2xpcC1wYXRoPSJ1cmwoI2NsaXAwKSI+CjxwYXRoIGZpbGw9IiM2RUQxRkYiIGQ9Ik05LjM2Nzc0IDMuMzMzMzRIMi42NjY2N0MyLjI5ODQ4IDMuMzMzMzQgMiAzLjYzMTgyIDIgNC4wMDAwMVYxMy4zMzMzQzIgMTMuNzAxNSAyLjI5ODQ4IDE0IDIuNjY2NjcgMTRIMTJDMTIuMzY4MiAxNCAxMi42NjY3IDEzLjcwMTUgMTIuNjY2NyAxMy4zMzMzVjYuNjM0MDhMMTEuMzMzMyA3Ljk2NzQxVjEyLjY2NjdIMy4zMzMzM1Y0LjY2NjY4SDguMDM0NEw5LjM2Nzc0IDMuMzMzMzRaIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPgo8cmVjdCBmaWxsPSIjNkVEMUZGIiB0cmFuc2Zvcm09InJvdGF0ZSgxMzUgMTQuMzcwOSAyLjYwOTc3KSIgaGVpZ2h0PSIxLjMzMzMzIiB3aWR0aD0iMTAuNjY2NyIgeT0iMi42MDk3NyIgeD0iMTQuMzcwOSIvPgo8cGF0aCBmaWxsPSIjNkVEMUZGIiBkPSJNMTAuNjQwMiAxLjkwMjY4QzEwLjY0MDIgMS41MzQ0OSAxMC45Mzg2IDEuMjM2MDEgMTEuMzA2OCAxLjIzNjAxTDE0LjgwMTkgMS4yMzYwMUwxNC44MDE5IDQuNzMxMTFDMTQuODAxOSA1LjA5OTMgMTQuNTAzNCA1LjM5Nzc3IDE0LjEzNTMgNS4zOTc3N0MxMy43NjcxIDUuMzk3NzcgMTMuNDY4NiA1LjA5OTMgMTMuNDY4NiA0LjczMTExTDEzLjQ2ODYgMi41NjkzNUwxMS4zMDY4IDIuNTY5MzVDMTAuOTM4NiAyLjU2OTM1IDEwLjY0MDIgMi4yNzA4NyAxMC42NDAyIDEuOTAyNjhaIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPgo8L2c+CjxkZWZzPgo8Y2xpcFBhdGggaWQ9ImNsaXAwIj4KPHBhdGggZmlsbD0id2hpdGUiIGQ9Ik0wIDBIMTZWMTZIMFYwWiIvPgo8L2NsaXBQYXRoPgo8L2RlZnM+Cjwvc3ZnPgo="
                    }
                  />
                }
                type={["link"]}
                withIcons={["startIcon"]}
              >
                {"See here for more details"}
              </Button>
            ) : null}
          </div>
        ) : null}
        {(hasVariant($state, "state", "nochanges") ? false : true) ? (
          <Button
            data-plasmic-name={"publishButton"}
            data-plasmic-override={overrides.publishButton}
            className={classNames("__wab_instance", sty.publishButton, {
              [sty.publishButtonstate_disabled]: hasVariant(
                $state,
                "state",
                "disabled"
              ),
              [sty.publishButtonstate_nochanges]: hasVariant(
                $state,
                "state",
                "nochanges"
              ),
            })}
            disabled={
              hasVariant($state, "state", "disabled") ? true : undefined
            }
            endIcon={
              <ChevronDownSvgIcon
                className={classNames(projectcss.all, sty.svg__e8G4E)}
                role={"img"}
              />
            }
            size={"wide"}
            startIcon={
              false ? (
                <TrashIcon
                  className={classNames(projectcss.all, sty.svg__tI9X5)}
                  role={"img"}
                />
              ) : null
            }
            type={["primary"]}
          >
            {"Publish"}
          </Button>
        ) : null}
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "closeButton",
    "hint",
    "title",
    "publishHint",
    "openButton",
    "img",
    "publishButton",
  ],
  closeButton: ["closeButton"],
  hint: ["hint"],
  title: ["title"],
  publishHint: ["publishHint", "openButton", "img"],
  openButton: ["openButton", "img"],
  img: ["img"],
  publishButton: ["publishButton"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  closeButton: typeof PlasmicImg__;
  hint: "div";
  title: "input";
  publishHint: "div";
  openButton: typeof Button;
  img: typeof PlasmicImg__;
  publishButton: typeof Button;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicPublishDialogContent__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicPublishDialogContent__VariantsArgs;
    args?: PlasmicPublishDialogContent__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicPublishDialogContent__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicPublishDialogContent__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicPublishDialogContent__ArgProps,
          internalVariantPropNames: PlasmicPublishDialogContent__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicPublishDialogContent__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicPublishDialogContent";
  } else {
    func.displayName = `PlasmicPublishDialogContent.${nodeName}`;
  }
  return func;
}

export const PlasmicPublishDialogContent = Object.assign(
  // Top-level PlasmicPublishDialogContent renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    closeButton: makeNodeComponent("closeButton"),
    hint: makeNodeComponent("hint"),
    title: makeNodeComponent("title"),
    publishHint: makeNodeComponent("publishHint"),
    openButton: makeNodeComponent("openButton"),
    img: makeNodeComponent("img"),
    publishButton: makeNodeComponent("publishButton"),

    // Metadata about props expected for PlasmicPublishDialogContent
    internalVariantProps: PlasmicPublishDialogContent__VariantProps,
    internalArgProps: PlasmicPublishDialogContent__ArgProps,
  }
);

export default PlasmicPublishDialogContent;
/* prettier-ignore-end */
