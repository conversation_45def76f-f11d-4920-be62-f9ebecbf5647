/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: aukbrhkegRkQ6KizvhdUPT
// Component: bDbzY5jXLz

import * as React from "react";

import {
  Flex as Flex__,
  SingleBooleanChoiceArg,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  generateStateOnChangeProp,
  generateStateValueProp,
  hasVariant,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import LeftPaneHeader from "../../components/studio/LeftPaneHeader"; // plasmic-import: XLa52PvduIy/component
import LeftSearchPanel from "../../components/studio/LeftSearchPanel"; // plasmic-import: TqAPn0srTq/component
import Button from "../../components/widgets/Button"; // plasmic-import: SEF-sRmSoqV5c/component
import Select from "../../components/widgets/Select"; // plasmic-import: j_4IQyOWK2b/component
import Select__Option from "../../components/widgets/Select__Option"; // plasmic-import: rr-LWdMni2G/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_design_system_deprecated_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "../PP__plasmickit_left_pane.module.css"; // plasmic-import: aukbrhkegRkQ6KizvhdUPT/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_new_design_system_former_style_controls_css from "../plasmic_kit_style_controls/plasmic_plasmic_kit_styles_pane.module.css"; // plasmic-import: gYEVvAzCcLMHDVPvuYxkFh/projectcss
import sty from "./PlasmicLeftGeneralTokensPanel.module.css"; // plasmic-import: bDbzY5jXLz/css

import ChevronDownSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ChevronDownSvg"; // plasmic-import: xZrB9_0ir/icon
import DownloadSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__DownloadSvg"; // plasmic-import: Bu7POPssl/icon
import PlusSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__PlusSvg"; // plasmic-import: sQKgd2GNr/icon

createPlasmicElementProxy;

export type PlasmicLeftGeneralTokensPanel__VariantMembers = {
  isTargeting: "isTargeting";
};
export type PlasmicLeftGeneralTokensPanel__VariantsArgs = {
  isTargeting?: SingleBooleanChoiceArg<"isTargeting">;
};
type VariantPropType = keyof PlasmicLeftGeneralTokensPanel__VariantsArgs;
export const PlasmicLeftGeneralTokensPanel__VariantProps =
  new Array<VariantPropType>("isTargeting");

export type PlasmicLeftGeneralTokensPanel__ArgsType = {};
type ArgPropType = keyof PlasmicLeftGeneralTokensPanel__ArgsType;
export const PlasmicLeftGeneralTokensPanel__ArgProps = new Array<ArgPropType>();

export type PlasmicLeftGeneralTokensPanel__OverridesType = {
  root?: Flex__<"div">;
  leftSearchPanel?: Flex__<typeof LeftSearchPanel>;
  leftPaneHeader?: Flex__<typeof LeftPaneHeader>;
  importTokensButton?: Flex__<typeof Button>;
  globalVariantsSelectContainer?: Flex__<"div">;
  globalVariantSelect?: Flex__<typeof Select>;
  option?: Flex__<typeof Select__Option>;
  content?: Flex__<"div">;
};

export interface DefaultLeftGeneralTokensPanelProps {
  isTargeting?: SingleBooleanChoiceArg<"isTargeting">;
  className?: string;
}

const $$ = {};

function PlasmicLeftGeneralTokensPanel__RenderFunc(props: {
  variants: PlasmicLeftGeneralTokensPanel__VariantsArgs;
  args: PlasmicLeftGeneralTokensPanel__ArgsType;
  overrides: PlasmicLeftGeneralTokensPanel__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "isTargeting",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.isTargeting,
      },
      {
        path: "globalVariantSelect.value",
        type: "private",
        variableType: "text",
        initFunc: ({ $props, $state, $queries, $ctx }) => "",
      },
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_deprecated_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        plasmic_plasmic_kit_new_design_system_former_style_controls_css.plasmic_tokens,
        sty.root,
        {
          [sty.rootisTargeting]: hasVariant(
            $state,
            "isTargeting",
            "isTargeting"
          ),
        }
      )}
    >
      <LeftSearchPanel
        data-plasmic-name={"leftSearchPanel"}
        data-plasmic-override={overrides.leftSearchPanel}
        className={classNames("__wab_instance", sty.leftSearchPanel, {
          [sty.leftSearchPanelisTargeting]: hasVariant(
            $state,
            "isTargeting",
            "isTargeting"
          ),
        })}
        rightOptions={"groupingControls"}
      />

      <LeftPaneHeader
        data-plasmic-name={"leftPaneHeader"}
        data-plasmic-override={overrides.leftPaneHeader}
        actions={
          <Button
            data-plasmic-name={"importTokensButton"}
            data-plasmic-override={overrides.importTokensButton}
            endIcon={
              <ChevronDownSvgIcon
                className={classNames(projectcss.all, sty.svg__o0O5S)}
                role={"img"}
              />
            }
            size={"wide"}
            startIcon={
              <DownloadSvgIcon
                className={classNames(projectcss.all, sty.svg__e3TfD)}
                role={"img"}
              />
            }
            type={["secondary"]}
            withIcons={["startIcon"]}
          >
            {"Import tokens"}
          </Button>
        }
        className={classNames("__wab_instance", sty.leftPaneHeader, {
          [sty.leftPaneHeaderisTargeting]: hasVariant(
            $state,
            "isTargeting",
            "isTargeting"
          ),
        })}
        description={
          "Create tokens for colors, spacing, and more. Tokens can reference other tokens."
        }
        hasTitleActions={true}
        title={"Style Tokens"}
        titleActions={
          <div
            data-plasmic-name={"globalVariantsSelectContainer"}
            data-plasmic-override={overrides.globalVariantsSelectContainer}
            className={classNames(
              projectcss.all,
              sty.globalVariantsSelectContainer
            )}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__h2R,
                {
                  [sty.textisTargeting__h2RWy5IQ]: hasVariant(
                    $state,
                    "isTargeting",
                    "isTargeting"
                  ),
                }
              )}
            >
              {"Target:"}
            </div>
            <Select
              data-plasmic-name={"globalVariantSelect"}
              data-plasmic-override={overrides.globalVariantSelect}
              className={classNames("__wab_instance", sty.globalVariantSelect)}
              icon={
                <PlusSvgIcon
                  className={classNames(projectcss.all, sty.svg__ugBmB)}
                  role={"img"}
                />
              }
              name={""}
              onChange={async (...eventArgs: any) => {
                ((...eventArgs) => {
                  generateStateOnChangeProp($state, [
                    "globalVariantSelect",
                    "value",
                  ])(eventArgs[0]);
                }).apply(null, eventArgs);

                if (
                  eventArgs.length > 1 &&
                  eventArgs[1] &&
                  eventArgs[1]._plasmic_state_init_
                ) {
                  return;
                }
              }}
              placeholder={
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__hDqR6
                  )}
                >
                  {"Base"}
                </div>
              }
              size={"tiny"}
              value={generateStateValueProp($state, [
                "globalVariantSelect",
                "value",
              ])}
            >
              <Select__Option
                data-plasmic-name={"option"}
                data-plasmic-override={overrides.option}
                className={classNames("__wab_instance", sty.option)}
                value={"base"}
              >
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__qa0Lm
                  )}
                >
                  {"Base"}
                </div>
              </Select__Option>
            </Select>
          </div>
        }
      />

      <div
        data-plasmic-name={"content"}
        data-plasmic-override={overrides.content}
        className={classNames(projectcss.all, sty.content)}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "leftSearchPanel",
    "leftPaneHeader",
    "importTokensButton",
    "globalVariantsSelectContainer",
    "globalVariantSelect",
    "option",
    "content",
  ],
  leftSearchPanel: ["leftSearchPanel"],
  leftPaneHeader: [
    "leftPaneHeader",
    "importTokensButton",
    "globalVariantsSelectContainer",
    "globalVariantSelect",
    "option",
  ],
  importTokensButton: ["importTokensButton"],
  globalVariantsSelectContainer: [
    "globalVariantsSelectContainer",
    "globalVariantSelect",
    "option",
  ],
  globalVariantSelect: ["globalVariantSelect", "option"],
  option: ["option"],
  content: ["content"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  leftSearchPanel: typeof LeftSearchPanel;
  leftPaneHeader: typeof LeftPaneHeader;
  importTokensButton: typeof Button;
  globalVariantsSelectContainer: "div";
  globalVariantSelect: typeof Select;
  option: typeof Select__Option;
  content: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicLeftGeneralTokensPanel__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicLeftGeneralTokensPanel__VariantsArgs;
    args?: PlasmicLeftGeneralTokensPanel__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicLeftGeneralTokensPanel__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicLeftGeneralTokensPanel__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicLeftGeneralTokensPanel__ArgProps,
          internalVariantPropNames: PlasmicLeftGeneralTokensPanel__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicLeftGeneralTokensPanel__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicLeftGeneralTokensPanel";
  } else {
    func.displayName = `PlasmicLeftGeneralTokensPanel.${nodeName}`;
  }
  return func;
}

export const PlasmicLeftGeneralTokensPanel = Object.assign(
  // Top-level PlasmicLeftGeneralTokensPanel renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    leftSearchPanel: makeNodeComponent("leftSearchPanel"),
    leftPaneHeader: makeNodeComponent("leftPaneHeader"),
    importTokensButton: makeNodeComponent("importTokensButton"),
    globalVariantsSelectContainer: makeNodeComponent(
      "globalVariantsSelectContainer"
    ),
    globalVariantSelect: makeNodeComponent("globalVariantSelect"),
    option: makeNodeComponent("option"),
    content: makeNodeComponent("content"),

    // Metadata about props expected for PlasmicLeftGeneralTokensPanel
    internalVariantProps: PlasmicLeftGeneralTokensPanel__VariantProps,
    internalArgProps: PlasmicLeftGeneralTokensPanel__ArgProps,
  }
);

export default PlasmicLeftGeneralTokensPanel;
/* prettier-ignore-end */
