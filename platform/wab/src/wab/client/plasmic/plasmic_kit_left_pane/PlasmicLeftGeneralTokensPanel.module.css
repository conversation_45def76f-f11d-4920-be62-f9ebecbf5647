.root {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
  flex-direction: column;
  background: #ffffff;
  min-width: 0;
  min-height: 0;
}
.rootisTargeting {
  background: var(--token-htSNbGB58Rx);
}
.leftSearchPanel:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
}
.leftSearchPanelisTargeting:global(.__wab_instance) {
  flex-shrink: 0;
}
.leftPaneHeader:global(.__wab_instance) {
  position: relative;
}
.svg__e3TfD {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 16px;
  height: 16px;
}
.svg__o0O5S {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 16px;
  height: 1em;
}
.textWithInfo__ubSre:global(.__wab_instance) {
  max-width: 100%;
}
.text___8DPia {
  font-weight: 600;
  font-size: 14px;
}
.globalVariantsSelectContainer {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  column-gap: 4px;
}
.text__h2R {
  width: auto;
  height: auto;
  max-width: 800px;
}
.textisTargeting__h2RWy5IQ {
  color: var(--token-WnbI6EOoey2);
  font-weight: 500;
}
.globalVariantSelect:global(.__wab_instance) {
  position: relative;
  margin: 0px;
}
.svg__ugBmB {
  position: relative;
  object-fit: cover;
  min-width: 16px;
  min-height: 16px;
  width: 16px;
  height: 16px;
}
.text__hDqR6 {
  position: relative;
  text-align: left;
}
.option:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.text__qa0Lm {
  line-height: 2;
  padding-right: 0px;
  margin: 0px;
}
.content {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  flex-grow: 1;
  overflow: hidden;
  min-width: 0;
  min-height: 0;
}
