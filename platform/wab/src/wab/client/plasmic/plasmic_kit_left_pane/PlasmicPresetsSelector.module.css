.root {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
  flex-direction: column;
  padding-right: 16px;
  padding-left: 16px;
  background: #ffffff;
  min-width: 0;
  min-height: 0;
}
.freeBox {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  top: auto;
  left: auto;
}
.search:global(.__wab_instance) {
  width: 264px;
  flex-shrink: 0;
}
.text {
  position: relative;
}
.groups {
  display: flex;
  position: relative;
  width: auto;
  height: auto;
  left: auto;
  top: auto;
  flex-direction: column;
  row-gap: 16px;
  padding: 16px 0px;
}
.presetGroup__u3B9Z:global(.__wab_instance) {
  position: relative;
}
.preset__nQhS:global(.__wab_instance) {
  position: relative;
}
.example16 {
  position: relative;
}
.preset__uqDw3:global(.__wab_instance) {
  position: relative;
}
.example162 {
  position: relative;
}
.presetGroup__tGpD:global(.__wab_instance) {
  position: relative;
}
.preset__v9F1P:global(.__wab_instance) {
  position: relative;
}
.text__xg93C {
  position: relative;
}
.preset__xvSNl:global(.__wab_instance) {
  position: relative;
}
.text___0Pi7O {
  position: relative;
}
