.root {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  max-width: 100%;
  position: relative;
  min-width: 0;
  min-height: 0;
}
.freeBox___1GpQq {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: 16px;
}
.text___27NdN {
  width: 100%;
  height: auto;
  max-width: 800px;
  min-width: 0;
}
.freeBox__kPz9 {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  row-gap: 8px;
  min-width: 0;
  padding: 0px 16px 16px;
}
.text__t3OaN {
  font-weight: 500;
  position: relative;
  width: auto;
  height: auto;
  max-width: 100%;
}
.elementSelect:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  width: 100%;
  min-width: 0;
}
.svg {
  position: relative;
  object-fit: cover;
  min-width: 16px;
  min-height: 16px;
  width: 16px;
  height: 16px;
}
.option__yxJNb:global(.__wab_instance) {
  position: relative;
}
.option__vHWgU:global(.__wab_instance) {
  position: relative;
}
.optionGroup___3Lvj:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.option__oxGAn:global(.__wab_instance) {
  position: relative;
}
.option__pMpud:global(.__wab_instance) {
  position: relative;
}
.optionGroup__ivp2:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.option__znVbi:global(.__wab_instance) {
  position: relative;
}
.option__oLj4T:global(.__wab_instance) {
  position: relative;
}
.option__eLpWf:global(.__wab_instance) {
  position: relative;
}
.form {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  max-width: 100%;
  overflow: auto;
  min-width: 0;
  min-height: 0;
}
