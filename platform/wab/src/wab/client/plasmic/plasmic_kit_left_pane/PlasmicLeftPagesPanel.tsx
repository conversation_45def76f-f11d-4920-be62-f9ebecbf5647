/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: aukbrhkegRkQ6KizvhdUPT
// Component: wXKvVcr82I

import * as React from "react";

import {
  Flex as Flex__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import LeftPaneHeader from "../../components/studio/LeftPaneHeader"; // plasmic-import: XLa52PvduIy/component
import LeftSearchPanel from "../../components/studio/LeftSearchPanel"; // plasmic-import: TqAPn0srTq/component
import Button from "../../components/widgets/Button"; // plasmic-import: SEF-sRmSoqV5c/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_design_system_deprecated_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "../PP__plasmickit_left_pane.module.css"; // plasmic-import: aukbrhkegRkQ6KizvhdUPT/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_new_design_system_former_style_controls_css from "../plasmic_kit_style_controls/plasmic_plasmic_kit_styles_pane.module.css"; // plasmic-import: gYEVvAzCcLMHDVPvuYxkFh/projectcss
import sty from "./PlasmicLeftPagesPanel.module.css"; // plasmic-import: wXKvVcr82I/css

import PlusIcon from "../plasmic_kit/PlasmicIcon__Plus"; // plasmic-import: -k064DlQ8k8-L/icon
import ChevronDownSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ChevronDownSvg"; // plasmic-import: xZrB9_0ir/icon

createPlasmicElementProxy;

export type PlasmicLeftPagesPanel__VariantMembers = {};
export type PlasmicLeftPagesPanel__VariantsArgs = {};
type VariantPropType = keyof PlasmicLeftPagesPanel__VariantsArgs;
export const PlasmicLeftPagesPanel__VariantProps = new Array<VariantPropType>();

export type PlasmicLeftPagesPanel__ArgsType = {};
type ArgPropType = keyof PlasmicLeftPagesPanel__ArgsType;
export const PlasmicLeftPagesPanel__ArgProps = new Array<ArgPropType>();

export type PlasmicLeftPagesPanel__OverridesType = {
  root?: Flex__<"div">;
  leftSearchPanel?: Flex__<typeof LeftSearchPanel>;
  leftPaneHeader?: Flex__<typeof LeftPaneHeader>;
  newPageButton?: Flex__<typeof Button>;
  content?: Flex__<"div">;
};

export interface DefaultLeftPagesPanelProps {
  className?: string;
}

const $$ = {};

function PlasmicLeftPagesPanel__RenderFunc(props: {
  variants: PlasmicLeftPagesPanel__VariantsArgs;
  args: PlasmicLeftPagesPanel__ArgsType;
  overrides: PlasmicLeftPagesPanel__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_deprecated_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        plasmic_plasmic_kit_new_design_system_former_style_controls_css.plasmic_tokens,
        sty.root
      )}
    >
      <LeftSearchPanel
        data-plasmic-name={"leftSearchPanel"}
        data-plasmic-override={overrides.leftSearchPanel}
        className={classNames("__wab_instance", sty.leftSearchPanel)}
      />

      <LeftPaneHeader
        data-plasmic-name={"leftPaneHeader"}
        data-plasmic-override={overrides.leftPaneHeader}
        actions={
          <Button
            data-plasmic-name={"newPageButton"}
            data-plasmic-override={overrides.newPageButton}
            endIcon={
              <ChevronDownSvgIcon
                className={classNames(projectcss.all, sty.svg__dIrCd)}
                role={"img"}
              />
            }
            size={"wide"}
            startIcon={
              <PlusIcon
                className={classNames(projectcss.all, sty.svg__k4Jy8)}
                role={"img"}
              />
            }
            type={["secondary"]}
            withIcons={["startIcon"]}
          >
            {"New page"}
          </Button>
        }
        className={classNames("__wab_instance", sty.leftPaneHeader)}
        description={
          "Pages are full screen components with associated route paths. A page can contain a title and other metadata (e.g. for SEO purposes)."
        }
        title={"Pages"}
      />

      <div
        data-plasmic-name={"content"}
        data-plasmic-override={overrides.content}
        className={classNames(projectcss.all, sty.content)}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "leftSearchPanel",
    "leftPaneHeader",
    "newPageButton",
    "content",
  ],
  leftSearchPanel: ["leftSearchPanel"],
  leftPaneHeader: ["leftPaneHeader", "newPageButton"],
  newPageButton: ["newPageButton"],
  content: ["content"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  leftSearchPanel: typeof LeftSearchPanel;
  leftPaneHeader: typeof LeftPaneHeader;
  newPageButton: typeof Button;
  content: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicLeftPagesPanel__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicLeftPagesPanel__VariantsArgs;
    args?: PlasmicLeftPagesPanel__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicLeftPagesPanel__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicLeftPagesPanel__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicLeftPagesPanel__ArgProps,
          internalVariantPropNames: PlasmicLeftPagesPanel__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicLeftPagesPanel__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicLeftPagesPanel";
  } else {
    func.displayName = `PlasmicLeftPagesPanel.${nodeName}`;
  }
  return func;
}

export const PlasmicLeftPagesPanel = Object.assign(
  // Top-level PlasmicLeftPagesPanel renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    leftSearchPanel: makeNodeComponent("leftSearchPanel"),
    leftPaneHeader: makeNodeComponent("leftPaneHeader"),
    newPageButton: makeNodeComponent("newPageButton"),
    content: makeNodeComponent("content"),

    // Metadata about props expected for PlasmicLeftPagesPanel
    internalVariantProps: PlasmicLeftPagesPanel__VariantProps,
    internalArgProps: PlasmicLeftPagesPanel__ArgProps,
  }
);

export default PlasmicLeftPagesPanel;
/* prettier-ignore-end */
