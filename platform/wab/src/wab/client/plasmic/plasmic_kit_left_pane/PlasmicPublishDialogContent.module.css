.root {
  display: flex;
  position: relative;
  background: var(--token-iR8SeEwQZ);
  padding: 32px;
}
.rootstate_disabled {
  cursor: wait;
}
.freeBox__qwLxK {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: stretch;
  justify-content: space-between;
  margin-bottom: 8px;
  width: 100%;
  height: auto;
  min-width: 0;
}
.text__wzTni {
  width: auto;
  height: auto;
  font-weight: 600;
}
.freeBox__ad0B7 {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  column-gap: 16px;
}
.closeButton {
  display: flex;
  width: 16px;
  height: 16px;
  cursor: pointer;
  flex-shrink: 0;
}
.freeBox__hspE3 {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  row-gap: 16px;
}
.text__bitqw {
  position: relative;
}
.freeBox__torLq {
  display: flex;
  position: relative;
}
.text__pPoTy {
  position: relative;
  color: var(--token-UunsGa2Y3t3);
  width: 128px;
  flex-shrink: 0;
  padding: 8px;
}
.freeBox__epD6O {
  display: flex;
  position: relative;
  flex-direction: column;
}
.freeBox__iv4LQ {
  display: flex;
  padding: 8px;
}
.slotTargetVersionNumber {
  font-weight: 500;
}
.hint {
  position: relative;
  background: var(--token-WsutfVbnQWpY);
  color: var(--token-N-GFU-C_NPxa);
  border-radius: 8px;
  padding: 16px;
}
.freeBox___67EiX {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
}
.freeBoxstate_disabled___67EiXc5URt {
  top: auto;
  left: auto;
}
.freeBoxstate_nochanges___67EiXxcr0E {
  display: none;
}
.text__lY3Fv {
  position: relative;
  height: auto;
  color: var(--token-UunsGa2Y3t3);
  width: 128px;
  flex-shrink: 0;
  padding: 8px;
}
.title {
  display: flex;
  width: 100%;
  min-width: 0;
  border-radius: 6px;
  padding: 8px;
  border: 1px solid var(--token-eBt2ZgqRUCz);
}
.titlestate_disabled {
  cursor: wait;
  background: var(--token-O4S7RMTqZ3);
}
.root .title:disabled {
  left: auto;
  top: auto;
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}
.root .title:focus {
  box-shadow: 0px 0px 0px 2px #0091ff80;
  left: auto;
  top: auto;
  outline: none;
}
.publishHint {
  display: flex;
  position: relative;
  flex-direction: column;
  justify-content: flex-start;
  width: 100%;
  min-width: 0;
}
.openButton:global(.__wab_instance) {
  position: relative;
  width: auto;
}
.img {
  display: flex;
  position: relative;
  width: 16px;
  height: 16px;
}
.svg__zwFhP {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 16px;
  height: 1em;
}
.publishButton:global(.__wab_instance) {
  position: relative;
  align-self: flex-end;
}
.svg__tI9X5 {
  display: flex;
  position: relative;
  object-fit: cover;
  max-width: 100%;
  max-height: 100%;
  width: 16px;
  height: 16px;
}
.svg__e8G4E {
  display: flex;
  position: relative;
  object-fit: cover;
  width: 16px;
  height: 1em;
}
