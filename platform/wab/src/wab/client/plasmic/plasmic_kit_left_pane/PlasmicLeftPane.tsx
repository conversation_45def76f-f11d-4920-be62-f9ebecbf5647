/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: aukbrhkegRkQ6KizvhdUPT
// Component: avrERxAp81S

import * as React from "react";

import {
  Flex as Flex__,
  SingleChoiceArg,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  hasVariant,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import { VersionsTab as LeftVersionsPanel } from "../../components/sidebar-tabs/versions-tab"; // plasmic-import: YldGgVsq6N/component
import LeftComponentsPanel from "../../components/sidebar/LeftComponentsPanel"; // plasmic-import: 7Wsvgu6cRd/component
import LeftGeneralDataTokensPanel from "../../components/sidebar/LeftGeneralDataTokensPanel"; // plasmic-import: WwK9TyWdjIfT/component
import LeftGeneralTokensPanel from "../../components/sidebar/LeftGeneralTokensPanel"; // plasmic-import: bDbzY5jXLz/component
import LeftLintIssuesPanel from "../../components/sidebar/LeftLintIssuesPanel"; // plasmic-import: xymZo1AIeU/component
import LeftPagesPanel from "../../components/sidebar/LeftPagesPanel"; // plasmic-import: wXKvVcr82I/component
import LeftSettingsPanel from "../../components/sidebar/LeftSettingsPanel"; // plasmic-import: EeT-6P6YTW/component
import LeftSplitsPanel from "../../components/sidebar/LeftSplitsPanel"; // plasmic-import: OzaoSbFLbl/component
import { MixinsPanel as LeftMixinsPanel } from "../../components/sidebar/MixinControls"; // plasmic-import: ZsFxxgE4E8/component
import { ProjectDependenciesPanel as LeftImportsPanel } from "../../components/sidebar/ProjectDependencies"; // plasmic-import: MeRxD_0BtJ/component
import LeftThemesPanel from "../../components/sidebar/ThemesControls"; // plasmic-import: 9I47RGPv62/component
import { UserManagedFontsPanel as LeftFontsPanel } from "../../components/sidebar/UserManagedFonts"; // plasmic-import: 5oz1qmvGBe/component
import { ImageAssetsPanel as LeftImagesPanel } from "../../components/sidebar/image-asset-controls"; // plasmic-import: ECu8FUyP0f3/component
import LeftTabButton from "../../components/studio/LeftTabButton"; // plasmic-import: 1q_JapBg7U/component
import LeftTabStrip from "../../components/studio/LeftTabStrip"; // plasmic-import: l7y_rhJyMt2/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_design_system_deprecated_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "../PP__plasmickit_left_pane.module.css"; // plasmic-import: aukbrhkegRkQ6KizvhdUPT/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_new_design_system_former_style_controls_css from "../plasmic_kit_style_controls/plasmic_plasmic_kit_styles_pane.module.css"; // plasmic-import: gYEVvAzCcLMHDVPvuYxkFh/projectcss
import sty from "./PlasmicLeftPane.module.css"; // plasmic-import: avrERxAp81S/css

import GearIcon from "../plasmic_kit/PlasmicIcon__Gear"; // plasmic-import: ZmVZmXEc9f_SR/icon
import TreeIcon from "../plasmic_kit/PlasmicIcon__Tree"; // plasmic-import: 4KZjuPY_m0VTb/icon
import ComponentsSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ComponentsSvg"; // plasmic-import: coPzxnFyi/icon
import DotsHorizontalCircleSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__DotsHorizontalCircleSvg"; // plasmic-import: xdn8wiJBv/icon
import HelpCircleSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__HelpCircleSvg"; // plasmic-import: zY-2PPrFT/icon
import WarningTriangleSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__WarningTriangleSvg"; // plasmic-import: S0L-xosWD/icon

createPlasmicElementProxy;

export type PlasmicLeftPane__VariantMembers = {
  type:
    | "outline"
    | "tokens"
    | "mixins"
    | "images"
    | "themes"
    | "fonts"
    | "imports"
    | "versions"
    | "components"
    | "pages"
    | "responsiveness"
    | "settings"
    | "splits"
    | "endpoints"
    | "copilot"
    | "lint"
    | "dataTokens";
};
export type PlasmicLeftPane__VariantsArgs = {
  type?: SingleChoiceArg<
    | "outline"
    | "tokens"
    | "mixins"
    | "images"
    | "themes"
    | "fonts"
    | "imports"
    | "versions"
    | "components"
    | "pages"
    | "responsiveness"
    | "settings"
    | "splits"
    | "endpoints"
    | "copilot"
    | "lint"
    | "dataTokens"
  >;
};
type VariantPropType = keyof PlasmicLeftPane__VariantsArgs;
export const PlasmicLeftPane__VariantProps = new Array<VariantPropType>("type");

export type PlasmicLeftPane__ArgsType = {};
type ArgPropType = keyof PlasmicLeftPane__ArgsType;
export const PlasmicLeftPane__ArgProps = new Array<ArgPropType>();

export type PlasmicLeftPane__OverridesType = {
  root?: Flex__<"div">;
  leftTabStrip?: Flex__<typeof LeftTabStrip>;
  lint?: Flex__<typeof LeftTabButton>;
  outline?: Flex__<typeof LeftTabButton>;
  assets?: Flex__<typeof LeftTabButton>;
  settingsGroup?: Flex__<typeof LeftTabButton>;
  more?: Flex__<typeof LeftTabButton>;
  helpGroup?: Flex__<typeof LeftTabButton>;
  paneContainer?: Flex__<"div">;
  paneContent?: Flex__<"div">;
  leftGeneralTokensPanel?: Flex__<typeof LeftGeneralTokensPanel>;
  leftMixinsPanel?: Flex__<typeof LeftMixinsPanel>;
  leftImagesPanel?: Flex__<typeof LeftImagesPanel>;
  leftThemesPanel?: Flex__<typeof LeftThemesPanel>;
  leftFontsPanel?: Flex__<typeof LeftFontsPanel>;
  leftImportsPanel?: Flex__<typeof LeftImportsPanel>;
  leftVersionsPanel?: Flex__<typeof LeftVersionsPanel>;
  leftComponentsPanel?: Flex__<typeof LeftComponentsPanel>;
  leftPagesPanel?: Flex__<typeof LeftPagesPanel>;
  leftSettingsPanel?: Flex__<typeof LeftSettingsPanel>;
  leftSplitsPanel?: Flex__<typeof LeftSplitsPanel>;
  leftLintIssuesPanel?: Flex__<typeof LeftLintIssuesPanel>;
  leftGeneralDataTokensPanel?: Flex__<typeof LeftGeneralDataTokensPanel>;
};

export interface DefaultLeftPaneProps {
  type?: SingleChoiceArg<
    | "outline"
    | "tokens"
    | "mixins"
    | "images"
    | "themes"
    | "fonts"
    | "imports"
    | "versions"
    | "components"
    | "pages"
    | "responsiveness"
    | "settings"
    | "splits"
    | "endpoints"
    | "copilot"
    | "lint"
    | "dataTokens"
  >;
  className?: string;
}

const $$ = {};

function PlasmicLeftPane__RenderFunc(props: {
  variants: PlasmicLeftPane__VariantsArgs;
  args: PlasmicLeftPane__ArgsType;
  overrides: PlasmicLeftPane__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "type",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.type,
      },
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_deprecated_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        plasmic_plasmic_kit_new_design_system_former_style_controls_css.plasmic_tokens,
        sty.root,
        {
          [sty.roottype_components]: hasVariant($state, "type", "components"),
          [sty.roottype_imports]: hasVariant($state, "type", "imports"),
          [sty.roottype_mixins]: hasVariant($state, "type", "mixins"),
          [sty.roottype_pages]: hasVariant($state, "type", "pages"),
          [sty.roottype_settings]: hasVariant($state, "type", "settings"),
          [sty.roottype_tokens]: hasVariant($state, "type", "tokens"),
          [sty.roottype_versions]: hasVariant($state, "type", "versions"),
        }
      )}
    >
      <LeftTabStrip
        data-plasmic-name={"leftTabStrip"}
        data-plasmic-override={overrides.leftTabStrip}
        activeTab={
          hasVariant($state, "type", "lint")
            ? "lint"
            : hasVariant($state, "type", "endpoints")
            ? "endpoints"
            : hasVariant($state, "type", "splits")
            ? "splits"
            : hasVariant($state, "type", "pages")
            ? "pages"
            : hasVariant($state, "type", "components")
            ? "components"
            : hasVariant($state, "type", "versions")
            ? "versions"
            : hasVariant($state, "type", "fonts")
            ? "fonts"
            : hasVariant($state, "type", "themes")
            ? "themes"
            : hasVariant($state, "type", "images")
            ? "images"
            : hasVariant($state, "type", "mixins")
            ? "mixins"
            : hasVariant($state, "type", "tokens")
            ? "tokens"
            : undefined
        }
        bottomButtons={
          <LeftTabButton
            data-plasmic-name={"helpGroup"}
            data-plasmic-override={overrides.helpGroup}
            className={classNames("__wab_instance", sty.helpGroup)}
            icon={
              <HelpCircleSvgIcon
                className={classNames(projectcss.all, sty.svg__zDP)}
                role={"img"}
              />
            }
          />
        }
        buttons={
          <React.Fragment>
            <LeftTabButton
              data-plasmic-name={"lint"}
              data-plasmic-override={overrides.lint}
              className={classNames("__wab_instance", sty.lint)}
              icon={
                <WarningTriangleSvgIcon
                  className={classNames(projectcss.all, sty.svg__zOfU0)}
                  role={"img"}
                />
              }
            />

            <LeftTabButton
              data-plasmic-name={"outline"}
              data-plasmic-override={overrides.outline}
              className={classNames("__wab_instance", sty.outline)}
              icon={
                <TreeIcon
                  className={classNames(projectcss.all, sty.svg__sSj6U)}
                  role={"img"}
                />
              }
            />

            <LeftTabButton
              data-plasmic-name={"assets"}
              data-plasmic-override={overrides.assets}
              className={classNames("__wab_instance", sty.assets)}
              icon={
                <ComponentsSvgIcon
                  className={classNames(projectcss.all, sty.svg___2I0R2)}
                  role={"img"}
                />
              }
            />

            <LeftTabButton
              data-plasmic-name={"settingsGroup"}
              data-plasmic-override={overrides.settingsGroup}
              className={classNames("__wab_instance", sty.settingsGroup)}
              icon={
                <GearIcon
                  className={classNames(projectcss.all, sty.svg__otRy1)}
                  role={"img"}
                />
              }
            />

            <LeftTabButton
              data-plasmic-name={"more"}
              data-plasmic-override={overrides.more}
              className={classNames("__wab_instance", sty.more)}
              icon={
                <DotsHorizontalCircleSvgIcon
                  className={classNames(projectcss.all, sty.svg___52RoI)}
                  role={"img"}
                />
              }
            />
          </React.Fragment>
        }
        className={classNames("__wab_instance", sty.leftTabStrip, {
          [sty.leftTabStriptype_components]: hasVariant(
            $state,
            "type",
            "components"
          ),
          [sty.leftTabStriptype_endpoints]: hasVariant(
            $state,
            "type",
            "endpoints"
          ),
          [sty.leftTabStriptype_fonts]: hasVariant($state, "type", "fonts"),
          [sty.leftTabStriptype_images]: hasVariant($state, "type", "images"),
          [sty.leftTabStriptype_imports]: hasVariant($state, "type", "imports"),
          [sty.leftTabStriptype_lint]: hasVariant($state, "type", "lint"),
          [sty.leftTabStriptype_mixins]: hasVariant($state, "type", "mixins"),
          [sty.leftTabStriptype_pages]: hasVariant($state, "type", "pages"),
          [sty.leftTabStriptype_responsiveness]: hasVariant(
            $state,
            "type",
            "responsiveness"
          ),
          [sty.leftTabStriptype_splits]: hasVariant($state, "type", "splits"),
          [sty.leftTabStriptype_themes]: hasVariant($state, "type", "themes"),
          [sty.leftTabStriptype_tokens]: hasVariant($state, "type", "tokens"),
          [sty.leftTabStriptype_versions]: hasVariant(
            $state,
            "type",
            "versions"
          ),
        })}
      />

      <div
        data-plasmic-name={"paneContainer"}
        data-plasmic-override={overrides.paneContainer}
        className={classNames(projectcss.all, sty.paneContainer, {
          [sty.paneContainertype_components]: hasVariant(
            $state,
            "type",
            "components"
          ),
          [sty.paneContainertype_fonts]: hasVariant($state, "type", "fonts"),
          [sty.paneContainertype_images]: hasVariant($state, "type", "images"),
          [sty.paneContainertype_imports]: hasVariant(
            $state,
            "type",
            "imports"
          ),
          [sty.paneContainertype_mixins]: hasVariant($state, "type", "mixins"),
          [sty.paneContainertype_pages]: hasVariant($state, "type", "pages"),
          [sty.paneContainertype_responsiveness]: hasVariant(
            $state,
            "type",
            "responsiveness"
          ),
          [sty.paneContainertype_settings]: hasVariant(
            $state,
            "type",
            "settings"
          ),
          [sty.paneContainertype_themes]: hasVariant($state, "type", "themes"),
          [sty.paneContainertype_tokens]: hasVariant($state, "type", "tokens"),
          [sty.paneContainertype_versions]: hasVariant(
            $state,
            "type",
            "versions"
          ),
        })}
      >
        <div
          data-plasmic-name={"paneContent"}
          data-plasmic-override={overrides.paneContent}
          className={classNames(projectcss.all, sty.paneContent, {
            [sty.paneContenttype_components]: hasVariant(
              $state,
              "type",
              "components"
            ),
            [sty.paneContenttype_copilot]: hasVariant(
              $state,
              "type",
              "copilot"
            ),
            [sty.paneContenttype_dataTokens]: hasVariant(
              $state,
              "type",
              "dataTokens"
            ),
            [sty.paneContenttype_endpoints]: hasVariant(
              $state,
              "type",
              "endpoints"
            ),
            [sty.paneContenttype_fonts]: hasVariant($state, "type", "fonts"),
            [sty.paneContenttype_images]: hasVariant($state, "type", "images"),
            [sty.paneContenttype_imports]: hasVariant(
              $state,
              "type",
              "imports"
            ),
            [sty.paneContenttype_lint]: hasVariant($state, "type", "lint"),
            [sty.paneContenttype_mixins]: hasVariant($state, "type", "mixins"),
            [sty.paneContenttype_outline]: hasVariant(
              $state,
              "type",
              "outline"
            ),
            [sty.paneContenttype_pages]: hasVariant($state, "type", "pages"),
            [sty.paneContenttype_responsiveness]: hasVariant(
              $state,
              "type",
              "responsiveness"
            ),
            [sty.paneContenttype_settings]: hasVariant(
              $state,
              "type",
              "settings"
            ),
            [sty.paneContenttype_themes]: hasVariant($state, "type", "themes"),
            [sty.paneContenttype_tokens]: hasVariant($state, "type", "tokens"),
            [sty.paneContenttype_versions]: hasVariant(
              $state,
              "type",
              "versions"
            ),
          })}
        >
          {(hasVariant($state, "type", "tokens") ? true : false) ? (
            <LeftGeneralTokensPanel
              data-plasmic-name={"leftGeneralTokensPanel"}
              data-plasmic-override={overrides.leftGeneralTokensPanel}
              className={classNames(
                "__wab_instance",
                sty.leftGeneralTokensPanel,
                {
                  [sty.leftGeneralTokensPaneltype_responsiveness]: hasVariant(
                    $state,
                    "type",
                    "responsiveness"
                  ),
                  [sty.leftGeneralTokensPaneltype_tokens]: hasVariant(
                    $state,
                    "type",
                    "tokens"
                  ),
                }
              )}
            />
          ) : null}
          {(hasVariant($state, "type", "mixins") ? true : false) ? (
            <LeftMixinsPanel
              data-plasmic-name={"leftMixinsPanel"}
              data-plasmic-override={overrides.leftMixinsPanel}
              className={classNames("__wab_instance", sty.leftMixinsPanel, {
                [sty.leftMixinsPaneltype_mixins]: hasVariant(
                  $state,
                  "type",
                  "mixins"
                ),
              })}
            />
          ) : null}
          {(hasVariant($state, "type", "images") ? true : false) ? (
            <LeftImagesPanel
              data-plasmic-name={"leftImagesPanel"}
              data-plasmic-override={overrides.leftImagesPanel}
              className={classNames("__wab_instance", sty.leftImagesPanel, {
                [sty.leftImagesPaneltype_images]: hasVariant(
                  $state,
                  "type",
                  "images"
                ),
              })}
            />
          ) : null}
          {(hasVariant($state, "type", "themes") ? true : false) ? (
            <LeftThemesPanel
              data-plasmic-name={"leftThemesPanel"}
              data-plasmic-override={overrides.leftThemesPanel}
              className={classNames("__wab_instance", sty.leftThemesPanel, {
                [sty.leftThemesPaneltype_themes]: hasVariant(
                  $state,
                  "type",
                  "themes"
                ),
              })}
            />
          ) : null}
          {(hasVariant($state, "type", "fonts") ? true : false) ? (
            <LeftFontsPanel
              data-plasmic-name={"leftFontsPanel"}
              data-plasmic-override={overrides.leftFontsPanel}
              className={classNames("__wab_instance", sty.leftFontsPanel, {
                [sty.leftFontsPaneltype_fonts]: hasVariant(
                  $state,
                  "type",
                  "fonts"
                ),
              })}
            />
          ) : null}
          {(hasVariant($state, "type", "imports") ? true : false) ? (
            <LeftImportsPanel
              data-plasmic-name={"leftImportsPanel"}
              data-plasmic-override={overrides.leftImportsPanel}
              className={classNames("__wab_instance", sty.leftImportsPanel, {
                [sty.leftImportsPaneltype_fonts]: hasVariant(
                  $state,
                  "type",
                  "fonts"
                ),
                [sty.leftImportsPaneltype_images]: hasVariant(
                  $state,
                  "type",
                  "images"
                ),
                [sty.leftImportsPaneltype_imports]: hasVariant(
                  $state,
                  "type",
                  "imports"
                ),
                [sty.leftImportsPaneltype_mixins]: hasVariant(
                  $state,
                  "type",
                  "mixins"
                ),
                [sty.leftImportsPaneltype_pages]: hasVariant(
                  $state,
                  "type",
                  "pages"
                ),
                [sty.leftImportsPaneltype_themes]: hasVariant(
                  $state,
                  "type",
                  "themes"
                ),
                [sty.leftImportsPaneltype_tokens]: hasVariant(
                  $state,
                  "type",
                  "tokens"
                ),
              })}
            />
          ) : null}
          {(hasVariant($state, "type", "versions") ? true : false) ? (
            <LeftVersionsPanel
              data-plasmic-name={"leftVersionsPanel"}
              data-plasmic-override={overrides.leftVersionsPanel}
              className={classNames("__wab_instance", sty.leftVersionsPanel, {
                [sty.leftVersionsPaneltype_pages]: hasVariant(
                  $state,
                  "type",
                  "pages"
                ),
                [sty.leftVersionsPaneltype_versions]: hasVariant(
                  $state,
                  "type",
                  "versions"
                ),
              })}
            />
          ) : null}
          {(hasVariant($state, "type", "components") ? true : false) ? (
            <LeftComponentsPanel
              data-plasmic-name={"leftComponentsPanel"}
              data-plasmic-override={overrides.leftComponentsPanel}
              className={classNames("__wab_instance", sty.leftComponentsPanel, {
                [sty.leftComponentsPaneltype_components]: hasVariant(
                  $state,
                  "type",
                  "components"
                ),
                [sty.leftComponentsPaneltype_pages]: hasVariant(
                  $state,
                  "type",
                  "pages"
                ),
              })}
            />
          ) : null}
          {(
            hasVariant($state, "type", "settings")
              ? true
              : hasVariant($state, "type", "pages")
              ? true
              : false
          ) ? (
            <LeftPagesPanel
              data-plasmic-name={"leftPagesPanel"}
              data-plasmic-override={overrides.leftPagesPanel}
              className={classNames("__wab_instance", sty.leftPagesPanel, {
                [sty.leftPagesPaneltype_pages]: hasVariant(
                  $state,
                  "type",
                  "pages"
                ),
                [sty.leftPagesPaneltype_settings]: hasVariant(
                  $state,
                  "type",
                  "settings"
                ),
              })}
            />
          ) : null}
          <LeftSettingsPanel
            data-plasmic-name={"leftSettingsPanel"}
            data-plasmic-override={overrides.leftSettingsPanel}
            className={classNames("__wab_instance", sty.leftSettingsPanel, {
              [sty.leftSettingsPaneltype_settings]: hasVariant(
                $state,
                "type",
                "settings"
              ),
            })}
          />

          <LeftSplitsPanel
            data-plasmic-name={"leftSplitsPanel"}
            data-plasmic-override={overrides.leftSplitsPanel}
            className={classNames("__wab_instance", sty.leftSplitsPanel, {
              [sty.leftSplitsPaneltype_splits]: hasVariant(
                $state,
                "type",
                "splits"
              ),
            })}
          />

          {(hasVariant($state, "type", "lint") ? true : false) ? (
            <LeftLintIssuesPanel
              data-plasmic-name={"leftLintIssuesPanel"}
              data-plasmic-override={overrides.leftLintIssuesPanel}
              className={classNames("__wab_instance", sty.leftLintIssuesPanel, {
                [sty.leftLintIssuesPaneltype_lint]: hasVariant(
                  $state,
                  "type",
                  "lint"
                ),
              })}
            />
          ) : null}
          <LeftGeneralDataTokensPanel
            data-plasmic-name={"leftGeneralDataTokensPanel"}
            data-plasmic-override={overrides.leftGeneralDataTokensPanel}
            className={classNames(
              "__wab_instance",
              sty.leftGeneralDataTokensPanel,
              {
                [sty.leftGeneralDataTokensPaneltype_dataTokens]: hasVariant(
                  $state,
                  "type",
                  "dataTokens"
                ),
              }
            )}
          />
        </div>
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "leftTabStrip",
    "lint",
    "outline",
    "assets",
    "settingsGroup",
    "more",
    "helpGroup",
    "paneContainer",
    "paneContent",
    "leftGeneralTokensPanel",
    "leftMixinsPanel",
    "leftImagesPanel",
    "leftThemesPanel",
    "leftFontsPanel",
    "leftImportsPanel",
    "leftVersionsPanel",
    "leftComponentsPanel",
    "leftPagesPanel",
    "leftSettingsPanel",
    "leftSplitsPanel",
    "leftLintIssuesPanel",
    "leftGeneralDataTokensPanel",
  ],
  leftTabStrip: [
    "leftTabStrip",
    "lint",
    "outline",
    "assets",
    "settingsGroup",
    "more",
    "helpGroup",
  ],
  lint: ["lint"],
  outline: ["outline"],
  assets: ["assets"],
  settingsGroup: ["settingsGroup"],
  more: ["more"],
  helpGroup: ["helpGroup"],
  paneContainer: [
    "paneContainer",
    "paneContent",
    "leftGeneralTokensPanel",
    "leftMixinsPanel",
    "leftImagesPanel",
    "leftThemesPanel",
    "leftFontsPanel",
    "leftImportsPanel",
    "leftVersionsPanel",
    "leftComponentsPanel",
    "leftPagesPanel",
    "leftSettingsPanel",
    "leftSplitsPanel",
    "leftLintIssuesPanel",
    "leftGeneralDataTokensPanel",
  ],
  paneContent: [
    "paneContent",
    "leftGeneralTokensPanel",
    "leftMixinsPanel",
    "leftImagesPanel",
    "leftThemesPanel",
    "leftFontsPanel",
    "leftImportsPanel",
    "leftVersionsPanel",
    "leftComponentsPanel",
    "leftPagesPanel",
    "leftSettingsPanel",
    "leftSplitsPanel",
    "leftLintIssuesPanel",
    "leftGeneralDataTokensPanel",
  ],
  leftGeneralTokensPanel: ["leftGeneralTokensPanel"],
  leftMixinsPanel: ["leftMixinsPanel"],
  leftImagesPanel: ["leftImagesPanel"],
  leftThemesPanel: ["leftThemesPanel"],
  leftFontsPanel: ["leftFontsPanel"],
  leftImportsPanel: ["leftImportsPanel"],
  leftVersionsPanel: ["leftVersionsPanel"],
  leftComponentsPanel: ["leftComponentsPanel"],
  leftPagesPanel: ["leftPagesPanel"],
  leftSettingsPanel: ["leftSettingsPanel"],
  leftSplitsPanel: ["leftSplitsPanel"],
  leftLintIssuesPanel: ["leftLintIssuesPanel"],
  leftGeneralDataTokensPanel: ["leftGeneralDataTokensPanel"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  leftTabStrip: typeof LeftTabStrip;
  lint: typeof LeftTabButton;
  outline: typeof LeftTabButton;
  assets: typeof LeftTabButton;
  settingsGroup: typeof LeftTabButton;
  more: typeof LeftTabButton;
  helpGroup: typeof LeftTabButton;
  paneContainer: "div";
  paneContent: "div";
  leftGeneralTokensPanel: typeof LeftGeneralTokensPanel;
  leftMixinsPanel: typeof LeftMixinsPanel;
  leftImagesPanel: typeof LeftImagesPanel;
  leftThemesPanel: typeof LeftThemesPanel;
  leftFontsPanel: typeof LeftFontsPanel;
  leftImportsPanel: typeof LeftImportsPanel;
  leftVersionsPanel: typeof LeftVersionsPanel;
  leftComponentsPanel: typeof LeftComponentsPanel;
  leftPagesPanel: typeof LeftPagesPanel;
  leftSettingsPanel: typeof LeftSettingsPanel;
  leftSplitsPanel: typeof LeftSplitsPanel;
  leftLintIssuesPanel: typeof LeftLintIssuesPanel;
  leftGeneralDataTokensPanel: typeof LeftGeneralDataTokensPanel;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicLeftPane__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicLeftPane__VariantsArgs;
    args?: PlasmicLeftPane__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicLeftPane__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicLeftPane__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicLeftPane__ArgProps,
          internalVariantPropNames: PlasmicLeftPane__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicLeftPane__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicLeftPane";
  } else {
    func.displayName = `PlasmicLeftPane.${nodeName}`;
  }
  return func;
}

export const PlasmicLeftPane = Object.assign(
  // Top-level PlasmicLeftPane renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    leftTabStrip: makeNodeComponent("leftTabStrip"),
    lint: makeNodeComponent("lint"),
    outline: makeNodeComponent("outline"),
    assets: makeNodeComponent("assets"),
    settingsGroup: makeNodeComponent("settingsGroup"),
    more: makeNodeComponent("more"),
    helpGroup: makeNodeComponent("helpGroup"),
    paneContainer: makeNodeComponent("paneContainer"),
    paneContent: makeNodeComponent("paneContent"),
    leftGeneralTokensPanel: makeNodeComponent("leftGeneralTokensPanel"),
    leftMixinsPanel: makeNodeComponent("leftMixinsPanel"),
    leftImagesPanel: makeNodeComponent("leftImagesPanel"),
    leftThemesPanel: makeNodeComponent("leftThemesPanel"),
    leftFontsPanel: makeNodeComponent("leftFontsPanel"),
    leftImportsPanel: makeNodeComponent("leftImportsPanel"),
    leftVersionsPanel: makeNodeComponent("leftVersionsPanel"),
    leftComponentsPanel: makeNodeComponent("leftComponentsPanel"),
    leftPagesPanel: makeNodeComponent("leftPagesPanel"),
    leftSettingsPanel: makeNodeComponent("leftSettingsPanel"),
    leftSplitsPanel: makeNodeComponent("leftSplitsPanel"),
    leftLintIssuesPanel: makeNodeComponent("leftLintIssuesPanel"),
    leftGeneralDataTokensPanel: makeNodeComponent("leftGeneralDataTokensPanel"),

    // Metadata about props expected for PlasmicLeftPane
    internalVariantProps: PlasmicLeftPane__VariantProps,
    internalArgProps: PlasmicLeftPane__ArgProps,
  }
);

export default PlasmicLeftPane;
/* prettier-ignore-end */
