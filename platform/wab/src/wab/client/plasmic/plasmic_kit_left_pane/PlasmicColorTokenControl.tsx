/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: aukbrhkegRkQ6KizvhdUPT
// Component: JyqCOl0Ccj

import * as React from "react";

import {
  Flex as Flex__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  renderPlasmicSlot,
  useTrigger,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import RowItem from "../../components/RowItem"; // plasmic-import: gkx-PRZnjFPo/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_design_system_deprecated_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "../PP__plasmickit_left_pane.module.css"; // plasmic-import: aukbrhkegRkQ6KizvhdUPT/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_new_design_system_former_style_controls_css from "../plasmic_kit_style_controls/plasmic_plasmic_kit_styles_pane.module.css"; // plasmic-import: gYEVvAzCcLMHDVPvuYxkFh/projectcss
import sty from "./PlasmicColorTokenControl.module.css"; // plasmic-import: JyqCOl0Ccj/css

import ComponentsSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ComponentsSvg"; // plasmic-import: coPzxnFyi/icon

createPlasmicElementProxy;

export type PlasmicColorTokenControl__VariantMembers = {};
export type PlasmicColorTokenControl__VariantsArgs = {};
type VariantPropType = keyof PlasmicColorTokenControl__VariantsArgs;
export const PlasmicColorTokenControl__VariantProps =
  new Array<VariantPropType>();

export type PlasmicColorTokenControl__ArgsType = {
  icon?: React.ReactNode;
  children?: React.ReactNode;
  value?: React.ReactNode;
};
type ArgPropType = keyof PlasmicColorTokenControl__ArgsType;
export const PlasmicColorTokenControl__ArgProps = new Array<ArgPropType>(
  "icon",
  "children",
  "value"
);

export type PlasmicColorTokenControl__OverridesType = {
  root?: Flex__<"div">;
  rowItem?: Flex__<typeof RowItem>;
};

export interface DefaultColorTokenControlProps {
  icon?: React.ReactNode;
  children?: React.ReactNode;
  value?: React.ReactNode;
  className?: string;
}

const $$ = {};

function PlasmicColorTokenControl__RenderFunc(props: {
  variants: PlasmicColorTokenControl__VariantsArgs;
  args: PlasmicColorTokenControl__ArgsType;
  overrides: PlasmicColorTokenControl__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const [isRootHover, triggerRootHoverProps] = useTrigger("useHover", {});
  const triggers = {
    hover_root: isRootHover,
  };

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_deprecated_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        plasmic_plasmic_kit_new_design_system_former_style_controls_css.plasmic_tokens,
        sty.root
      )}
      data-plasmic-trigger-props={[triggerRootHoverProps]}
    >
      <RowItem
        data-plasmic-name={"rowItem"}
        data-plasmic-override={overrides.rowItem}
        addendum={renderPlasmicSlot({
          defaultContents: "Big  Addendum",
          value: args.value,
        })}
        className={classNames("__wab_instance", sty.rowItem)}
        icon={renderPlasmicSlot({
          defaultContents: (
            <ComponentsSvgIcon
              className={classNames(projectcss.all, sty.svg__wDxJ4)}
              role={"img"}
            />
          ),

          value: args.icon,
        })}
        menuSize={"small"}
        showAddendum={triggers.hover_root ? true : undefined}
      >
        {renderPlasmicSlot({
          defaultContents: "Some very long item label",
          value: args.children,
        })}
      </RowItem>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "rowItem"],
  rowItem: ["rowItem"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  rowItem: typeof RowItem;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicColorTokenControl__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicColorTokenControl__VariantsArgs;
    args?: PlasmicColorTokenControl__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicColorTokenControl__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicColorTokenControl__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicColorTokenControl__ArgProps,
          internalVariantPropNames: PlasmicColorTokenControl__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicColorTokenControl__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicColorTokenControl";
  } else {
    func.displayName = `PlasmicColorTokenControl.${nodeName}`;
  }
  return func;
}

export const PlasmicColorTokenControl = Object.assign(
  // Top-level PlasmicColorTokenControl renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    rowItem: makeNodeComponent("rowItem"),

    // Metadata about props expected for PlasmicColorTokenControl
    internalVariantProps: PlasmicColorTokenControl__VariantProps,
    internalArgProps: PlasmicColorTokenControl__ArgProps,
  }
);

export default PlasmicColorTokenControl;
/* prettier-ignore-end */
