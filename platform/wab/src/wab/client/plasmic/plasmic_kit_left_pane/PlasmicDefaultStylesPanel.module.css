.root {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  max-width: 100%;
  position: relative;
  min-width: 0;
  min-height: 0;
}
.rootisTargeting {
  background: var(--token-htSNbGB58Rx);
}
.text__iGLd {
  padding: 16px;
}
.selector {
  display: flex;
  position: relative;
  flex-direction: column;
  width: 100%;
  row-gap: 8px;
  min-width: 0;
  padding: 0px 16px 16px;
  border-bottom: 1px solid var(--token-hoA5qaM-91G);
}
.freeBox {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
}
.text__amsGc {
  font-weight: 500;
}
.globalVariantSelectorContainer {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  width: auto;
  height: auto;
  max-width: 100%;
  column-gap: 4px;
}
.text___1UBnM {
  position: relative;
  width: 100%;
  height: auto;
  max-width: 800px;
  min-width: 0;
}
.textisTargeting___1UBnMog7Mw {
  color: var(--token-WnbI6EOoey2);
  font-weight: 500;
}
.svg__qOj9X {
  position: relative;
  object-fit: cover;
  min-width: 16px;
  min-height: 16px;
  width: 16px;
  height: 16px;
}
.text__oqmBw {
  position: relative;
}
.option:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.text__jSaSm {
  line-height: 2;
  padding-right: 0px;
  margin: 0px;
}
.tagSelect:global(.__wab_instance) {
  position: relative;
}
.svg__kRhm1 {
  position: relative;
  object-fit: cover;
  min-width: 16px;
  min-height: 16px;
  width: 16px;
  height: 16px;
}
.option__xD8QO:global(.__wab_instance) {
  position: relative;
}
.option__m7Bm7:global(.__wab_instance) {
  position: relative;
}
.optionGroup___2CNjT:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.option__riEgq:global(.__wab_instance) {
  position: relative;
}
.option__qzL:global(.__wab_instance) {
  position: relative;
}
.optionGroup__qxNfa:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.option__dp5Wj:global(.__wab_instance) {
  position: relative;
}
.option__rlwzo:global(.__wab_instance) {
  position: relative;
}
.option___3FzRf:global(.__wab_instance) {
  position: relative;
}
.pseudoClassSelect:global(.__wab_instance) {
  position: relative;
}
.svg__iMz8C {
  position: relative;
  object-fit: cover;
  min-width: 16px;
  min-height: 16px;
  width: 16px;
  height: 16px;
}
.option__yEjPg:global(.__wab_instance) {
  position: relative;
}
.option__b7NO0:global(.__wab_instance) {
  position: relative;
}
.optionGroup__apm4T:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.option__xlBm7:global(.__wab_instance) {
  position: relative;
}
.option___5KdEf:global(.__wab_instance) {
  position: relative;
}
.optionGroup__hQmds:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.option___1KlAw:global(.__wab_instance) {
  position: relative;
}
.option__wwOwp:global(.__wab_instance) {
  position: relative;
}
.option__smoae:global(.__wab_instance) {
  position: relative;
}
.content {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  flex-grow: 1;
  overflow: auto;
  min-width: 0;
}
