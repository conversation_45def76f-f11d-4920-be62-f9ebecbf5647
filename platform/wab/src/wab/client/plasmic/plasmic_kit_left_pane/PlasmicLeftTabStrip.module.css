.root {
  display: inline-flex;
  width: auto;
  height: 100%;
  position: relative;
  flex-direction: column;
  background: var(--token-iR8SeEwQZ);
  overflow: auto;
  pointer-events: auto;
  row-gap: 4px;
  min-height: 0;
  padding: 8px;
  border-right: 1px solid var(--token-hoA5qaM-91G);
}
.insert:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  margin-bottom: 10px;
}
.freeBox__s2Zn2 {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  max-width: 100%;
  min-width: 0;
  min-height: 0;
  padding: 0px;
}
.freeBox___5Ah7Y {
  display: flex;
  flex-direction: column;
  position: relative;
  align-items: center;
  justify-content: flex-start;
}
.leftTabButton__xnfKt:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
}
.svg__fJuLy {
  position: relative;
  object-fit: cover;
  width: 24px;
  height: 24px;
}
.leftTabButton__ro6Iy:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
}
.svg__rJYem {
  position: relative;
  object-fit: cover;
  height: 1em;
}
.leftTabButton__uOnAi:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
}
.svg__p2MgB {
  position: relative;
  object-fit: cover;
  width: 24px;
  height: 24px;
}
.leftTabButton___8X6Fn:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
}
.svg__qPkMk {
  position: relative;
  object-fit: cover;
  width: 24px;
  height: 24px;
}
.leftTabButton___5AwUj:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
}
.svg___7Cd9B {
  position: relative;
  object-fit: cover;
  width: 24px;
  height: 24px;
}
.freeBox__qcGiE {
  display: flex;
  flex-direction: column;
  position: relative;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  min-width: 0;
}
.leftTabButton__qvJoE:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
}
.svg__bhbR5 {
  position: relative;
  object-fit: cover;
  width: 24px;
  height: 24px;
}
.divider {
  display: block;
  position: relative;
  background: var(--token-hoA5qaM-91G);
  height: 1px;
  flex-shrink: 0;
  margin: 8px;
}
.copilot:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
  display: none;
}
.copilotactiveTab_outline:global(.__wab_instance) {
  flex-shrink: 0;
}
.copilotactiveTab_endpoints:global(.__wab_instance) {
  flex-shrink: 0;
}
.copilotactiveTab_copilot:global(.__wab_instance) {
  flex-shrink: 0;
}
.copilotshowAvatar:global(.__wab_instance) {
  flex-shrink: 0;
}
.copilotwithInsertButton:global(.__wab_instance) {
  flex-shrink: 0;
}
.svg__wujwR {
  position: relative;
  object-fit: cover;
  height: 1em;
}
.tokens:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
  display: none;
}
.tokensactiveTab_tokens:global(.__wab_instance) {
  flex-shrink: 0;
}
.tokensactiveTab_mixins:global(.__wab_instance) {
  flex-shrink: 0;
}
.tokensactiveTab_copilot:global(.__wab_instance) {
  flex-shrink: 0;
}
.tokenswithInsertButton:global(.__wab_instance) {
  flex-shrink: 0;
}
.svg__mn5Uf {
  position: relative;
  object-fit: cover;
  height: 1em;
}
.mixins:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
  display: none;
}
.mixinsactiveTab_mixins:global(.__wab_instance) {
  flex-shrink: 0;
}
.mixinsactiveTab_copilot:global(.__wab_instance) {
  flex-shrink: 0;
}
.svg__nxYe {
  position: relative;
  object-fit: cover;
  height: 1em;
}
.components:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
  display: none;
}
.componentsactiveTab_components:global(.__wab_instance) {
  flex-shrink: 0;
}
.componentsactiveTab_images:global(.__wab_instance) {
  flex-shrink: 0;
}
.componentsactiveTab_pages:global(.__wab_instance) {
  flex-shrink: 0;
}
.svg__kca03 {
  position: relative;
  object-fit: cover;
  height: 1em;
}
.pages:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
  display: none;
}
.pagesactiveTab_components:global(.__wab_instance) {
  flex-shrink: 0;
}
.pagesactiveTab_images:global(.__wab_instance) {
  flex-shrink: 0;
}
.pagesactiveTab_pages:global(.__wab_instance) {
  flex-shrink: 0;
}
.svg__g5Iby {
  position: relative;
  object-fit: cover;
  width: 24px;
  height: 24px;
}
.images:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
  display: none;
}
.imagesactiveTab_images:global(.__wab_instance) {
  flex-shrink: 0;
}
.imagesactiveTab_themes:global(.__wab_instance) {
  flex-shrink: 0;
}
.svg___75XmC {
  position: relative;
  object-fit: cover;
  height: 1em;
}
.themes:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
  display: none;
}
.themesactiveTab_themes:global(.__wab_instance) {
  flex-shrink: 0;
}
.themesactiveTab_fonts:global(.__wab_instance) {
  flex-shrink: 0;
}
.svg___15AOq {
  position: relative;
  object-fit: cover;
  width: 24px;
  height: 24px;
}
.fonts:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
  display: none;
}
.fontsactiveTab_fonts:global(.__wab_instance) {
  flex-shrink: 0;
}
.svg__odrDh {
  position: relative;
  object-fit: cover;
  width: 24px;
  height: 24px;
}
.responsiveness:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
  display: none;
}
.responsivenessactiveTab_fonts:global(.__wab_instance) {
  flex-shrink: 0;
}
.responsivenessactiveTab_pages:global(.__wab_instance) {
  flex-shrink: 0;
}
.responsivenessactiveTab_responsiveness:global(.__wab_instance) {
  flex-shrink: 0;
}
.svg__qjH4W {
  position: relative;
  object-fit: cover;
  height: 1em;
}
.splits:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
  display: none;
}
.splitsactiveTab_fonts:global(.__wab_instance) {
  flex-shrink: 0;
}
.splitsactiveTab_pages:global(.__wab_instance) {
  flex-shrink: 0;
}
.splitsactiveTab_responsiveness:global(.__wab_instance) {
  flex-shrink: 0;
}
.splitsactiveTab_splits:global(.__wab_instance) {
  flex-shrink: 0;
}
.svg___2ZAk3 {
  position: relative;
  object-fit: cover;
  width: 24px;
  height: 24px;
}
.imports:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
  display: none;
}
.importsactiveTab_fonts:global(.__wab_instance) {
  flex-shrink: 0;
}
.importsactiveTab_imports:global(.__wab_instance) {
  flex-shrink: 0;
}
.importsactiveTab_versions:global(.__wab_instance) {
  flex-shrink: 0;
}
.svg__c7UmG {
  position: relative;
  object-fit: cover;
  width: 24px;
  height: 24px;
}
.versions:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
  display: none;
}
.versionsactiveTab_versions:global(.__wab_instance) {
  flex-shrink: 0;
}
.versionsshowAvatar:global(.__wab_instance) {
  flex-shrink: 0;
}
.svg__pfWid {
  position: relative;
  object-fit: cover;
  height: 1em;
}
.settings:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
  display: none;
}
.settingsactiveTab_settings:global(.__wab_instance) {
  flex-shrink: 0;
}
.svg__kWpBc {
  position: relative;
  object-fit: cover;
  height: 1em;
}
.endpoints:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  flex-shrink: 0;
  display: none;
}
.endpointsactiveTab_settings:global(.__wab_instance) {
  flex-shrink: 0;
}
.endpointsactiveTab_endpoints:global(.__wab_instance) {
  flex-shrink: 0;
}
.svg__sZo1B {
  position: relative;
  object-fit: cover;
  height: 1em;
}
.freeBox___3AubH {
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: space-evenly;
  margin-top: auto;
  margin-bottom: 8px;
  row-gap: 4px;
  display: none;
}
.figma:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
}
.figmashowAvatar:global(.__wab_instance) {
  flex-shrink: 0;
}
.svg__r87Op {
  display: flex;
  position: relative;
  object-fit: cover;
  height: 1em;
}
.svg__gjOa1 {
  display: flex;
  position: relative;
  object-fit: cover;
  color: var(--token-UunsGa2Y3t3);
  height: 1em;
}
.keyboard:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
}
.keyboardshowAvatar:global(.__wab_instance) {
  flex-shrink: 0;
}
.svg__isDmf {
  display: flex;
  position: relative;
  object-fit: cover;
  height: 1em;
}
.svg__zTmmf {
  display: flex;
  position: relative;
  object-fit: cover;
  color: var(--token-UunsGa2Y3t3);
  height: 1em;
}
.slack:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
}
.slackshowAvatar:global(.__wab_instance) {
  flex-shrink: 0;
}
.freeBox__hJJr6 {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  min-width: 0;
  padding: 2px;
}
.svg__pqmVd {
  display: flex;
  object-fit: cover;
  height: 1em;
  flex-shrink: 0;
}
.svg__rPpbx {
  display: flex;
  position: relative;
  object-fit: cover;
  color: var(--token-UunsGa2Y3t3);
  height: 1em;
}
.help:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
}
.helpshowAvatar:global(.__wab_instance) {
  flex-shrink: 0;
}
.svg__tlcC1 {
  display: flex;
  position: relative;
  object-fit: cover;
  height: 1em;
}
.svg__t8IHm {
  display: flex;
  position: relative;
  object-fit: cover;
  color: var(--token-UunsGa2Y3t3);
  height: 1em;
}
.freeBox__igmvF {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  flex-shrink: 0;
  min-width: 0;
  padding: 0px;
}
.freeBoxshowAvatar__igmvF9NVfe {
  margin-top: 2px;
  margin-bottom: 2px;
}
.players {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
}
.playersshowAvatar {
  width: 28px;
  justify-content: flex-start;
  align-items: center;
  align-content: unset;
}
.img__fZbIh {
  position: relative;
  object-fit: cover;
}
.img__fZbIh > picture > img {
  object-fit: cover;
}
.imgshowAvatar__fZbIh9NVfe {
  width: 24px;
  height: 24px;
  margin-top: 2px;
  margin-bottom: 2px;
  flex-shrink: 0;
  border-radius: 50%;
  border: 2px solid var(--token-e370WsoPH8cE);
}
.svg__jFq8B {
  position: relative;
  object-fit: cover;
  height: 1em;
  flex-shrink: 0;
}
.svgshowAvatar__jFq8B9NVfe {
  width: 24px;
  height: 24px;
  margin-top: 2px;
  margin-bottom: 2px;
  flex-shrink: 0;
  border-radius: 50%;
  border: 2px solid var(--token-LmrMFZok-p9);
}
.img__hptuc {
  position: relative;
  object-fit: cover;
}
.img__hptuc > picture > img {
  object-fit: cover;
}
.imgshowAvatar__hptuc9NVfe {
  width: 24px;
  height: 24px;
  margin-top: 2px;
  margin-bottom: 2px;
  flex-shrink: 0;
  border-radius: 50%;
  border: 2px solid var(--token-DEbwNasuLfjs);
}
.img__l76QL {
  position: relative;
  object-fit: cover;
}
.img__l76QL > picture > img {
  object-fit: cover;
}
.imgshowAvatar__l76QL9NVfe {
  width: 24px;
  height: 24px;
  margin-top: 2px;
  margin-bottom: 2px;
  flex-shrink: 0;
  border-radius: 50%;
  border: 2px solid var(--token-49yXl5zB7c3);
}
.avatar {
  object-fit: cover;
}
.avatar > picture > img {
  object-fit: cover;
}
.avatarshowAvatar {
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}
