/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: aukbrhkegRkQ6KizvhdUPT
// Component: kZ3Ar3RnLt

import * as React from "react";

import {
  Flex as Flex__,
  SingleBooleanChoiceArg,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  hasVariant,
  renderPlasmicSlot,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import Preset from "../../components/Preset"; // plasmic-import: eS_Bw5U3wr/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_design_system_deprecated_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "../PP__plasmickit_left_pane.module.css"; // plasmic-import: aukbrhkegRkQ6KizvhdUPT/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_new_design_system_former_style_controls_css from "../plasmic_kit_style_controls/plasmic_plasmic_kit_styles_pane.module.css"; // plasmic-import: gYEVvAzCcLMHDVPvuYxkFh/projectcss
import sty from "./PlasmicPresetGroup.module.css"; // plasmic-import: kZ3Ar3RnLt/css

createPlasmicElementProxy;

export type PlasmicPresetGroup__VariantMembers = {
  hideGroupName: "hideGroupName";
};
export type PlasmicPresetGroup__VariantsArgs = {
  hideGroupName?: SingleBooleanChoiceArg<"hideGroupName">;
};
type VariantPropType = keyof PlasmicPresetGroup__VariantsArgs;
export const PlasmicPresetGroup__VariantProps = new Array<VariantPropType>(
  "hideGroupName"
);

export type PlasmicPresetGroup__ArgsType = {
  name?: React.ReactNode;
  children?: React.ReactNode;
};
type ArgPropType = keyof PlasmicPresetGroup__ArgsType;
export const PlasmicPresetGroup__ArgProps = new Array<ArgPropType>(
  "name",
  "children"
);

export type PlasmicPresetGroup__OverridesType = {
  root?: Flex__<"div">;
  freeBox?: Flex__<"div">;
};

export interface DefaultPresetGroupProps {
  name?: React.ReactNode;
  children?: React.ReactNode;
  hideGroupName?: SingleBooleanChoiceArg<"hideGroupName">;
  className?: string;
}

const $$ = {};

function PlasmicPresetGroup__RenderFunc(props: {
  variants: PlasmicPresetGroup__VariantsArgs;
  args: PlasmicPresetGroup__ArgsType;
  overrides: PlasmicPresetGroup__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "hideGroupName",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.hideGroupName,
      },
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_deprecated_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        plasmic_plasmic_kit_new_design_system_former_style_controls_css.plasmic_tokens,
        sty.root,
        {
          [sty.roothideGroupName]: hasVariant(
            $state,
            "hideGroupName",
            "hideGroupName"
          ),
        }
      )}
    >
      {(hasVariant($state, "hideGroupName", "hideGroupName") ? false : true) ? (
        <div
          data-plasmic-name={"freeBox"}
          data-plasmic-override={overrides.freeBox}
          className={classNames(projectcss.all, sty.freeBox, {
            [sty.freeBoxhideGroupName]: hasVariant(
              $state,
              "hideGroupName",
              "hideGroupName"
            ),
          })}
        >
          {renderPlasmicSlot({
            defaultContents: "Default",
            value: args.name,
            className: classNames(sty.slotTargetName, {
              [sty.slotTargetNamehideGroupName]: hasVariant(
                $state,
                "hideGroupName",
                "hideGroupName"
              ),
            }),
          })}
        </div>
      ) : null}
      {renderPlasmicSlot({
        defaultContents: (
          <React.Fragment>
            <Preset
              className={classNames("__wab_instance", sty.preset__kuBfd)}
              screenshot={
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__uIv8N
                  )}
                >
                  {"Enter some text"}
                </div>
              }
            />

            <Preset
              className={classNames("__wab_instance", sty.preset__aVsCj)}
              screenshot={
                <div
                  className={classNames(
                    projectcss.all,
                    projectcss.__wab_text,
                    sty.text__xxvsv
                  )}
                >
                  {"Enter some text"}
                </div>
              }
            />
          </React.Fragment>
        ),
        value: args.children,
      })}
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "freeBox"],
  freeBox: ["freeBox"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  freeBox: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicPresetGroup__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicPresetGroup__VariantsArgs;
    args?: PlasmicPresetGroup__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicPresetGroup__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicPresetGroup__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicPresetGroup__ArgProps,
          internalVariantPropNames: PlasmicPresetGroup__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicPresetGroup__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicPresetGroup";
  } else {
    func.displayName = `PlasmicPresetGroup.${nodeName}`;
  }
  return func;
}

export const PlasmicPresetGroup = Object.assign(
  // Top-level PlasmicPresetGroup renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    freeBox: makeNodeComponent("freeBox"),

    // Metadata about props expected for PlasmicPresetGroup
    internalVariantProps: PlasmicPresetGroup__VariantProps,
    internalArgProps: PlasmicPresetGroup__ArgProps,
  }
);

export default PlasmicPresetGroup;
/* prettier-ignore-end */
