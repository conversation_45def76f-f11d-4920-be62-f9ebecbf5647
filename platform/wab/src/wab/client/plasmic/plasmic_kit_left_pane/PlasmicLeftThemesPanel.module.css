.root {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
  flex-direction: column;
  overflow: hidden;
  background: #ffffff;
  min-width: 0;
  min-height: 0;
}
.themeHeader:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
}
.textWithInfo:global(.__wab_instance) {
  max-width: 100%;
}
.text__sLPev {
  font-weight: 600;
  font-size: 14px;
}
.freeBox {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: baseline;
  justify-content: flex-start;
  width: 100%;
  height: auto;
  max-width: 100%;
  column-gap: 4px;
  min-width: 0;
}
.text__rgfqo {
  position: relative;
  width: auto;
  height: auto;
  max-width: 800px;
  white-space: pre;
}
.themeSelector:global(.__wab_instance) {
  max-width: 100%;
  width: 100%;
  min-width: 0;
}
.hiliteTabs:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  width: 100%;
  min-width: 0;
}
.hiliteTabsnoLayout:global(.__wab_instance) {
  display: none;
}
.defaultStylesPanel:global(.__wab_instance) {
  position: relative;
  display: none;
}
.defaultStylesPanelnoLayout:global(.__wab_instance) {
  display: flex;
}
.defaultStylesPaneltab_styles:global(.__wab_instance) {
  display: flex;
}
.themeLayoutPanel:global(.__wab_instance) {
  position: relative;
  display: none;
}
.themeLayoutPaneltab_layout:global(.__wab_instance) {
  display: flex;
}
.themeInitialStylesPanel:global(.__wab_instance) {
  max-width: 100%;
  position: relative;
  display: none;
}
.themeInitialStylesPaneltab_initials:global(.__wab_instance) {
  display: flex;
}
