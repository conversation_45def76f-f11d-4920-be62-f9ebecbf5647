/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: aukbrhkegRkQ6KizvhdUPT
// Component: 0LQGzuFK6d

import * as React from "react";

import {
  Flex as Flex__,
  SingleBooleanChoiceArg,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  hasVariant,
  renderPlasmicSlot,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import RowItem from "../../components/RowItem"; // plasmic-import: gkx-PRZnjFPo/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_design_system_deprecated_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "../PP__plasmickit_left_pane.module.css"; // plasmic-import: aukbrhkegRkQ6KizvhdUPT/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_new_design_system_former_style_controls_css from "../plasmic_kit_style_controls/plasmic_plasmic_kit_styles_pane.module.css"; // plasmic-import: gYEVvAzCcLMHDVPvuYxkFh/projectcss
import sty from "./PlasmicGeneralTokenControl.module.css"; // plasmic-import: 0LQGzuFK6d/css

import ComponentsSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ComponentsSvg"; // plasmic-import: coPzxnFyi/icon

createPlasmicElementProxy;

export type PlasmicGeneralTokenControl__VariantMembers = {
  showIcon: "showIcon";
};
export type PlasmicGeneralTokenControl__VariantsArgs = {
  showIcon?: SingleBooleanChoiceArg<"showIcon">;
};
type VariantPropType = keyof PlasmicGeneralTokenControl__VariantsArgs;
export const PlasmicGeneralTokenControl__VariantProps =
  new Array<VariantPropType>("showIcon");

export type PlasmicGeneralTokenControl__ArgsType = {
  children?: React.ReactNode;
  value?: React.ReactNode;
};
type ArgPropType = keyof PlasmicGeneralTokenControl__ArgsType;
export const PlasmicGeneralTokenControl__ArgProps = new Array<ArgPropType>(
  "children",
  "value"
);

export type PlasmicGeneralTokenControl__OverridesType = {
  root?: Flex__<"div">;
  rowItem?: Flex__<typeof RowItem>;
  svg?: Flex__<"svg">;
};

export interface DefaultGeneralTokenControlProps {
  children?: React.ReactNode;
  value?: React.ReactNode;
  showIcon?: SingleBooleanChoiceArg<"showIcon">;
  className?: string;
}

const $$ = {};

function PlasmicGeneralTokenControl__RenderFunc(props: {
  variants: PlasmicGeneralTokenControl__VariantsArgs;
  args: PlasmicGeneralTokenControl__ArgsType;
  overrides: PlasmicGeneralTokenControl__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "showIcon",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.showIcon,
      },
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_deprecated_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        plasmic_plasmic_kit_new_design_system_former_style_controls_css.plasmic_tokens,
        sty.root
      )}
    >
      <RowItem
        data-plasmic-name={"rowItem"}
        data-plasmic-override={overrides.rowItem}
        addendum={renderPlasmicSlot({
          defaultContents: "Big  Addendum",
          value: args.value,
        })}
        className={classNames("__wab_instance", sty.rowItem, {
          [sty.rowItemshowIcon]: hasVariant($state, "showIcon", "showIcon"),
        })}
        hideIcon={hasVariant($state, "showIcon", "showIcon") ? undefined : true}
        icon={
          <ComponentsSvgIcon
            data-plasmic-name={"svg"}
            data-plasmic-override={overrides.svg}
            className={classNames(projectcss.all, sty.svg)}
            role={"img"}
          />
        }
        menuSize={"small"}
        showAddendum={true}
      >
        {renderPlasmicSlot({
          defaultContents: "Some very long item label",
          value: args.children,
        })}
      </RowItem>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "rowItem", "svg"],
  rowItem: ["rowItem", "svg"],
  svg: ["svg"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  rowItem: typeof RowItem;
  svg: "svg";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicGeneralTokenControl__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicGeneralTokenControl__VariantsArgs;
    args?: PlasmicGeneralTokenControl__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicGeneralTokenControl__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicGeneralTokenControl__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicGeneralTokenControl__ArgProps,
          internalVariantPropNames: PlasmicGeneralTokenControl__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicGeneralTokenControl__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicGeneralTokenControl";
  } else {
    func.displayName = `PlasmicGeneralTokenControl.${nodeName}`;
  }
  return func;
}

export const PlasmicGeneralTokenControl = Object.assign(
  // Top-level PlasmicGeneralTokenControl renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    rowItem: makeNodeComponent("rowItem"),
    svg: makeNodeComponent("svg"),

    // Metadata about props expected for PlasmicGeneralTokenControl
    internalVariantProps: PlasmicGeneralTokenControl__VariantProps,
    internalArgProps: PlasmicGeneralTokenControl__ArgProps,
  }
);

export default PlasmicGeneralTokenControl;
/* prettier-ignore-end */
