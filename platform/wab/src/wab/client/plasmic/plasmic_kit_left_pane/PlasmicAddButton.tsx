/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: aukbrhkegRkQ6KizvhdUPT
// Component: ss1yYyG4Pi

import * as React from "react";

import {
  Flex as Flex__,
  SingleBooleanChoiceArg,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  hasVariant,
  useDollarState,
  useTrigger,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_design_system_deprecated_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "../PP__plasmickit_left_pane.module.css"; // plasmic-import: aukbrhkegRkQ6KizvhdUPT/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_new_design_system_former_style_controls_css from "../plasmic_kit_style_controls/plasmic_plasmic_kit_styles_pane.module.css"; // plasmic-import: gYEVvAzCcLMHDVPvuYxkFh/projectcss
import sty from "./PlasmicAddButton.module.css"; // plasmic-import: ss1yYyG4Pi/css

import PlusSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__PlusSvg"; // plasmic-import: sQKgd2GNr/icon

createPlasmicElementProxy;

export type PlasmicAddButton__VariantMembers = {
  isActive: "isActive";
};
export type PlasmicAddButton__VariantsArgs = {
  isActive?: SingleBooleanChoiceArg<"isActive">;
};
type VariantPropType = keyof PlasmicAddButton__VariantsArgs;
export const PlasmicAddButton__VariantProps = new Array<VariantPropType>(
  "isActive"
);

export type PlasmicAddButton__ArgsType = { onClick?: () => void };
type ArgPropType = keyof PlasmicAddButton__ArgsType;
export const PlasmicAddButton__ArgProps = new Array<ArgPropType>("onClick");

export type PlasmicAddButton__OverridesType = {
  root?: Flex__<"button">;
  svg?: Flex__<"svg">;
  focusRing?: Flex__<"div">;
};

export interface DefaultAddButtonProps {
  onClick?: () => void;
  isActive?: SingleBooleanChoiceArg<"isActive">;
  className?: string;
}

const $$ = {};

function PlasmicAddButton__RenderFunc(props: {
  variants: PlasmicAddButton__VariantsArgs;
  args: PlasmicAddButton__ArgsType;
  overrides: PlasmicAddButton__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "isActive",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.isActive,
      },
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  const [isRootFocusVisible, triggerRootFocusVisibleProps] = useTrigger(
    "useFocusVisible",
    {
      isTextInput: false,
    }
  );
  const triggers = {
    focusVisible_root: isRootFocusVisible,
  };

  return (
    <button
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.button,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_deprecated_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        plasmic_plasmic_kit_new_design_system_former_style_controls_css.plasmic_tokens,
        sty.root,
        {
          [sty.root___focusVisible]: triggers.focusVisible_root,
          [sty.rootisActive]: hasVariant($state, "isActive", "isActive"),
        }
      )}
      data-test-id={"add-button"}
      onClick={args.onClick}
      data-plasmic-trigger-props={[triggerRootFocusVisibleProps]}
    >
      <PlusSvgIcon
        data-plasmic-name={"svg"}
        data-plasmic-override={overrides.svg}
        className={classNames(projectcss.all, sty.svg, {
          [sty.svg___focusVisible]: triggers.focusVisible_root,
        })}
        role={"img"}
      />

      {(triggers.focusVisible_root ? true : false) ? (
        <div
          data-plasmic-name={"focusRing"}
          data-plasmic-override={overrides.focusRing}
          className={classNames(projectcss.all, sty.focusRing, {
            [sty.focusRing___focusVisible]: triggers.focusVisible_root,
          })}
        />
      ) : null}
    </button>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "svg", "focusRing"],
  svg: ["svg"],
  focusRing: ["focusRing"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "button";
  svg: "svg";
  focusRing: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicAddButton__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicAddButton__VariantsArgs;
    args?: PlasmicAddButton__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicAddButton__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicAddButton__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicAddButton__ArgProps,
          internalVariantPropNames: PlasmicAddButton__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicAddButton__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicAddButton";
  } else {
    func.displayName = `PlasmicAddButton.${nodeName}`;
  }
  return func;
}

export const PlasmicAddButton = Object.assign(
  // Top-level PlasmicAddButton renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    svg: makeNodeComponent("svg"),
    focusRing: makeNodeComponent("focusRing"),

    // Metadata about props expected for PlasmicAddButton
    internalVariantProps: PlasmicAddButton__VariantProps,
    internalArgProps: PlasmicAddButton__ArgProps,
  }
);

export default PlasmicAddButton;
/* prettier-ignore-end */
