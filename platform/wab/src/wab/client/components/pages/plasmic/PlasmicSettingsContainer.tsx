/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: aaggSgVS8yYsAwQffVQB4p
// Component: XkSd43CUYOB

import * as React from "react";

import {
  Flex as Flex__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  hasVariant,
  renderPlasmicSlot,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import TrustedHost from "../../TrustedHost"; // plasmic-import: 0O5nMBdoCe/component
import Button from "../../widgets/Button"; // plasmic-import: SEF-sRmSoqV5c/component
import MenuButton from "../../widgets/MenuButton"; // plasmic-import: h69wHrrKtL/component
import PersonalAccessToken from "./PersonalAccessToken"; // plasmic-import: F4ZVtfq6Xg/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_design_system_css from "../../../plasmic/PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "../../../plasmic/PP__plasmickit_settings.module.css"; // plasmic-import: aaggSgVS8yYsAwQffVQB4p/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../../../plasmic/plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import sty from "./PlasmicSettingsContainer.module.css"; // plasmic-import: XkSd43CUYOB/css

import image3YherfIxkolNxf from "../../../plasmic/plasmic_kit_design_system/images/image3.svg"; // plasmic-import: yherfIxkolNXF/picture
import PlusSvgIcon from "../../../plasmic/plasmic_kit_icons/icons/PlasmicIcon__PlusSvg"; // plasmic-import: sQKgd2GNr/icon

createPlasmicElementProxy;

export type PlasmicSettingsContainer__VariantMembers = {
  tokenState: "loading" | "loaded" | "error";
  trustedHostsState: "enabled" | "loading" | "error";
  hideChangePassword: "hideChangePassword";
};
export type PlasmicSettingsContainer__VariantsArgs = {
  tokenState?: SingleChoiceArg<"loading" | "loaded" | "error">;
  trustedHostsState?: SingleChoiceArg<"enabled" | "loading" | "error">;
  hideChangePassword?: SingleBooleanChoiceArg<"hideChangePassword">;
};
type VariantPropType = keyof PlasmicSettingsContainer__VariantsArgs;
export const PlasmicSettingsContainer__VariantProps =
  new Array<VariantPropType>(
    "tokenState",
    "trustedHostsState",
    "hideChangePassword"
  );

export type PlasmicSettingsContainer__ArgsType = {
  avatarImgUrl?: string;
  name?: React.ReactNode;
  email?: React.ReactNode;
  role?: React.ReactNode;
};
type ArgPropType = keyof PlasmicSettingsContainer__ArgsType;
export const PlasmicSettingsContainer__ArgProps = new Array<ArgPropType>(
  "avatarImgUrl",
  "name",
  "email",
  "role"
);

export type PlasmicSettingsContainer__OverridesType = {
  root?: Flex__<"div">;
  img?: Flex__<"img">;
  menuButton?: Flex__<typeof MenuButton>;
  changePasswordButton?: Flex__<typeof Button>;
  newTokenButton?: Flex__<typeof Button>;
  existingTokens?: Flex__<"div">;
  tokenInstance?: Flex__<typeof PersonalAccessToken>;
  personalAccessToken?: Flex__<typeof PersonalAccessToken>;
  newTrustedHostBtn?: Flex__<typeof Button>;
  hostsList?: Flex__<"div">;
};

export interface DefaultSettingsContainerProps {
  avatarImgUrl?: string;
  name?: React.ReactNode;
  email?: React.ReactNode;
  role?: React.ReactNode;
  tokenState?: SingleChoiceArg<"loading" | "loaded" | "error">;
  trustedHostsState?: SingleChoiceArg<"enabled" | "loading" | "error">;
  hideChangePassword?: SingleBooleanChoiceArg<"hideChangePassword">;
  className?: string;
}

const $$ = {};

function PlasmicSettingsContainer__RenderFunc(props: {
  variants: PlasmicSettingsContainer__VariantsArgs;
  args: PlasmicSettingsContainer__ArgsType;
  overrides: PlasmicSettingsContainer__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "tokenState",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.tokenState,
      },
      {
        path: "trustedHostsState",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.trustedHostsState,
      },
      {
        path: "hideChangePassword",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) =>
          $props.hideChangePassword,
      },
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        sty.root,
        {
          [sty.roottokenState_error]: hasVariant($state, "tokenState", "error"),
          [sty.roottokenState_loading]: hasVariant(
            $state,
            "tokenState",
            "loading"
          ),
          [sty.roottrustedHostsState_enabled]: hasVariant(
            $state,
            "trustedHostsState",
            "enabled"
          ),
          [sty.roottrustedHostsState_error]: hasVariant(
            $state,
            "trustedHostsState",
            "error"
          ),
          [sty.roottrustedHostsState_loading]: hasVariant(
            $state,
            "trustedHostsState",
            "loading"
          ),
        }
      )}
    >
      <div
        className={classNames(
          projectcss.all,
          projectcss.__wab_text,
          sty.text__mqBhA,
          {
            [sty.texttokenState_loading__mqBhAmpB4V]: hasVariant(
              $state,
              "tokenState",
              "loading"
            ),
            [sty.texttrustedHostsState_enabled__mqBhA8K6RB]: hasVariant(
              $state,
              "trustedHostsState",
              "enabled"
            ),
          }
        )}
      >
        {"Profile settings"}
      </div>
      <div
        className={classNames(projectcss.all, sty.freeBox__vlwnM, {
          [sty.freeBoxtokenState_error__vlwnMa2Ye]: hasVariant(
            $state,
            "tokenState",
            "error"
          ),
          [sty.freeBoxtokenState_loaded__vlwnManNqw]: hasVariant(
            $state,
            "tokenState",
            "loaded"
          ),
          [sty.freeBoxtokenState_loading__vlwnMmpB4V]: hasVariant(
            $state,
            "tokenState",
            "loading"
          ),
          [sty.freeBoxtrustedHostsState_enabled__vlwnM8K6RB]: hasVariant(
            $state,
            "trustedHostsState",
            "enabled"
          ),
          [sty.freeBoxtrustedHostsState_error__vlwnM7WksN]: hasVariant(
            $state,
            "trustedHostsState",
            "error"
          ),
          [sty.freeBoxtrustedHostsState_loading__vlwnMqBk4M]: hasVariant(
            $state,
            "trustedHostsState",
            "loading"
          ),
        })}
      >
        <div
          className={classNames(projectcss.all, sty.freeBox__mhSrh, {
            [sty.freeBoxhideChangePassword__mhSrhT898]: hasVariant(
              $state,
              "hideChangePassword",
              "hideChangePassword"
            ),
            [sty.freeBoxtokenState_loaded__mhSrhanNqw]: hasVariant(
              $state,
              "tokenState",
              "loaded"
            ),
          })}
        >
          <div
            className={classNames(projectcss.all, sty.freeBox__nPrWc, {
              [sty.freeBoxhideChangePassword__nPrWcT898]: hasVariant(
                $state,
                "hideChangePassword",
                "hideChangePassword"
              ),
              [sty.freeBoxtokenState_loaded__nPrWcanNqw]: hasVariant(
                $state,
                "tokenState",
                "loaded"
              ),
            })}
          >
            <img
              data-plasmic-name={"img"}
              data-plasmic-override={overrides.img}
              alt={""}
              className={classNames(projectcss.all, projectcss.img, sty.img)}
              src={image3YherfIxkolNxf}
            />

            <div className={classNames(projectcss.all, sty.freeBox__piPA)}>
              {renderPlasmicSlot({
                defaultContents: "Kimberly Schmidt",
                value: args.name,
                className: classNames(sty.slotTargetName),
              })}
              {renderPlasmicSlot({
                defaultContents: "<EMAIL>",
                value: args.email,
                className: classNames(sty.slotTargetEmail),
              })}
              {false
                ? renderPlasmicSlot({
                    defaultContents: "Designer",
                    value: args.role,
                    className: classNames(sty.slotTargetRole),
                  })
                : null}
            </div>
          </div>
          <div className={classNames(projectcss.all, sty.freeBox__mdyOy)}>
            <MenuButton
              data-plasmic-name={"menuButton"}
              data-plasmic-override={overrides.menuButton}
              className={classNames("__wab_instance", sty.menuButton)}
            />

            <Button
              data-plasmic-name={"changePasswordButton"}
              data-plasmic-override={overrides.changePasswordButton}
              className={classNames(
                "__wab_instance",
                sty.changePasswordButton,
                {
                  [sty.changePasswordButtonhideChangePassword]: hasVariant(
                    $state,
                    "hideChangePassword",
                    "hideChangePassword"
                  ),
                  [sty.changePasswordButtontokenState_error]: hasVariant(
                    $state,
                    "tokenState",
                    "error"
                  ),
                  [sty.changePasswordButtontokenState_loaded]: hasVariant(
                    $state,
                    "tokenState",
                    "loaded"
                  ),
                  [sty.changePasswordButtontokenState_loading]: hasVariant(
                    $state,
                    "tokenState",
                    "loading"
                  ),
                  [sty.changePasswordButtontrustedHostsState_enabled]:
                    hasVariant($state, "trustedHostsState", "enabled"),
                  [sty.changePasswordButtontrustedHostsState_error]: hasVariant(
                    $state,
                    "trustedHostsState",
                    "error"
                  ),
                  [sty.changePasswordButtontrustedHostsState_loading]:
                    hasVariant($state, "trustedHostsState", "loading"),
                }
              )}
              size={"wide"}
            >
              {"Change password"}
            </Button>
          </div>
        </div>
        <div
          className={classNames(projectcss.all, sty.freeBox__wYsKg, {
            [sty.freeBoxtrustedHostsState_enabled__wYsKg8K6RB]: hasVariant(
              $state,
              "trustedHostsState",
              "enabled"
            ),
          })}
        />

        <div
          className={classNames(projectcss.all, sty.freeBox__amKmj, {
            [sty.freeBoxtokenState_error__amKmJa2Ye]: hasVariant(
              $state,
              "tokenState",
              "error"
            ),
            [sty.freeBoxtokenState_loaded__amKmJanNqw]: hasVariant(
              $state,
              "tokenState",
              "loaded"
            ),
            [sty.freeBoxtokenState_loading__amKmJmpB4V]: hasVariant(
              $state,
              "tokenState",
              "loading"
            ),
            [sty.freeBoxtrustedHostsState_enabled__amKmj8K6RB]: hasVariant(
              $state,
              "trustedHostsState",
              "enabled"
            ),
            [sty.freeBoxtrustedHostsState_error__amKmj7WksN]: hasVariant(
              $state,
              "trustedHostsState",
              "error"
            ),
            [sty.freeBoxtrustedHostsState_loading__amKmJqBk4M]: hasVariant(
              $state,
              "trustedHostsState",
              "loading"
            ),
          })}
        >
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text___189Ix,
              {
                [sty.texttokenState_loaded___189IxanNqw]: hasVariant(
                  $state,
                  "tokenState",
                  "loaded"
                ),
              }
            )}
          >
            {"Personal Access Tokens"}
          </div>
          <Button
            data-plasmic-name={"newTokenButton"}
            data-plasmic-override={overrides.newTokenButton}
            className={classNames("__wab_instance", sty.newTokenButton, {
              [sty.newTokenButtontokenState_loading]: hasVariant(
                $state,
                "tokenState",
                "loading"
              ),
              [sty.newTokenButtontrustedHostsState_enabled]: hasVariant(
                $state,
                "trustedHostsState",
                "enabled"
              ),
            })}
            size={"wide"}
            startIcon={
              <PlusSvgIcon
                className={classNames(projectcss.all, sty.svg__hD3Ln)}
                role={"img"}
              />
            }
            withIcons={["startIcon"]}
          >
            {"New Token"}
          </Button>
        </div>
        {(
          hasVariant($state, "tokenState", "error")
            ? false
            : hasVariant($state, "tokenState", "loading")
            ? false
            : true
        ) ? (
          <div
            data-plasmic-name={"existingTokens"}
            data-plasmic-override={overrides.existingTokens}
            className={classNames(projectcss.all, sty.existingTokens, {
              [sty.existingTokenstokenState_error]: hasVariant(
                $state,
                "tokenState",
                "error"
              ),
              [sty.existingTokenstokenState_loaded]: hasVariant(
                $state,
                "tokenState",
                "loaded"
              ),
              [sty.existingTokenstokenState_loading]: hasVariant(
                $state,
                "tokenState",
                "loading"
              ),
              [sty.existingTokenstrustedHostsState_enabled]: hasVariant(
                $state,
                "trustedHostsState",
                "enabled"
              ),
              [sty.existingTokenstrustedHostsState_error]: hasVariant(
                $state,
                "trustedHostsState",
                "error"
              ),
              [sty.existingTokenstrustedHostsState_loading]: hasVariant(
                $state,
                "trustedHostsState",
                "loading"
              ),
            })}
          >
            <PersonalAccessToken
              data-plasmic-name={"tokenInstance"}
              data-plasmic-override={overrides.tokenInstance}
              className={classNames("__wab_instance", sty.tokenInstance, {
                [sty.tokenInstancetokenState_loaded]: hasVariant(
                  $state,
                  "tokenState",
                  "loaded"
                ),
                [sty.tokenInstancetrustedHostsState_enabled]: hasVariant(
                  $state,
                  "trustedHostsState",
                  "enabled"
                ),
                [sty.tokenInstancetrustedHostsState_error]: hasVariant(
                  $state,
                  "trustedHostsState",
                  "error"
                ),
                [sty.tokenInstancetrustedHostsState_loading]: hasVariant(
                  $state,
                  "trustedHostsState",
                  "loading"
                ),
              })}
              tokenValue={
                hasVariant($state, "trustedHostsState", "enabled")
                  ? "isOF5407F0nPZXdjPpDFmsgk5piwIbB5PUK3AmiIW3621JB39pPSC2P7LxyxnuVxAKRctOIOVuw0fgZIBQ"
                  : "isOF5407F0nPZXdjPpDFmsgk5piwIbB5PUK3AmiIW3621JB39pPSC2P7LxyxnuVxAKRctOIOVuw0fgZIBQ"
              }
            />

            <PersonalAccessToken
              data-plasmic-name={"personalAccessToken"}
              data-plasmic-override={overrides.personalAccessToken}
              className={classNames("__wab_instance", sty.personalAccessToken, {
                [sty.personalAccessTokentokenState_loaded]: hasVariant(
                  $state,
                  "tokenState",
                  "loaded"
                ),
                [sty.personalAccessTokentrustedHostsState_enabled]: hasVariant(
                  $state,
                  "trustedHostsState",
                  "enabled"
                ),
              })}
              tokenValue={
                "isOF5407F0nPZXdjPpDFmsgk5piwIbB5PUK3AmiIW3621JB39pPSC2P7LxyxnuVxAKRctOIOVuw0fgZIBQ"
              }
            />
          </div>
        ) : null}
        {(
          hasVariant($state, "trustedHostsState", "enabled")
            ? true
            : hasVariant($state, "tokenState", "loading")
            ? true
            : false
        ) ? (
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__a9D1Y,
              {
                [sty.texttokenState_error__a9D1Ya2Ye]: hasVariant(
                  $state,
                  "tokenState",
                  "error"
                ),
                [sty.texttokenState_loaded__a9D1YanNqw]: hasVariant(
                  $state,
                  "tokenState",
                  "loaded"
                ),
                [sty.texttokenState_loading__a9D1YmpB4V]: hasVariant(
                  $state,
                  "tokenState",
                  "loading"
                ),
                [sty.texttrustedHostsState_enabled__a9D1Y8K6RB]: hasVariant(
                  $state,
                  "trustedHostsState",
                  "enabled"
                ),
                [sty.texttrustedHostsState_error__a9D1Y7WksN]: hasVariant(
                  $state,
                  "trustedHostsState",
                  "error"
                ),
                [sty.texttrustedHostsState_loading__a9D1YqBk4M]: hasVariant(
                  $state,
                  "trustedHostsState",
                  "loading"
                ),
              }
            )}
          >
            {hasVariant($state, "tokenState", "loading") ? "Loading\u2026" : ""}
          </div>
        ) : null}
        {(hasVariant($state, "tokenState", "error") ? true : false) ? (
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__bklVi,
              {
                [sty.texttokenState_error__bklVia2Ye]: hasVariant(
                  $state,
                  "tokenState",
                  "error"
                ),
                [sty.texttrustedHostsState_enabled__bklVi8K6RB]: hasVariant(
                  $state,
                  "trustedHostsState",
                  "enabled"
                ),
                [sty.texttrustedHostsState_error__bklVi7WksN]: hasVariant(
                  $state,
                  "trustedHostsState",
                  "error"
                ),
                [sty.texttrustedHostsState_loading__bklViqBk4M]: hasVariant(
                  $state,
                  "trustedHostsState",
                  "loading"
                ),
              }
            )}
          >
            {hasVariant($state, "tokenState", "error")
              ? "Error while load existing tokens. Please refresh page to try again."
              : ""}
          </div>
        ) : null}
        {(
          hasVariant($state, "trustedHostsState", "error")
            ? true
            : hasVariant($state, "trustedHostsState", "loading")
            ? true
            : hasVariant($state, "trustedHostsState", "enabled")
            ? true
            : false
        ) ? (
          <div
            className={classNames(projectcss.all, sty.freeBox__ysama, {
              [sty.freeBoxtrustedHostsState_enabled__ysama8K6RB]: hasVariant(
                $state,
                "trustedHostsState",
                "enabled"
              ),
              [sty.freeBoxtrustedHostsState_error__ysama7WksN]: hasVariant(
                $state,
                "trustedHostsState",
                "error"
              ),
              [sty.freeBoxtrustedHostsState_loading__ysamaqBk4M]: hasVariant(
                $state,
                "trustedHostsState",
                "loading"
              ),
            })}
          />
        ) : null}
        {(
          hasVariant($state, "trustedHostsState", "error")
            ? true
            : hasVariant($state, "trustedHostsState", "loading")
            ? true
            : hasVariant($state, "trustedHostsState", "enabled")
            ? true
            : false
        ) ? (
          <div
            className={classNames(projectcss.all, sty.freeBox__xTzRz, {
              [sty.freeBoxtrustedHostsState_enabled__xTzRz8K6RB]: hasVariant(
                $state,
                "trustedHostsState",
                "enabled"
              ),
              [sty.freeBoxtrustedHostsState_error__xTzRz7WksN]: hasVariant(
                $state,
                "trustedHostsState",
                "error"
              ),
              [sty.freeBoxtrustedHostsState_loading__xTzRZqBk4M]: hasVariant(
                $state,
                "trustedHostsState",
                "loading"
              ),
            })}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__xkLp,
                {
                  [sty.texttrustedHostsState_enabled__xkLp8K6RB]: hasVariant(
                    $state,
                    "trustedHostsState",
                    "enabled"
                  ),
                  [sty.texttrustedHostsState_error__xkLp7WksN]: hasVariant(
                    $state,
                    "trustedHostsState",
                    "error"
                  ),
                  [sty.texttrustedHostsState_loading__xkLpqBk4M]: hasVariant(
                    $state,
                    "trustedHostsState",
                    "loading"
                  ),
                }
              )}
            >
              {hasVariant($state, "trustedHostsState", "error")
                ? "Trusted host apps"
                : hasVariant($state, "trustedHostsState", "loading")
                ? "Trusted host apps"
                : hasVariant($state, "trustedHostsState", "enabled")
                ? "Trusted host apps"
                : "Personal Access Tokens"}
            </div>
            <Button
              data-plasmic-name={"newTrustedHostBtn"}
              data-plasmic-override={overrides.newTrustedHostBtn}
              className={classNames("__wab_instance", sty.newTrustedHostBtn, {
                [sty.newTrustedHostBtntrustedHostsState_enabled]: hasVariant(
                  $state,
                  "trustedHostsState",
                  "enabled"
                ),
                [sty.newTrustedHostBtntrustedHostsState_error]: hasVariant(
                  $state,
                  "trustedHostsState",
                  "error"
                ),
              })}
              size={"wide"}
              startIcon={
                <PlusSvgIcon
                  className={classNames(projectcss.all, sty.svg___9ILd5)}
                  role={"img"}
                />
              }
              withIcons={["startIcon"]}
            >
              {hasVariant($state, "trustedHostsState", "error")
                ? "Add URL"
                : hasVariant($state, "trustedHostsState", "loading")
                ? "Add URL"
                : hasVariant($state, "trustedHostsState", "enabled")
                ? "Add URL"
                : "New Token"}
            </Button>
          </div>
        ) : null}
        {(
          hasVariant($state, "trustedHostsState", "error")
            ? false
            : hasVariant($state, "trustedHostsState", "loading")
            ? false
            : hasVariant($state, "trustedHostsState", "enabled")
            ? true
            : hasVariant($state, "tokenState", "error")
            ? false
            : hasVariant($state, "tokenState", "loading")
            ? false
            : false
        ) ? (
          <div
            data-plasmic-name={"hostsList"}
            data-plasmic-override={overrides.hostsList}
            className={classNames(projectcss.all, sty.hostsList, {
              [sty.hostsListtokenState_error]: hasVariant(
                $state,
                "tokenState",
                "error"
              ),
              [sty.hostsListtokenState_loading]: hasVariant(
                $state,
                "tokenState",
                "loading"
              ),
              [sty.hostsListtrustedHostsState_enabled]: hasVariant(
                $state,
                "trustedHostsState",
                "enabled"
              ),
              [sty.hostsListtrustedHostsState_error]: hasVariant(
                $state,
                "trustedHostsState",
                "error"
              ),
              [sty.hostsListtrustedHostsState_loading]: hasVariant(
                $state,
                "trustedHostsState",
                "loading"
              ),
            })}
          >
            {(
              hasVariant($state, "trustedHostsState", "error")
                ? true
                : hasVariant($state, "trustedHostsState", "loading")
                ? true
                : hasVariant($state, "trustedHostsState", "enabled")
                ? true
                : false
            ) ? (
              <TrustedHost
                className={classNames(
                  "__wab_instance",
                  sty.trustedHost__fkvE6,
                  {
                    [sty.trustedHosttrustedHostsState_enabled__fkvE68K6RB]:
                      hasVariant($state, "trustedHostsState", "enabled"),
                    [sty.trustedHosttrustedHostsState_error__fkvE67WksN]:
                      hasVariant($state, "trustedHostsState", "error"),
                    [sty.trustedHosttrustedHostsState_loading__fkvE6QBk4M]:
                      hasVariant($state, "trustedHostsState", "loading"),
                  }
                )}
                url={"https://my-app.com"}
              />
            ) : null}
            {(
              hasVariant($state, "trustedHostsState", "error")
                ? true
                : hasVariant($state, "trustedHostsState", "loading")
                ? true
                : hasVariant($state, "trustedHostsState", "enabled")
                ? true
                : false
            ) ? (
              <TrustedHost
                className={classNames(
                  "__wab_instance",
                  sty.trustedHost__wrgv3,
                  {
                    [sty.trustedHosttrustedHostsState_enabled__wrgv38K6RB]:
                      hasVariant($state, "trustedHostsState", "enabled"),
                    [sty.trustedHosttrustedHostsState_error__wrgv37WksN]:
                      hasVariant($state, "trustedHostsState", "error"),
                    [sty.trustedHosttrustedHostsState_loading__wrgv3QBk4M]:
                      hasVariant($state, "trustedHostsState", "loading"),
                  }
                )}
                url={
                  hasVariant($state, "trustedHostsState", "error")
                    ? "https://studio.plasmic.app/"
                    : hasVariant($state, "trustedHostsState", "loading")
                    ? "https://studio.plasmic.app/"
                    : hasVariant($state, "trustedHostsState", "enabled")
                    ? "https://studio.plasmic.app/"
                    : "https://my-app.com"
                }
              />
            ) : null}
          </div>
        ) : null}
        {(hasVariant($state, "trustedHostsState", "loading") ? true : false) ? (
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__oOxt,
              {
                [sty.texttrustedHostsState_loading__oOxtqBk4M]: hasVariant(
                  $state,
                  "trustedHostsState",
                  "loading"
                ),
              }
            )}
          >
            {hasVariant($state, "trustedHostsState", "loading")
              ? "Loading..."
              : "Enter some text"}
          </div>
        ) : null}
        {(
          hasVariant($state, "trustedHostsState", "error")
            ? true
            : hasVariant($state, "trustedHostsState", "loading")
            ? false
            : false
        ) ? (
          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__upK2J,
              {
                [sty.texttrustedHostsState_error__upK2J7WksN]: hasVariant(
                  $state,
                  "trustedHostsState",
                  "error"
                ),
                [sty.texttrustedHostsState_loading__upK2JqBk4M]: hasVariant(
                  $state,
                  "trustedHostsState",
                  "loading"
                ),
              }
            )}
          >
            {hasVariant($state, "trustedHostsState", "error")
              ? "Error while loading trusted hosts. Please refresh page to try again."
              : hasVariant($state, "trustedHostsState", "loading")
              ? "Loading..."
              : "Enter some text"}
          </div>
        ) : null}
      </div>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "img",
    "menuButton",
    "changePasswordButton",
    "newTokenButton",
    "existingTokens",
    "tokenInstance",
    "personalAccessToken",
    "newTrustedHostBtn",
    "hostsList",
  ],
  img: ["img"],
  menuButton: ["menuButton"],
  changePasswordButton: ["changePasswordButton"],
  newTokenButton: ["newTokenButton"],
  existingTokens: ["existingTokens", "tokenInstance", "personalAccessToken"],
  tokenInstance: ["tokenInstance"],
  personalAccessToken: ["personalAccessToken"],
  newTrustedHostBtn: ["newTrustedHostBtn"],
  hostsList: ["hostsList"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  img: "img";
  menuButton: typeof MenuButton;
  changePasswordButton: typeof Button;
  newTokenButton: typeof Button;
  existingTokens: "div";
  tokenInstance: typeof PersonalAccessToken;
  personalAccessToken: typeof PersonalAccessToken;
  newTrustedHostBtn: typeof Button;
  hostsList: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicSettingsContainer__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicSettingsContainer__VariantsArgs;
    args?: PlasmicSettingsContainer__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicSettingsContainer__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicSettingsContainer__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicSettingsContainer__ArgProps,
          internalVariantPropNames: PlasmicSettingsContainer__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicSettingsContainer__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicSettingsContainer";
  } else {
    func.displayName = `PlasmicSettingsContainer.${nodeName}`;
  }
  return func;
}

export const PlasmicSettingsContainer = Object.assign(
  // Top-level PlasmicSettingsContainer renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    img: makeNodeComponent("img"),
    menuButton: makeNodeComponent("menuButton"),
    changePasswordButton: makeNodeComponent("changePasswordButton"),
    newTokenButton: makeNodeComponent("newTokenButton"),
    existingTokens: makeNodeComponent("existingTokens"),
    tokenInstance: makeNodeComponent("tokenInstance"),
    personalAccessToken: makeNodeComponent("personalAccessToken"),
    newTrustedHostBtn: makeNodeComponent("newTrustedHostBtn"),
    hostsList: makeNodeComponent("hostsList"),

    // Metadata about props expected for PlasmicSettingsContainer
    internalVariantProps: PlasmicSettingsContainer__VariantProps,
    internalArgProps: PlasmicSettingsContainer__ArgProps,
  }
);

export default PlasmicSettingsContainer;
/* prettier-ignore-end */
