/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: aaggSgVS8yYsAwQffVQB4p
// Component: F4ZVtfq6Xg

import * as React from "react";

import {
  Flex as Flex__,
  SingleChoiceArg,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  hasVariant,
  renderPlasmicSlot,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import IconButton from "../../widgets/IconButton"; // plasmic-import: LPry-TF4j22a/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_design_system_css from "../../../plasmic/PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "../../../plasmic/PP__plasmickit_settings.module.css"; // plasmic-import: aaggSgVS8yYsAwQffVQB4p/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../../../plasmic/plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import sty from "./PlasmicPersonalAccessToken.module.css"; // plasmic-import: F4ZVtfq6Xg/css

import KeySvgIcon from "../../../plasmic/plasmic_kit_icons/icons/PlasmicIcon__KeySvg"; // plasmic-import: xlWv-mkvk/icon
import Trash2SvgIcon from "../../../plasmic/plasmic_kit_icons/icons/PlasmicIcon__Trash2Svg"; // plasmic-import: nS4_I75qv/icon

createPlasmicElementProxy;

export type PlasmicPersonalAccessToken__VariantMembers = {
  copyState: "copied";
};
export type PlasmicPersonalAccessToken__VariantsArgs = {
  copyState?: SingleChoiceArg<"copied">;
};
type VariantPropType = keyof PlasmicPersonalAccessToken__VariantsArgs;
export const PlasmicPersonalAccessToken__VariantProps =
  new Array<VariantPropType>("copyState");

export type PlasmicPersonalAccessToken__ArgsType = {
  tokenValue?: React.ReactNode;
};
type ArgPropType = keyof PlasmicPersonalAccessToken__ArgsType;
export const PlasmicPersonalAccessToken__ArgProps = new Array<ArgPropType>(
  "tokenValue"
);

export type PlasmicPersonalAccessToken__OverridesType = {
  root?: Flex__<"div">;
  freeBox?: Flex__<"div">;
  copyLink?: Flex__<"div">;
  deleteBtn?: Flex__<typeof IconButton>;
};

export interface DefaultPersonalAccessTokenProps {
  tokenValue?: React.ReactNode;
  copyState?: SingleChoiceArg<"copied">;
  className?: string;
}

const $$ = {};

function PlasmicPersonalAccessToken__RenderFunc(props: {
  variants: PlasmicPersonalAccessToken__VariantsArgs;
  args: PlasmicPersonalAccessToken__ArgsType;
  overrides: PlasmicPersonalAccessToken__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "copyState",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.copyState,
      },
    ],
    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        sty.root,
        {
          [sty.rootcopyState_copied]: hasVariant($state, "copyState", "copied"),
        }
      )}
    >
      <div
        data-plasmic-name={"freeBox"}
        data-plasmic-override={overrides.freeBox}
        className={classNames(projectcss.all, sty.freeBox, {
          [sty.freeBoxcopyState_copied]: hasVariant(
            $state,
            "copyState",
            "copied"
          ),
        })}
      >
        <KeySvgIcon
          className={classNames(projectcss.all, sty.svg___2Kau)}
          role={"img"}
        />

        {renderPlasmicSlot({
          defaultContents:
            "isOF5407F0nPZXdjPpDFmsgk5piwIbB5PUK3AmiIW3621JB39pPSC2P7LxyxnuVxAKRctOIOVuw0fgZIBQ",
          value: args.tokenValue,
          className: classNames(sty.slotTargetTokenValue),
        })}
        <div
          data-plasmic-name={"copyLink"}
          data-plasmic-override={overrides.copyLink}
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.copyLink,
            {
              [sty.copyLinkcopyState_copied]: hasVariant(
                $state,
                "copyState",
                "copied"
              ),
            }
          )}
        >
          {hasVariant($state, "copyState", "copied") ? "Copied" : "Copy"}
        </div>
      </div>
      <IconButton
        data-plasmic-name={"deleteBtn"}
        data-plasmic-override={overrides.deleteBtn}
        className={classNames("__wab_instance", sty.deleteBtn)}
        withRedBackgroundHover={true}
      >
        <Trash2SvgIcon
          className={classNames(projectcss.all, sty.svg__gp5XI)}
          role={"img"}
        />
      </IconButton>
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: ["root", "freeBox", "copyLink", "deleteBtn"],
  freeBox: ["freeBox", "copyLink"],
  copyLink: ["copyLink"],
  deleteBtn: ["deleteBtn"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  freeBox: "div";
  copyLink: "div";
  deleteBtn: typeof IconButton;
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicPersonalAccessToken__OverridesType,
  DescendantsType<T>
>;
type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicPersonalAccessToken__VariantsArgs;
    args?: PlasmicPersonalAccessToken__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicPersonalAccessToken__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    // Specify args directly as props
    Omit<PlasmicPersonalAccessToken__ArgsType, ReservedPropsType> &
    // Specify overrides for each element directly as props
    Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    // Specify props for the root element
    Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicPersonalAccessToken__ArgProps,
          internalVariantPropNames: PlasmicPersonalAccessToken__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicPersonalAccessToken__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicPersonalAccessToken";
  } else {
    func.displayName = `PlasmicPersonalAccessToken.${nodeName}`;
  }
  return func;
}

export const PlasmicPersonalAccessToken = Object.assign(
  // Top-level PlasmicPersonalAccessToken renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    freeBox: makeNodeComponent("freeBox"),
    copyLink: makeNodeComponent("copyLink"),
    deleteBtn: makeNodeComponent("deleteBtn"),

    // Metadata about props expected for PlasmicPersonalAccessToken
    internalVariantProps: PlasmicPersonalAccessToken__VariantProps,
    internalArgProps: PlasmicPersonalAccessToken__ArgProps,
  }
);

export default PlasmicPersonalAccessToken;
/* prettier-ignore-end */
