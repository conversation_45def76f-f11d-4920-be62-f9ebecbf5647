.feedContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 276px;
  height: 730px;
  position: relative;
  row-gap: 10px;
  padding: 10px;
}
.activityFeedItem__btc0P:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
}
.img__gdOyp {
  display: flex;
  position: relative;
  width: 48px;
  height: 48px;
}
.activityFeedItem__uxQl:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
}
.img___87C7U {
  display: flex;
  position: relative;
  width: 48px;
  height: 48px;
}
.activityFeedItem___1K9Hj:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
}
.img__vwKp4 {
  display: flex;
  position: relative;
  width: 48px;
  height: 48px;
}
.activityFeedItem__iTzwf:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
}
.img__mj4Q2 {
  display: flex;
  position: relative;
  width: 48px;
  height: 48px;
}
.activityFeedItem__ofHRh:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
}
.img__pPYfK {
  display: flex;
  position: relative;
  width: 48px;
  height: 48px;
}
.activityFeedItem___1BeLe:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
}
.img__zEr6X {
  display: flex;
  position: relative;
  width: 48px;
  height: 48px;
}
.activityFeedItem__c8Mm1:global(.__wab_instance) {
  position: relative;
  flex-shrink: 0;
}
.img__s2Mvv {
  display: flex;
  position: relative;
  width: 48px;
  height: 48px;
}
