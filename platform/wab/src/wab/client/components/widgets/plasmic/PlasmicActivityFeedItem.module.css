.root {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  position: relative;
  width: 100%;
  height: 48px;
  column-gap: 10px;
  min-width: 0;
  border-top: 1px solid var(--token-O4S7RMTqZ3);
}
.rootselected {
  background: var(--token-bV4cCeIniS6);
}
.root:hover {
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-width: 1px;
  border-color: var(--token-mu3x63xzJRW);
}
.freeBox__bGeUh {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
  min-height: 0;
}
.text {
  position: relative;
  font-size: 18px;
  margin-top: 5px;
}
.textstate_projectRenamed {
  height: auto;
  width: auto;
}
.freeBox__wrNkZ {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: space-evenly;
  width: 0px;
  height: 100%;
  margin-top: 5px;
  background: rgba(68, 192, 255, 0.2);
  min-height: 0;
  border-width: 0.5px;
  border-style: dashed;
}
.freeBox__s0K6N {
  display: flex;
  flex-direction: column;
  position: relative;
  align-items: stretch;
  justify-content: space-between;
  align-self: auto;
  width: 100%;
  height: 100%;
  flex-grow: 0;
  flex-shrink: 1;
  flex-basis: auto;
  min-width: 0;
  min-height: 0;
}
.slotTargetTitle {
  font-size: 14px;
}
.slotTargetSubtitle {
  font-size: 12px;
  color: var(--token-fVn5vRhXJxQ);
  white-space: pre;
}
.img___14Wx {
  display: flex;
  position: relative;
  width: 48px;
  height: 48px;
  flex-shrink: 0;
}
