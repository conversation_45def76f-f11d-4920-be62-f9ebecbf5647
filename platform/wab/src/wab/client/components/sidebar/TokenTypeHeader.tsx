// This is a skeleton starter React component generated by Plasmic.
// This file is owned by you, feel free to edit as you see fit.
import {
  isTokenPanelReadOnly,
  useTokenControls,
} from "@/wab/client/components/sidebar/token-controls";
import PlasmicTokenTypeHeader, {
  DefaultTokenTypeHeaderProps,
} from "@/wab/client/plasmic/plasmic_kit_left_pane/PlasmicTokenTypeHeader";
import { useStudioCtx } from "@/wab/client/studio-ctx/StudioCtx";
import { StyleTokenType, tokenTypeLabel } from "@/wab/commons/StyleToken";
import { MultiChoiceArg } from "@plasmicapp/react-web";
import * as React from "react";

interface TokenTypeHeaderProps extends DefaultTokenTypeHeaderProps {
  tokenType: StyleTokenType;
  isExpanded?: boolean;
  toggleExpand: () => void;
}

const PREVIOUS_TOKEN_TYPES: Record<StyleTokenType, StyleTokenType> = {
  Color: "Color",
  FontFamily: "Color",
  FontSize: "FontFamily",
  LineHeight: "FontSize",
  Opacity: "LineHeight",
  Spacing: "Opacity",
};

function TokenTypeHeader(props: TokenTypeHeaderProps) {
  const { isExpanded, tokenType, toggleExpand, ...rest } = props;
  const studioCtx = useStudioCtx();
  const tokenControls = useTokenControls();

  React.useEffect(() => {
    tokenControls.setExpandedHeaders((set) => {
      if (isExpanded && !set.has(tokenType)) {
        set.add(tokenType);
      } else if (!isExpanded && set.has(tokenType)) {
        set.delete(tokenType);
      } else {
        return set;
      }

      return new Set(set);
    });
  }, [isExpanded]);

  const readOnly = isTokenPanelReadOnly(studioCtx);

  const borders: MultiChoiceArg<"bottom" | "top"> = [
    "bottom" as const,
    ...(tokenType !== "Color" &&
    tokenControls.expandedHeaders.has(PREVIOUS_TOKEN_TYPES[tokenType])
      ? ["top" as const]
      : []),
  ];

  return (
    <PlasmicTokenTypeHeader
      tokenType={tokenTypeLabel(tokenType)}
      addButton={
        readOnly
          ? { render: () => null }
          : {
              props: {
                onClick: async (e) => {
                  e.stopPropagation();
                  await tokenControls.onAdd(tokenType);
                  if (!isExpanded) {
                    toggleExpand();
                  }
                },
                "data-test-id": `add-token-button-${tokenType}`,
              },
            }
      }
      isExpanded={isExpanded}
      border={borders}
      {...rest}
    />
  );
}

export default TokenTypeHeader;
