import { ENV } from "@/wab/client/env";
import { initAmplitudeBrowser } from "@/wab/client/observability/amplitude-browser";
import { initPosthogBrowser } from "@/wab/client/observability/posthog-browser";
import { initSentryBrowser } from "@/wab/client/observability/sentry-browser";
import { methodForwarder } from "@/wab/commons/methodForwarder";
import type { Analytics } from "@/wab/shared/observability/Analytics";
import { ConsoleLogAnalytics } from "@/wab/shared/observability/ConsoleLogAnalytics";

let globalAnalytics: Analytics;

export function analytics(): Analytics {
  return globalAnalytics;
}

export function initObservability(): void {
  if (globalAnalytics) {
    console.warn("Initializing observability again");
  }

  const production = ENV.NODE_ENV === "production";

  const amplitudeAnalytics = ENV.AMPLITUDE_API_KEY
    ? initAmplitudeBrowser({
        apiKey: ENV.AMPLITUDE_API_KEY,
      })
    : null;
  const posthogAnalytics =
    ENV.POSTHOG_API_KEY && ENV.POSTHOG_HOST
      ? initPosthogBrowser({
          apiKey: ENV.POSTHOG_API_KEY,
          host: ENV.POSTHOG_HOST,
          reverseProxyHost: ENV.POSTHOG_REVERSE_PROXY_HOST,
        })
      : null;
  if (
    ENV.SENTRY_DSN &&
    ENV.SENTRY_ORG_ID &&
    ENV.SENTRY_PROJECT_ID &&
    posthogAnalytics
  ) {
    initSentryBrowser({
      production,
      commitHash: ENV.COMMITHASH,
      dsn: ENV.SENTRY_DSN,
      orgId: ENV.SENTRY_ORG_ID,
      projId: +ENV.SENTRY_PROJECT_ID,
      posthogAnalytics,
    });
  }

  if (production) {
    globalAnalytics = methodForwarder(amplitudeAnalytics, posthogAnalytics);
  } else {
    globalAnalytics = methodForwarder(
      new ConsoleLogAnalytics(),
      amplitudeAnalytics,
      posthogAnalytics
    );
  }
  globalAnalytics.appendBaseEventProperties({
    production,
    commitHash: ENV.COMMITHASH,
  });
}

export const _testonly = {
  setGlobalAnalytics: (testAnalytics: Analytics) => {
    globalAnalytics = testAnalytics;
  },
};
