import { DbMgr } from "@/wab/server/db/DbMgr";
import { freeCustomDomainFromVercel } from "@/wab/server/routes/internal/hosting";
import { ProjectId } from "@/wab/shared/ApiSchema";
import { PLASMIC_HOSTING_DOMAIN_VALIDATOR } from "@/wab/shared/hosting";

export async function onProjectDelete(dbMgr: DbMgr, projectId: ProjectId) {
  const domains = await dbMgr.getDomainsForProject(projectId);
  for (const domain of domains) {
    // Only free up custom domains, not Plasmic subdomains!
    if (!PLASMIC_HOSTING_DOMAIN_VALIDATOR.isValidSubdomain(domain)) {
      await freeCustomDomainFromVercel(domain);
    }
  }
}
