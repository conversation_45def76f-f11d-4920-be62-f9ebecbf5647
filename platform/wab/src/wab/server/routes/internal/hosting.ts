import { getOpenAI } from "@/wab/server/copilot/llms";
import type { DbMgr } from "@/wab/server/db/DbMgr";
import { InternalDbMgr } from "@/wab/server/db/internal/InternalDbMgr";
import { isTeamOnFreeTrial } from "@/wab/server/freeTrial";
import { logger } from "@/wab/server/observability";
import { superDbMgr, userDbMgr } from "@/wab/server/routes/util";
import { getVercelSecrets } from "@/wab/server/secrets";
import { logError } from "@/wab/server/server-util";
import { withSpan } from "@/wab/server/util/apm-util";
import { UnauthorizedError } from "@/wab/shared/ApiErrors/errors";
import {
  CheckDomainResponse,
  CheckDomainStatus,
  DomainsForProjectResponse,
  ProjectId,
  RevalidateError,
  RevalidatePlasmicHostingRequest,
  RevalidatePlasmicHostingResponse,
  SetCustomDomainForProjectRequest,
  SetCustomDomainForProjectResponse,
  SetDomainStatus,
  SetSubdomainForProjectRequest,
  SetSubdomainForProjectResponse,
} from "@/wab/shared/ApiSchema";
import {
  check,
  ensure,
  ensureString,
  ensureType,
  strictZip,
  withoutNils,
} from "@/wab/shared/common";
import { DEVFLAGS } from "@/wab/shared/devflags";
import {
  DomainValidator,
  PLASMIC_HOSTING_DOMAIN_VALIDATOR,
} from "@/wab/shared/hosting";
import * as Sentry from "@sentry/node";
import { Request, Response } from "express-serve-static-core";
import L from "lodash";
import fetch from "node-fetch";
import { zodTextFormat } from "openai/helpers/zod";
import * as tldts from "tldts";
import { FailablePromise, failableAsync } from "ts-failable";
import { z } from "zod";

export async function getProjectIdAndTokenForDomain(
  req: Request,
  res: Response
) {
  const mgr = InternalDbMgr.fromDbMgr(superDbMgr(req));
  const domain = ensureString(req.query.domain);
  const projectId = await mgr.tryGetProjectIdForDomain(domain);
  const [token] = projectId
    ? Object.values(await mgr.getProjectApiTokens([projectId]))
    : [undefined];
  const team = projectId ? await mgr.getTeamByProjectId(projectId) : undefined;
  const shouldAllowCustomConfig = team
    ? !isTeamOnFreeTrial(team) && !!team.featureTierId
    : false;
  const showBadge =
    projectId && shouldAllowCustomConfig
      ? await mgr.showHostingBadgeForProject(projectId)
      : true;
  const settings =
    projectId && shouldAllowCustomConfig
      ? await mgr.getPlasmicHostingSettings(projectId)
      : undefined;
  const hasAppAuth = projectId
    ? !!(await mgr.getAppAuthConfig(projectId))
    : false;
  const allowRobots = projectId
    ? await mgr.allowRobotsInPlasmicHosting(projectId)
    : false;
  res.json({
    projectId,
    token,
    showBadge,
    favicon: settings?.favicon,
    hasAppAuth,
    allowRobots,
  });
}

export async function getDomainsForProject(req: Request, res: Response) {
  const mgr = userDbMgr(req);
  const projectId = ensureString(req.params.projectId) as ProjectId;
  const domains = await mgr.getDomainsForProject(projectId);
  res.json(ensureType<DomainsForProjectResponse>({ domains }));
}

const rePhishing = /\b(auth|login|secure|sso)\b/i;
const phishingCheckFormat = zodTextFormat(
  z.object({
    phishing: z.boolean(),
    reason: z.string(),
  }),
  "phishing_subdomain_check"
);
const phishingCheckPrompt = `For a subdomain such as "secure-login.plasmic.run", the prefix is "secure-login".
You will be given a subdomain's prefix.
Your job is to determine whether the subdomain will be used for phishing.

Identify whether a subdomain is phishing with these criteria:
- Includes names of banking, cryptocurrency, and financial companies
- Includes misspellings of company names
- Include words that give a sense of security

Examples of phishing subdomains:
- "coinbase"
- "coinbaase"
- "coinbase-login"
- "ledger-wallet"
- "trezor-secure"
- "sso-metamask"
- "metamask-faq-us"
- "official-site-kraken"
- "public-kukoin-login-en"
- "sso-coinbaase-login-us"
- "welcome-ndax-learn"

Reply whether the subdomain is phishing and a concise reason.`;

/**
 * `deep` checks whether the domain has been configured.
 */
async function doCheckDomain(
  domain: string,
  mgr: DbMgr,
  deep = false
): Promise<CheckDomainStatus> {
  const domainValidator = PLASMIC_HOSTING_DOMAIN_VALIDATOR;
  if (!domainValidator.isValidDomainOrSubdomain(domain)) {
    return {
      isValid: false,
    };
  }

  // Anti-phishing validation for only *.plasmic.run-style subdomains
  if (domainValidator.isValidSubdomain(domain)) {
    const prefix = ensure(
      domainValidator.parseSubdomainPart(domain),
      "subdomain must have prefix"
    );

    // Tier 1: simple regex blacklist on the subdomain prefix
    if (rePhishing.test(prefix)) {
      logger().info(`RegExp domain check rejected, blocking: ${domain}`);
      return { isValid: false };
    }

    // Tier 2: LLM-based heuristic
    try {
      const openai = getOpenAI();
      const response = await withSpan("LLM domain check", () =>
        openai.responses.parse({
          model: "gpt-5-nano",
          reasoning: {
            effort: "minimal" as "low", // TODO: remove type assertion when upgrading to openai@5
          },
          text: {
            format: phishingCheckFormat,
          },
          input: [
            {
              role: "system",
              content: phishingCheckPrompt,
            },
            {
              role: "user",
              content: [
                {
                  type: "input_text",
                  text: prefix,
                },
              ],
            },
          ],
        })
      );
      const result = response.output_parsed;
      if (!result) {
        logger().info(`LLM domain check invalid response, BLOCKING: ${domain}`);
        return { isValid: false };
      } else if (result.phishing) {
        logger().info(
          `LLM domain check rejected with reason "${result.reason}", BLOCKING: ${domain}`
        );
        return { isValid: false };
      } else {
        logger().debug(
          `LLM domain check approved with reason "${result.reason}", ALLOWING: ${domain}`
        );
        // do not return, continue
      }
    } catch (err) {
      // On any error, conservatively block.
      Sentry.captureException(err);
      logger().error(`LLM domain check failed, BLOCKING: ${domain}`, {
        error: err,
      });
      return { isValid: false };
    }
  }

  const projectId = await mgr.tryGetProjectIdForDomain(domain);

  const status: CheckDomainStatus = {
    isValid: true,
    isAvailable: !projectId,
    isPlasmicSubdomain: domainValidator.isValidSubdomain(domain),
    isAnyPlasmicDomain: domainValidator.isAnyPlasmicDomain(domain),
  };

  if (deep) {
    const response = await fetch(
      `https://api.vercel.com/v6/domains/${domain}/config?teamId=${
        getVercelSecrets().teamId
      }`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${getVercelSecrets().authBearerToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    const data = await response.json();

    // configuredBy will only be set (to a value like CNAME or A) only if the domain is working with Vercel--that is,
    // if it has a correct CNAME/A value that ultimately resolves to Vercel.
    const valid = data?.configuredBy ? true : false;
    status.configuredBy = data?.configuredBy ?? undefined;

    status.isCorrectlyConfigured = valid;
  }

  return status;
}

export async function checkDomain(req: Request, res: Response) {
  const mgr = userDbMgr(req);
  const domain = ensureString(JSON.parse(ensureString(req.query.domain)));
  res.json(
    ensureType<CheckDomainResponse>({
      status: await doCheckDomain(domain, mgr, true),
    })
  );
}

/**
 * Keeps *.plasmic.run, only replaces any custom domains.
 *
 * Must pass in both non-www and www (the expanded domains).
 */
async function upsertCustomDomain(
  domainValidator: DomainValidator,
  mgr: DbMgr,
  projectId: ProjectId,
  domains: string[]
) {
  const oldDomains = await mgr.getDomainsForProject(projectId);
  check(
    oldDomains.filter((d) => !domainValidator.isValidSubdomain(d)),
    "There should be no existing custom domain when setting the domain! Need to explicitly free old domains first."
  );
  const newDomains = withoutNils([
    ...oldDomains.filter((d) => domainValidator.isValidSubdomain(d)),
    ...domains,
  ]);
  await mgr.setDomainsForProject(newDomains, projectId);
}

async function upsertSubdomain(
  domainValidator: DomainValidator,
  mgr: DbMgr,
  projectId: ProjectId,
  domain: string | undefined
) {
  const oldDomains = await mgr.getDomainsForProject(projectId);
  const newDomains = withoutNils([
    ...oldDomains.filter((d) => !domainValidator.isValidSubdomain(d)),
    domain,
  ]);
  await mgr.setDomainsForProject(newDomains, projectId);
}

// Returns www first, since that is the canonical, and non-www is the redirect.
// This is the recommended behavior in Vercel as well.
function getExpandedWwwDomains(domainWithoutWww: string) {
  return !tldts.parse(domainWithoutWww).subdomain
    ? [`www.${domainWithoutWww}`, domainWithoutWww]
    : [domainWithoutWww];
}

export async function freeCustomDomainFromVercel(domain: string) {
  // Use the project domain API, not the domain API!
  // We always try removing both www and non-www.
  const domainWithoutWww =
    tldts.parse(domain).subdomain === "www" ? domain.slice(4) : domain;
  const expandedDomains = getExpandedWwwDomains(domainWithoutWww);
  // We try twice, since we don't know which order of precedence the domains are - we've historically done both treating www as canonical (current behavior) and treating non-www as canonical (and the www as the redirect).
  // Otherwise you get {"error":{"code":"domain_is_redirect","message":"Cannot remove \"aoeuaoeuaoeuaoeu.com\" until existing redirects to \"aoeuaoeuaoeuaoeu.com\" are removed."}}
  for (let i = 0; i < 2; i++) {
    for (const dom of expandedDomains) {
      const response = await fetch(
        `https://api.vercel.com/v9/projects/${
          getVercelSecrets().projectId
        }/domains/${dom}?teamId=${getVercelSecrets().teamId}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${getVercelSecrets().authBearerToken}`,
          },
        }
      );
      const result = await response.json();
      logger().info(
        "freeCustomDomainFromVercel: " + dom + JSON.stringify(result)
      );
    }
  }
}

export async function setCustomDomainForProject(req: Request, res: Response) {
  const {
    customDomain: submittedCustomDomain,
    projectId,
  }: SetCustomDomainForProjectRequest = req.body;
  const mgr = userDbMgr(req);
  const domainValidator = PLASMIC_HOSTING_DOMAIN_VALIDATOR;

  async function attemptUpdateAndGetStatus(): Promise<{
    [key: string]: SetDomainStatus;
  }> {
    // Remove the old domain from Vercel

    const oldDomains = await mgr.getDomainsForProject(projectId);
    const oldDomain = domainValidator.extractCustomDomain(oldDomains);
    if (oldDomain) {
      await freeCustomDomainFromVercel(oldDomain);
    }

    async function prelimCheckDomain(
      customDomain: string,
      redirectTo?: string
    ): Promise<SetDomainStatus> {
      const checkStatus = await doCheckDomain(customDomain, mgr);
      if (!checkStatus.isValid) {
        return "DomainInvalid";
      }

      if (checkStatus.isPlasmicSubdomain || checkStatus.isAnyPlasmicDomain) {
        return "DomainInvalid";
      }

      if (!checkStatus.isAvailable) {
        return "DomainUsedElsewhereInPlasmic";
      }

      return "DomainUpdated";
    }

    async function tryAddingDomainToVercel(
      customDomain: string,
      redirectTo?: string
    ): Promise<SetDomainStatus> {
      const response = await fetch(
        `https://api.vercel.com/v8/projects/${
          getVercelSecrets().projectId
        }/domains?teamId=${getVercelSecrets().teamId}`,
        {
          body: JSON.stringify({ name: customDomain, redirect: redirectTo }),
          headers: {
            Authorization: `Bearer ${getVercelSecrets().authBearerToken}`,
            "Content-Type": "application/json",
          },
          method: "POST",
        }
      );

      const data = await response.json();

      logger().info(
        `registering custom domain ${customDomain} project ${projectId} ${JSON.stringify(
          data
        )}`
      );

      // Domain is already owned by another team but you can request delegation to access it
      if (data.error?.code === "forbidden")
        return "DomainUsedElsewhereInVercel";

      // Domain is already being used by a different project
      if (data.error?.code === "domain_taken")
        return "DomainUsedElsewhereInVercel";

      // Domain is already being used by a different project
      if (
        data.error?.code === "duplicate-team-registration" ||
        data.error?.code === "owned-on-other-team"
      )
        return "DomainUsedElsewhereInVercel";

      // Other domain error
      if (data.error) {
        return "OtherDomainError";
      }

      return "DomainUpdated";
    }

    if (submittedCustomDomain) {
      // If we are adding example.com, we also want to add www.example.com as a second domain which redirects to example.com.
      // Note that we are adding this alias only to Vercel, and not to our own DB. We should only be tracking the 'canonical' non-www domain.
      //
      // If we're just adding a subdomain like foo.example.com, then we don't add a www alias, and we're just adding the subdomain itself.
      // The exception is www.example.com, which is technically the www subdomain, but we treat that as if we submitted example.com.
      const domainWithoutWww =
        tldts.parse(submittedCustomDomain).subdomain === "www"
          ? submittedCustomDomain.slice(4)
          : submittedCustomDomain;
      const expandedDomains = getExpandedWwwDomains(domainWithoutWww);

      // First check the domains for availability and for validity.
      const checkResults = await Promise.all(
        expandedDomains.map((expandedDomain) =>
          prelimCheckDomain(expandedDomain)
        )
      );
      const badResults = Object.fromEntries(
        strictZip(expandedDomains, checkResults).filter(
          ([expandedDomain, result]) => result !== "DomainUpdated"
        )
      );
      if (Object.keys(badResults).length > 0) {
        return badResults;
      }

      // Tentatively insert the domain(s) in the DB, this lets us detect when we don't even have the right permissions.
      await upsertCustomDomain(
        domainValidator,
        mgr,
        projectId,
        expandedDomains
      );

      // Here we try adding the domains one at a time, starting with example.com then www.example.com.
      // If the latter domain can't be added, we remove the first one and abandon.
      const status = await tryAddingDomainToVercel(expandedDomains[0]);
      if (status !== "DomainUpdated") {
        return { [expandedDomains[0]]: status };
      }
      if (expandedDomains.length > 1) {
        try {
          const status2 = await tryAddingDomainToVercel(
            expandedDomains[1],
            expandedDomains[0]
          );
          if (status2 !== "DomainUpdated") {
            // Unwind
            await freeCustomDomainFromVercel(expandedDomains[0]);
            await upsertCustomDomain(domainValidator, mgr, projectId, []);
            return { [expandedDomains[1]]: status2 };
          }
        } catch (error) {
          // Unwind
          await freeCustomDomainFromVercel(expandedDomains[0]);
          await upsertCustomDomain(domainValidator, mgr, projectId, []);
          return { [expandedDomains[1]]: "OtherDomainError" };
        }
      }
    } else {
      await upsertCustomDomain(domainValidator, mgr, projectId, []);
    }

    return { "": "DomainUpdated" };
  }

  res.json(
    ensureType<SetCustomDomainForProjectResponse>({
      status: await attemptUpdateAndGetStatus(),
    })
  );
}

export async function setSubdomainForProject(req: Request, res: Response) {
  const { subdomain: rawSubdomain, projectId }: SetSubdomainForProjectRequest =
    req.body;
  const subdomain = rawSubdomain ? rawSubdomain.toLowerCase() : undefined;

  const mgr = userDbMgr(req);
  const domainValidator = PLASMIC_HOSTING_DOMAIN_VALIDATOR;

  async function go(): Promise<SetDomainStatus> {
    if (subdomain) {
      const checkStatus = await doCheckDomain(subdomain, mgr);

      if (!checkStatus.isValid) {
        return "DomainInvalid";
      }

      if (!checkStatus.isPlasmicSubdomain) {
        return "DomainInvalid";
      }

      if (!checkStatus.isAvailable) {
        return "DomainUsedElsewhereInPlasmic";
      }
    }

    await upsertSubdomain(domainValidator, mgr, projectId, subdomain);

    // TODO: Invalidate pages from the old domain (need to "unpublish") and clarify to user that they must republish them to the new one.

    return "DomainUpdated";
  }

  res.json(
    ensureType<SetSubdomainForProjectResponse>({
      status: await go(),
    })
  );
}

interface RecordPlasmicHostingHit {
  hit: boolean;
  site: string;
  path: string;
  projects: Array<{
    projectId: ProjectId;
    version: string;
  }>;
}

export async function recordPlasmicHostingHit(req: Request, res: Response) {
  const mgr = superDbMgr(req);
  const { hit, site, path, projects } = ensureType<RecordPlasmicHostingHit>(
    req.body
  );
  logger().debug(
    `record hosting-hit ${hit} ${site} ${path} ${JSON.stringify(projects)}`
  );
  await mgr.recordHostingHit(site, path, hit);
  res.json({});
}

export async function getRecordPlasmicHostingHit(req: Request, res: Response) {
  const mgr = superDbMgr(req);
  const site = ensureString(req.query.site);
  const path = ensureString(req.query.path);
  logger().debug(`record hosting-hit ${site} ${path}`);
  await mgr.recordHostingHit(site, path, true);
  res.json({});
}

export async function revalidatePlasmicHosting(req: Request, res: Response) {
  const mgr = userDbMgr(req);
  const { projectId } = ensureType<RevalidatePlasmicHostingRequest>(req.body);
  const domains = await mgr.getDomainsForProject(projectId);
  const response: RevalidatePlasmicHostingResponse = {
    successes: [],
    failures: [],
  };
  for (const domain of domains) {
    // Get and remove old routes.
    //  This may include dynamic routes, so we cannot determine this just from looking at prior project revisions.
    //  We need the SSR code to record what pages were actually generated and thus need to be re-built.
    //  This can also include 404 pages.
    const { hits, keyVals } = await mgr.getAllRecordedHitsForSite(domain);
    const paths = hits.map((hit) => hit.path);

    try {
      // Invalidate all old routes
      const { result } = await revalidate(domain, paths);
      if (result.isError) {
        response.failures.push({ domain, error: result.error });
      } else {
        // Delete all the recorded hits.
        await mgr.purgeHitsForSite(keyVals.map(({ id }) => id));
        response.successes.push({ domain });
      }
    } catch (error) {
      logError(error, "revalidatePlasmicHosting");
      response.failures.push({
        domain,
        error: { type: "Unknown error", message: "" + error },
      });
    }
  }
  res.json(response);
}

async function revalidate(
  site: string, // hostname to be revalidated
  paths: string[]
): FailablePromise<void, RevalidateError> {
  return failableAsync(async ({ success, failure }) => {
    const urlPaths = L.uniq(paths.map((path) => `/_sites/${site}${path}`));

    // You must call this at the associated domain, since the Vercel-side .revalidate() call must happen from that domain:
    // https://github.com/vercel/next.js/discussions/35968#discussioncomment-3192931

    // Note: this can fail because of getaddrinfo on the domain not working for some reason.
    // For instance, our app server might still not be able to resolve the given address, due to DNS caching,
    // while Vercel was able to add it via the API.
    // In that case, we should fail the API revalidate operation.
    // We could alternatively nuke everything and redeploy the Vercel app (future work).
    const response = await fetch(`https://${site}/api/revalidate`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authentication: `Bearer ${getVercelSecrets().plasmicHostingSecret}`,
      },
      body: JSON.stringify({
        urlPaths,
      }),
    });

    const text = await response.text();
    logger().info(
      `revalidate got response: ${JSON.stringify(urlPaths)} ${text}`
    );
    if (text.includes("https://www.cloudflare.com?utm_source=challenge")) {
      return failure({ type: "Cloudflare challenge" });
    }
    try {
      JSON.parse(text);
    } catch (error) {
      return failure({ type: "Invalid JSON response" });
    }
    return success();
  });
}

export async function setShowHostingBadge(req: Request, res: Response) {
  const mgr = userDbMgr(req);
  const { projectId } = req.params as { projectId: ProjectId };
  const { showBadge } = req.body as { showBadge: boolean };
  if (!showBadge) {
    const dbMgr = superDbMgr(req);
    const team = await dbMgr.getTeamByProjectId(projectId);
    if (
      !team ||
      !team.featureTierId ||
      team.featureTierId === DEVFLAGS.freeTier.id
    ) {
      throw new UnauthorizedError(
        "Upgrade your team to remove the Plasmic badge"
      );
    }
  }
  await mgr.updateProjectExtraData(projectId, {
    hideHostingBadge: !showBadge,
  });
  res.json({});
}

export async function setAllowRobots(req: Request, res: Response) {
  const mgr = userDbMgr(req);
  const { projectId } = req.params as { projectId: ProjectId };
  const { allowRobots } = req.body as { allowRobots: boolean };
  await mgr.updateProjectExtraData(projectId, {
    allowRobots,
  });
  res.json({});
}

export async function getHostingSettings(req: Request, res: Response) {
  const mgr = InternalDbMgr.fromDbMgr(userDbMgr(req));
  const settings = await mgr.getPlasmicHostingSettings(
    req.params.projectId as ProjectId
  );
  res.json(settings ?? {});
}

export async function updateHostingSettings(req: Request, res: Response) {
  const mgr = InternalDbMgr.fromDbMgr(userDbMgr(req));
  const { projectId } = req.params as { projectId: ProjectId };

  const dbMgr = superDbMgr(req);
  const team = await dbMgr.getTeamByProjectId(projectId);
  if (
    !team ||
    !team.featureTierId ||
    team.featureTierId === DEVFLAGS.freeTier.id
  ) {
    throw new UnauthorizedError("Upgrade your team to add a custom favicon");
  }

  const settings = await mgr.upsertPlasmicHostingSettings(
    req.params.projectId as ProjectId,
    {
      ...(req.body.favicon && {
        favicon: {
          url: req.body.favicon.url,
          mimeType: req.body.favicon.mimeType,
        },
      }),
    }
  );
  res.json(settings);
}
