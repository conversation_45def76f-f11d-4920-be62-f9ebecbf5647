import { team<PERSON>piUserAuth } from "@/wab/server/auth/routes";
import { doLogin, verifyClientCredentials } from "@/wab/server/auth/util";
import { InternalDbMgr } from "@/wab/server/db/internal/InternalDbMgr";
import { User } from "@/wab/server/entities/Entities";
import { mkApiTeam } from "@/wab/server/routes/teams";
import {
  superDbMgr,
  userDbMgr,
  withTransaction,
} from "@/wab/server/routes/util";
import {
  BadRequestError,
  ForbiddenError,
  UnauthorizedError,
} from "@/wab/shared/ApiErrors/errors";
import { CreateTeamRequest, CreateTeamResponse } from "@/wab/shared/ApiSchema";
import {
  assert,
  ensure,
  ensureType,
  safeCast,
  uncheckedCast,
} from "@/wab/shared/common";
import { Application, NextFunction, RequestHandler } from "express";
import { Request, Response } from "express-serve-static-core";
import passport from "passport";
import { IVerifyOptions } from "passport-local";

type Region = "apac" | "eu";

function makeGuidewireEnv(test: boolean, region?: Region) {
  return `guidewire${test ? `-test` : ""}${region ? `-${region}` : ""}`;
}

export async function gwAdminOnly(
  req: Request,
  res: Response,
  next: NextFunction
) {
  if (await isGuidewireServerRequest(req)) {
    next();
  } else {
    next(new ForbiddenError());
  }
}

export function addGuidewireRoutes(app: Application) {
  app.get(
    "/api/v1/auth/oidc/guidewire",
    makeGuidewireLogin({ test: false })
  );
  app.get(
    "/api/v1/auth/oidc/guidewire-apac",
    makeGuidewireLogin({ test: false, region: "apac" })
  );
  app.get(
    "/api/v1/auth/oidc/guidewire-eu",
    makeGuidewireLogin({ test: false, region: "eu" })
  );
  app.get(
    "/api/v1/auth/oidc/guidewire-test",
    makeGuidewireLogin({ test: true })
  );
  //-------------------------------------
  app.get(
    "/api/v1/auth/oidc/guidewire/callback",
    withTransaction(makeGuidewireCallback({ test: false }))
  );
  app.get(
    "/api/v1/auth/oidc/guidewire-apac/callback",
    withTransaction(makeGuidewireCallback({ test: false, region: "apac" }))
  );
  app.get(
    "/api/v1/auth/oidc/guidewire-eu/callback",
    withTransaction(makeGuidewireCallback({ test: false, region: "eu" }))
  );
  app.get(
    "/api/v1/auth/oidc/guidewire-test/callback",
    withTransaction(makeGuidewireCallback({ test: true }))
  );
  //-------------------------------------
  app.get(
    "/api/v1/guidewire/redirect",
    makeGuidewireRedirect({ test: false })
  );
  app.get(
    "/api/v1/guidewire-apac/redirect",
    makeGuidewireRedirect({ test: false, region: "apac" })
  );
  app.get(
    "/api/v1/guidewire-eu/redirect",
    makeGuidewireRedirect({ test: false, region: "eu" })
  );
  app.get(
    "/api/v1/guidewire-test/redirect",
    makeGuidewireRedirect({ test: true })
  );
  app.post(
    "/api/v1/guidewire/users",
    safeCast<RequestHandler>(gwAdminOnly),
    withTransaction(createGuidewireUser)
  );
  app.delete(
    "/api/v1/guidewire/users/:gwId",
    safeCast<RequestHandler>(gwAdminOnly),
    withTransaction(deleteGuidewireUser)
  );
  app.get(
    "/api/v1/guidewire/users/:gwId",
    safeCast<RequestHandler>(gwAdminOnly),
    withTransaction(getGuidewireUser)
  );
  app.put(
    "/api/v1/guidewire/users/:gwId",
    safeCast<RequestHandler>(gwAdminOnly),
    withTransaction(updateGuidewireUser)
  );
  app.get(
    "/api/v1/guidewire/user-by-id/:userId",
    tryGetGuidewireUserByUserId
  );

  app.post(
    "/api/v1/guidewire/teams",
    safeCast<RequestHandler>(teamApiUserAuth),
    withTransaction(createGuidewireTeam)
  );
}

export function makeGuidewireLogin(opts: { test: boolean; region?: Region }) {
  const env = makeGuidewireEnv(opts.test, opts.region);
  return async function guidewireLogin(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const prompt = req.query.force ? { prompt: "consent" } : {};
    await new Promise<void>((resolve) =>
      passport.authenticate(
        env,
        {
          ...prompt,
          // Store continueTo as part of the oauth state
          state: { continueTo: (req.query.continueTo as string) ?? "/" } as any,
        },
        () => resolve()
      )(req, res, next)
    );
  };
}

export function makeGuidewireCallback(opts: {
  test: boolean;
  region?: Region;
}) {
  return async function guidewireCallback(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    const env = makeGuidewireEnv(opts.test, opts.region);
    await new Promise<void>((resolve) => {
      passport.authenticate(
        env,
        async (err: Error, user: User, info: IVerifyOptions) => {
          console.log("Guidewire callback", { err, user, info });
          if (err || !user) {
            err = err ?? new Error(`Unable to authenticate user`);
            next(err);
          } else {
            doLogin(req, user, (err2) => {
              if (err2) {
                next(err2);
              } else {
                res.redirect((info as any).state?.continueTo ?? "/");
                resolve();
              }
            });
          }
        }
      )(req, res, next);
    });
  };
}

export function makeGuidewireRedirect(opts: {
  test: boolean;
  region?: Region;
}) {
  const env = makeGuidewireEnv(opts.test, opts.region);
  return async function guidewireRedirect(
    req: Request,
    res: Response,
    next: NextFunction
  ) {
    console.log("Authenticating JWT");
    res.redirect(
      `/api/v1/auth/oidc/${env}?continueTo=${encodeURIComponent(
        (req.query.continueTo as string) ?? "/"
      )}`
    );
  };
}

interface ApiGuidewireUser {
  id: string; // Plasmic user id
  firstName: string;
  lastName: string;
  email: string;
  gwId: string;
}

export async function createGuidewireUser(req: Request, res: Response) {
  const mgr = InternalDbMgr.fromDbMgr(superDbMgr(req));
  const userInfo = req.body as Omit<ApiGuidewireUser, "id">;

  assert(userInfo.gwId, "Missing Guidewire ID");
  assert(userInfo.email, "Missing user email");
  assert(userInfo.firstName, "Missing user first name");
  assert(userInfo.lastName, "Missing user last name");

  let userExists: false | "email" | "Guidewire ID" = false;
  try {
    await mgr.getGuidewireUserByGwId(userInfo.gwId);
    userExists = "Guidewire ID";
  } catch {}

  if (!userExists) {
    try {
      await mgr.getUserByEmail(userInfo.email);
      userExists = "email";
    } catch {}
  }

  if (userExists) {
    throw new BadRequestError(
      `User with the provided ${userExists} already exists`
    );
  }

  const rootTeam = await getGuidewireRootTeam(req);

  const user = await mgr.createUser({
    email: userInfo.email,
    firstName: userInfo.firstName,
    lastName: userInfo.lastName,
    needsSurvey: false,
    needsTeamCreationPrompt: false,
    needsIntroSplash: false,
    isWhiteLabel: true,
    whiteLabelId: userInfo.gwId,
    owningTeamId: rootTeam.id,
  });
  const gwUser = await mgr.createGuidewireUser(user, userInfo.gwId);
  res.json(
    ensureType<ApiGuidewireUser>({
      email: user.email,
      firstName: user.firstName ?? "",
      lastName: user.lastName ?? "",
      id: user.id,
      gwId: gwUser.gwId,
    })
  );
}

export async function updateGuidewireUser(req: Request, res: Response) {
  const mgr = InternalDbMgr.fromDbMgr(superDbMgr(req));

  const gwId: string = req.params.gwId;
  const userInfo = req.body as Omit<ApiGuidewireUser, "id">;
  const gwUser = await mgr.getGuidewireUserByGwId(gwId);
  const user = await mgr.getUserById(gwUser.userId);

  await mgr.updateUser({
    id: user.id,
    firstName: userInfo.firstName,
    lastName: userInfo.lastName,
  });
  res.json(
    ensureType<ApiGuidewireUser>({
      email: user.email,
      firstName: user.firstName ?? "",
      lastName: user.lastName ?? "",
      id: user.id,
      gwId: gwUser.gwId,
    })
  );
}

export async function getGuidewireUser(req: Request, res: Response) {
  const mgr = InternalDbMgr.fromDbMgr(superDbMgr(req));
  const gwId: string = req.params.gwId;
  const gwUser = await mgr.getGuidewireUserByGwId(gwId);
  const user = await mgr.getUserById(gwUser.userId);
  res.json(
    ensureType<ApiGuidewireUser>({
      email: user.email,
      firstName: user.firstName ?? "",
      lastName: user.lastName ?? "",
      id: user.id,
      gwId: gwUser.gwId,
    })
  );
}

export async function deleteGuidewireUser(req: Request, res: Response) {
  const mgr = InternalDbMgr.fromDbMgr(superDbMgr(req));
  const gwId: string = req.params.gwId;
  await mgr.deleteGuidewireUserByGwId(gwId);
  res.json({ deletedId: gwId });
}

export async function tryGetGuidewireUserByUserId(req: Request, res: Response) {
  const mgr = InternalDbMgr.fromDbMgr(userDbMgr(req));
  res.json(
    ensureType<string | null>(
      (await mgr.tryGetGuidewireUserByUserId(req.params.userId))?.gwId ?? null
    )
  );
}

export async function isGuidewireServerRequest(req: Request) {
  if ((req as any).isGwApiRequest) {
    return true;
  }
  const token = req.headers["x-gw-api-access-token"];
  const rootTeam = await getGuidewireRootTeam(req);
  assert(
    rootTeam.whiteLabelInfo?.apiClientCredentials,
    "Must have client credentials configured"
  );
  if (typeof token === "string") {
    try {
      await verifyClientCredentials(
        ensure(rootTeam.whiteLabelName!, "whiteLabelName must be configured"),
        token,
        rootTeam.whiteLabelInfo.apiClientCredentials
      );
      (req as any).isGwApiRequest = true;
      return true;
    } catch {
      return false;
    }
  }

  return false;
}

async function getGuidewireRootTeam(req: Request) {
  const superMgr = superDbMgr(req);
  const isTest = req.headers["x-gw-api-test"] === "true";
  return await superMgr.getTeamByWhiteLabelName(
    isTest ? "guidewire-test" : "guidewire"
  );
}

export async function createGuidewireTeam(req: Request, res: Response) {
  const userMgr = userDbMgr(req);
  const superMgr = superDbMgr(req);
  const { name: rawName } = uncheckedCast<CreateTeamRequest>(req.body);
  if (!req.user) {
    throw new UnauthorizedError();
  }
  const teamName = rawName ? rawName : `${req.user.firstName}'s Team`;

  const rootTeam = await getGuidewireRootTeam(req);
  const team = await userMgr.createTeam(teamName);

  await superMgr.sudoUpdateTeam({
    id: team.id,
    seats: 1000000,
    whiteLabelInfo: {},
    parentTeamId: rootTeam.id,
  });

  // Automatically create a new workspace in the new team.
  await userMgr.createWorkspace({
    name: "Workspace",
    description: "My first workspace",
    teamId: team.id,
  });

  req.analytics.track("Create Guidewire team", {
    teamName: teamName,
  });
  const apiTeam = mkApiTeam(team);
  res.json(ensureType<CreateTeamResponse>({ team: apiTeam }));
}
