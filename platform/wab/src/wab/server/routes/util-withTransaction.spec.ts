import {
  withTransaction,
} from "@/wab/server/routes/util";
import { EntityManager } from "typeorm";

describe("withTransaction", () => {
  let mockReq: any;
  let mockRes: any;
  let mockNext: jest.Mock;
  let mockTxMgr: EntityManager;
  let mockConnection: any;

  beforeEach(() => {
    mockTxMgr = {} as EntityManager;
    mockNext = jest.fn();
    mockRes = {
      send: jest.fn(),
      json: jest.fn(),
      status: jest.fn(() => mockRes),
      headersSent: false,
    };
    mockReq = {
      con: {
        transaction: jest.fn(),
      },
    };
    mockConnection = mockReq.con;
  });

  describe("withTransaction", () => {
    it("should wrap handler in transaction and commit on success", async () => {
      const handler = jest.fn(async (_req, res, _next) => {
        // Simulate successful handler
        res.json({ success: true });
      });

      // Mock successful transaction
      mockConnection.transaction.mockImplementation(async (callback: any) => {
        await callback(mockTxMgr);
      });

      const wrapped = withTransaction(handler);
      await wrapped(mockReq, mockRes, mockNext);

      expect(mockConnection.transaction).toHaveBeenCalledTimes(1);
      expect(mockReq.txMgr).toBe(mockTxMgr);
      expect(handler).toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled(); // No error passed to next
    });

    it("should rollback transaction on error", async () => {
      const testError = new Error("Test error");
      const handler = jest.fn(async () => {
        throw testError;
      });

      // Mock transaction that throws when callback throws
      mockConnection.transaction.mockImplementation(async (callback: any) => {
        await callback(mockTxMgr);
      });

      const wrapped = withTransaction(handler);
      await wrapped(mockReq, mockRes, mockNext);

      expect(mockConnection.transaction).toHaveBeenCalledTimes(1);
      expect(handler).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith(testError); // Error passed to next
    });

    it("should set txMgr on request", async () => {
      const handler = jest.fn(async (req, _res, _next) => {
        expect(req.txMgr).toBeDefined();
        expect(req.txMgr).toBe(mockTxMgr);
      });

      mockConnection.transaction.mockImplementation(async (callback: any) => {
        await callback(mockTxMgr);
      });

      const wrapped = withTransaction(handler);
      await wrapped(mockReq, mockRes, mockNext);

      expect(handler).toHaveBeenCalled();
    });
  });
});