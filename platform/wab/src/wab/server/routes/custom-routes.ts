import { createRateLimiter } from "@/wab/server/rate-limit";
import {
  queryCopilot,
  queryCopilotFeedback,
  queryPublicUiCopilot,
  queryUiCopilot,
  saveCopilotFeedback,
} from "@/wab/server/routes/internal/copilot";
import {
  addGuidewireRoutes,
  createGuidewireTeam,
} from "@/wab/server/routes/internal/guidewire";
import {
  checkDomain,
  getDomainsForProject,
  getHostingSettings,
  getProjectIdAndTokenForDomain,
  getRecordPlasmicHostingHit,
  recordPlasmicHostingHit,
  revalidatePlasmicHosting,
  setCustomDomainForProject,
  setShowHostingBadge,
  setSubdomainForProject,
  updateHostingSettings,
} from "@/wab/server/routes/internal/hosting";
import * as paymentRoutes from "@/wab/server/routes/internal/payment";
import { startFreeTrial } from "@/wab/server/routes/internal/team-plans";
import { billingServer } from "@/wab/server/routes/team-plans";
import {
  adminOnly,
  createTsRestEndpoints,
  withTransaction,
} from "@/wab/server/routes/util";
import { billingContract } from "@/wab/shared/api/internal/billing";
import { Application, Request, Response } from "express";

function addPublicHostingRoutes(app: Application) {
  app.get(
    "/api/v1/project-token-for-domain",
    getProjectIdAndTokenForDomain
  );
  app.get("/api/v1/hosting-hit", withTransaction(getRecordPlasmicHostingHit));
  app.post("/api/v1/hosting-hit", withTransaction(recordPlasmicHostingHit));
}

function addHostingRoutes(app: Application) {
  addPublicHostingRoutes(app);

  app.get(
    "/api/v1/domains-for-project/:projectId",
    getDomainsForProject
  );
  app.get("/api/v1/check-domain", checkDomain);
  app.put(
    "/api/v1/custom-domain-for-project",
    withTransaction(setCustomDomainForProject)
  );
  app.put(
    "/api/v1/subdomain-for-project",
    withTransaction(setSubdomainForProject)
  );

  app.post(
    "/api/v1/revalidate-hosting",
    withTransaction(revalidatePlasmicHosting)
  );
  app.put(
    "/api/v1/projects/:projectId/hosting/badge",
    withTransaction(setShowHostingBadge)
  );
  app.put(
    "/api/v1/projects/:projectId/hosting/robots",
    withTransaction(updateHostingSettings)
  );

  app.get("/api/v1/plasmic-hosting/:projectId", getHostingSettings);
  app.put(
    "/api/v1/plasmic-hosting/:projectId",
    withTransaction(updateHostingSettings)
  );
}

function addCopilotRoutes(app: Application) {
  /**
   * Copilot routes
   */
  const ipPerMinuteRateLimiter = createRateLimiter({
    windowMs: 1 * 60 * 1000, // 1 minute
    limit: 2,
  });
  app.post("/api/v1/copilot", withTransaction(queryCopilot));
  app.post("/api/v1/copilot/ui", withTransaction(queryUiCopilot));
  app.post(
    "/api/v1/copilot/ui/public",
    ipPerMinuteRateLimiter,
    withTransaction(queryPublicUiCopilot)
  );
  app.post("/api/v1/copilot-feedback", withTransaction(saveCopilotFeedback));
  app.get(
    "/api/v1/copilot-feedback",
    adminOnly,
    queryCopilotFeedback
  );
}

export function addInternalRoutes(app: Application) {
  addHostingRoutes(app);
  addPaymentRoutes(app);
  addCopilotRoutes(app);
  addGuidewireRoutes(app);
}

export function addInternalIntegrationsRoutes(app: Application) {
  addPublicHostingRoutes(app);
}

function addPaymentRoutes(app: Application) {
  createTsRestEndpoints(billingContract, billingServer, app);

  /**
   * Billing
   */
  app.post("/api/v1/teams/:teamId/trial", withTransaction(startFreeTrial));
  app.post(
    "/api/v1/teams/:teamId/paymentMethod",
    withTransaction(paymentRoutes.updatePaymentMethod)
  );
  app.post(
    "/api/v1/billing/subscription/create",
    withTransaction(paymentRoutes.createSubscription)
  );
  app.get(
    "/api/v1/billing/subscription/:teamId",
    withTransaction(paymentRoutes.getSubscription)
  );
  app.post(
    "/api/v1/billing/subscription/create",
    withTransaction(paymentRoutes.createSubscription)
  );
  app.put(
    "/api/v1/billing/subscription/:teamId",
    withTransaction(paymentRoutes.changeSubscription)
  );
  app.delete(
    "/api/v1/billing/subscription/:teamId",
    withTransaction(paymentRoutes.cancelSubscription)
  );
  app.post(
    "/api/v1/billing/setup-intent/:teamId",
    withTransaction(paymentRoutes.createSetupIntent)
  );
}

export async function customCreateTeam(req: Request, res: Response) {
  if (req.headers["x-gw-api-access-token"]) {
    await createGuidewireTeam(req, res);
    return true;
  }
  return false;
}

export function isCustomPublicApiRequest(req: Request) {
  // Guidewire
  if (req.headers?.["x-gw-api-access-token"]) {
    return true;
  }
  return false;
}
