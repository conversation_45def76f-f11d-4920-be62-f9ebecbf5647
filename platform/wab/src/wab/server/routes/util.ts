import {
  ANON_USER,
  DbMgr,
  SUPER_USER,
  normalActor,
  teamActor,
} from "@/wab/server/db/DbMgr";
import { User } from "@/wab/server/entities/Entities";
import "@/wab/server/extensions";
import {
  BadRequestError,
  ForbiddenError,
  UnauthorizedError,
} from "@/wab/shared/ApiErrors/errors";
import { CmsIdAndToken, ProjectIdAndToken } from "@/wab/shared/ApiSchema";
import { asyncWrapper, omitNils } from "@/wab/shared/common";
import { AppRouter } from "@ts-rest/core";
import { createExpressEndpoints } from "@ts-rest/express";
import {
  RouterImplementation,
  TsRestExpressOptions,
} from "@ts-rest/express/src/lib/types";
import {
  NextFunction,
  Request,
  Response,
  type IRouter,
} from "express-serve-static-core";
import L from "lodash";
import type { Readable } from "stream";
import { ZodIssue } from "zod";

/**
 * Request that is compatible with normal `Request`s and ts-rest `Request`s.
 *
 * ts-rest modifies the `query` field so it matches the shape of its schema,
 * which makes it incompatible with normal Express requests.
 * Therefore, we omit `query` from the type since it's usually unnecessary for
 * generic request handling code.
 *
 * Some fields on `Request` return `this`, which is also problematic since it
 * references the original `Request` type. This is handled by omitting the
 * fields `setTimeout` and all fields of the extended class `Readable`.
 */
export type CompatRequest = Omit<
  Request,
  "query" | "setTimeout" | keyof Readable
>;

export function hasUser(req: CompatRequest) {
  return !!req.user;
}

export function getUser(
  req: CompatRequest,
  opts?: { allowUnverifiedEmail: boolean }
) {
  if (!req.user) {
    throw new UnauthorizedError();
  }
  if (!opts?.allowUnverifiedEmail && req.user.waitingEmailVerification) {
    throw new UnauthorizedError();
  }
  return req.user;
}

export function userDbMgr(
  req: CompatRequest,
  opts?: { allowUnverifiedEmail: boolean }
) {
  const isSpy = req.cookies["plasmic-spy"] === "true";
  let dbMgr = new DbMgr(
    req.txMgr,
    req.user
      ? normalActor(getUser(req, opts).id, isSpy)
      : req.apiTeam
      ? teamActor(req.apiTeam.id)
      : ANON_USER,
    {
      projectIdsAndTokens:
        (req.body.projectIdsAndTokens as ProjectIdAndToken[] | undefined) ??
        parseProjectIdsAndTokensHeader(
          req.headers["x-plasmic-api-project-tokens"]
        ),
      teamApiToken: req.body.teamApiToken ?? req.headers["x-plasmic-api-token"],
      temporaryTeamApiToken:
        req.body.sessionToken ?? req.headers["x-plasmic-api-session-token"],
      cmsIdsAndTokens: parseCmsIdsAndTokensHeader(
        req.headers["x-plasmic-api-cms-tokens"]
      ),
    }
  );
  return dbMgr;
}

export function parseProjectIdsAndTokensHeader(value: any) {
  if (!value || typeof value !== "string") {
    return undefined;
  }

  const parsed = value
    .trim()
    .split(",")
    .map((val) => {
      const [projectId, projectApiToken] = val.split(":");
      if (!projectId || !projectApiToken) {
        throw new BadRequestError(
          `Invalid values for x-plasmic-api-project-tokens header`
        );
      }
      return {
        projectId: projectId.trim(),
        projectApiToken: projectApiToken.trim(),
      } as ProjectIdAndToken;
    });

  return parsed.length === 0 ? undefined : parsed;
}

export function parseCmsIdsAndTokensHeader(value: any) {
  if (!value || typeof value !== "string") {
    return undefined;
  }

  const parsed = value
    .trim()
    .split(",")
    .map((val) => {
      const [databaseId, token] = val.split(":");
      if (!databaseId || !token) {
        throw new BadRequestError(
          `Invalid values for x-plasmic-api-cms-tokens header`
        );
      }
      return {
        databaseId: databaseId.trim(),
        token: token.trim(),
      } as CmsIdAndToken;
    });

  return parsed.length === 0 ? undefined : parsed;
}

export function superDbMgr(req: CompatRequest) {
  return new DbMgr(req.txMgr, SUPER_USER);
}

export function makeUserTraits(user: User) {
  return omitNils({
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    role: user.role,
    domain: user.email.split("@")[1],
    source: user.source,
    createdAt: user.createdAt.toISOString(),
    ...user.surveyResponse,
  });
}

/**
 * Parses metadata from the CLI
 * We want an object that we can spread into the event properties
 * @param m
 */
export function parseMetadata(m?: any) {
  if (!m) {
    return {};
  } else if (typeof m === "object") {
    return m;
  } else {
    return {
      metadata: m,
    };
  }
}

export function parseQueryParams(req: Request) {
  return L.mapValues(req.query, (v) => JSON.parse(v as string));
}

export function adminOnly(req: Request, _res: Response, next: NextFunction) {
  const user = req.user as User | undefined;
  if (
    user?.email &&
    req.config.adminEmails.includes(user.email.toLowerCase())
  ) {
    next();
  } else {
    next(new ForbiddenError());
  }
}

export function adminOrDevelopmentEnvOnly(
  req: Request,
  res: Response,
  next: NextFunction
) {
  if (process.env.NODE_ENV !== "production") {
    next();
  } else {
    adminOnly(req, res, next);
  }
}

/**
 * Wraps a route handler in a database transaction.
 * The transaction will automatically commit on success or rollback on error.
 * Use this for all routes that perform database writes.
 */
export function withTransaction(
  f: (req: Request, res: Response, next: NextFunction) => Promise<void> | void
) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      await req.con.transaction(async (txMgr) => {
        req.txMgr = txMgr;
        await asyncWrapper(f)(req, res, next);
      });
    } catch (error) {
      next(error);
    }
  };
}

export function createTsRestEndpoints<TRouter extends AppRouter>(
  contract: TRouter,
  server: RouterImplementation<TRouter>,
  app: IRouter,
  options?: Omit<TsRestExpressOptions<TRouter>, "requestValidationErrorHandler">
): void {
  createExpressEndpoints(contract, server, app, {
    // Convert to BadRequestError, let our error middleware handle this
    requestValidationErrorHandler: (err, _req, _res, _next) => {
      function issueMap(issue: ZodIssue) {
        return `${issue.path.join(".")}: ${issue.message}`;
      }

      const issues = {};
      if (err.headers) {
        issues["headers"] = err.headers.issues.map(issueMap);
      }
      if (err.pathParams) {
        issues["pathParams"] = err.pathParams.issues.map(issueMap);
      }
      if (err.query) {
        issues["query"] = err.query.issues.map(issueMap);
      }
      if (err.body) {
        issues["body"] = err.body.issues.map(issueMap);
      }

      throw new BadRequestError("Request validation failed. See issues.", {
        issues,
      });
    },
    ...options,
  });
}
