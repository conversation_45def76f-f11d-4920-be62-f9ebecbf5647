import { methodForwarder } from "@/wab/commons/methodForwarder";
import { PinoLogger } from "@/wab/server/observability/PinoLogger";
import { initAmplitudeNode } from "@/wab/server/observability/amplitude-node";
import { initPosthogNode } from "@/wab/server/observability/posthog-node";
import { Analytics } from "@/wab/shared/observability/Analytics";
import { ConsoleLogAnalytics } from "@/wab/shared/observability/ConsoleLogAnalytics";
import { Logger } from "@/wab/shared/observability/Logger";

const loggerInstance: Logger = new PinoLogger();

// TODO: Move to environment variable.
const AMPLITUDE_API_KEY = "1efde847a1dd16e6dbf8a242c1e2dd07";
const POSTHOG_API_KEY = "phc_eaI1hFsPRIZkmwrXaSGRNDh4H9J3xdh1j9rgNy27NgP";
const POSTHOG_API_HOST = "https://us.i.posthog.com";

export function initAnalyticsFactory(opts: {
  production: boolean;
}): () => Analytics {
  const amplitudeFactory = initAmplitudeNode({
    apiKey: AMPLITUDE_API_KEY,
  });
  const posthogFactory = initPosthogNode({
    apiKey: POSTHOG_API_KEY,
    apiHost: POSTHOG_API_HOST,
  });
  return () => {
    const amplitude = amplitudeFactory();
    const posthog = posthogFactory();
    return opts.production
      ? methodForwarder(amplitude, posthog)
      : methodForwarder(new ConsoleLogAnalytics(), amplitude, posthog);
  };
}

export function logger(): Logger {
  return loggerInstance;
}
