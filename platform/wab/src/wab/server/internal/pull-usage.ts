import { DEFAULT_DATABASE_URI } from "@/wab/server/config";
import { DbMgr, SUPER_USER } from "@/wab/server/db/DbMgr";
import { createDbConnection } from "@/wab/server/db/dbcli-utils";
import { Team } from "@/wab/server/entities/Entities";
import { extractBundleKeyProjectIds } from "@/wab/server/loader/gen-code-bundle";
import { extractProjectId } from "@/wab/server/loader/resolve-projects";
import { logger } from "@/wab/server/observability";
import { logError } from "@/wab/server/server-util";
import { recordPulledUsage } from "@/wab/server/usage/usage";
import { withSpan } from "@/wab/server/util/apm-util";
import { ProjectId } from "@/wab/shared/ApiSchema";
import { UsageType } from "@/wab/shared/api/internal/usage";
import { notNil, spawn } from "@/wab/shared/common";
import { Range, mkRange } from "@/wab/shared/range";
import { BigQuery } from "@google-cloud/bigquery";
import { Command } from "commander";
import { regex } from "regex";
import { Temporal } from "temporal-polyfill";

if (require.main === module) {
  spawn(main());
}

export async function main() {
  await withSpan("pull-usage", async () => {
    const opts = new Command("pull usage")
      .option("-db, --dburi <dburi>", "Database uri", DEFAULT_DATABASE_URI)
      .requiredOption(
        "-t, --time <ISO 8601 timestamp>",
        "start time in ISO 8601 format"
      )
      .option(
        "-d, --duration <ISO 8601 duration>",
        "duration in ISO 8601 notation",
        "PT1H"
      )
      .parse(process.argv)
      .opts();

    const duration = Temporal.Duration.from(opts.duration);
    const startTime = Temporal.Instant.from(opts.time);
    const usageRange = mkRange(startTime, startTime.add(duration), {
      upperExclusive: true,
    });

    const con = await createDbConnection(opts.dburi);
    const em = con.createEntityManager();
    const sudoDbMgr = new DbMgr(em, SUPER_USER);

    await withSpan("pull usage from loader_light_ops_cdn", () =>
      pullLoaderCdnUsage({
        dbMgr: sudoDbMgr,
        datasetId: "loader_light_ops_cdn",
        datasetLocation: "us-central1",
        usageRange,
      })
    );
    await withSpan("pull usage from loader_cdn", () =>
      pullLoaderCdnUsage({
        dbMgr: sudoDbMgr,
        datasetId: "loader_cdn",
        datasetLocation: "us-central1",
        usageRange,
      })
    );
  });
}

async function pullLoaderCdnUsage({
  dbMgr,
  datasetId,
  datasetLocation,
  usageRange,
}: {
  dbMgr: DbMgr;
  datasetId: string;
  datasetLocation: string;
  usageRange: Range<Temporal.Instant>;
}): Promise<void> {
  // https://cloud.google.com/bigquery/docs/paging-results#bigquery_browse_table-nodejs
  const bq = new BigQuery();
  const dataset = bq.dataset(datasetId, {
    location: datasetLocation,
  });

  (await bq.getJobs()).map((jobs) => jobs);
  // This queries a BigQuery partitioned table of Cloud CDN logs.
  const stream = dataset.createQueryStream(
    `SELECT httpRequest.requestUrl AS url, COUNT(1) AS count
FROM \`requests\`
WHERE timestamp >= '${usageRange.lower}'
AND timestamp < '${usageRange.upper}'
AND httpRequest.requestMethod = 'GET'
AND httpRequest.status BETWEEN 200 AND 399
GROUP BY url
ORDER BY count DESC`
  );

  const projectToTeamCache = new Map<ProjectId, Team | undefined>();
  for await (const row of stream) {
    let { url, count } = row as { url: string; count: number };

    const extracted = extractLoaderUrlData(url);
    if (!extracted) {
      logError(new Error(`pullLoaderCdnUsage found unexpected URL: ${url}`));
      continue;
    }
    const { type, projectIds } = extracted;

    const teams = new Set(
      await Promise.all(
        projectIds.map(async (projectId) => {
          if (projectToTeamCache.has(projectId)) {
            return projectToTeamCache.get(projectId);
          }

          const team = await dbMgr.getTeamByProjectId(projectId, true);
          if (!team) {
            logError(
              new Error(
                `pullLoaderCdnUsage could not find team for project ${projectId}`
              )
            );
          }
          projectToTeamCache.set(projectId, team);
          return team;
        })
      )
    );

    logger().info(`Recording ${count} ${type} usages to ${url}`);
    await Promise.allSettled(
      [...teams].filter(notNil).map((team) => {
        const teamId = team.id;
        const userId = team.createdById; // TODO: attribute to someone else?
        logger().info(`=> team:${teamId} user:${userId}`);
        return recordPulledUsage(dbMgr, {
          // make a consistent, unique ID for this entry
          id: `${usageRange.lower.toString()}_${type}_${teamId}_${userId}_${url}`,
          type,
          usageRange,
          teamId,
          userId,
          value: count,
        });
      })
    );
  }
}

const GENERAL_LOADER_REGEXP = regex`
^
/api/v1/loader
/(?<type> html | repr-v2 | repr-v3 )
/(?<version> preview | published | versioned )
/(?<projectIdSpec> [^\/]+ )
`;

function extractLoaderUrlData(url: string): {
  type: UsageType;
  projectIds: ProjectId[];
} | null {
  const parsedUrl = new URL(url);

  // Extract preview|published|versioned from pathname
  // Extract projectIds from searchParams
  const searchParams = new URLSearchParams(parsedUrl.search);
  switch (parsedUrl.pathname) {
    // /api/v1/loader/chunks?bundleKey=bundle%2Fcb%3D19%2FloaderVersion%3D10%2Fps%3Dproj123%402.0.3%2Cproj456%400.1.0%2Fplatform%3Dnextjs%2FbrowserOnly%3Dfalse%2Fopts%3D8bdfc7656ef60c0451f269920cc8baeb33e65e309cd627d20ce6df06a598459e&fileName=chunk-3BDEXIIY.js%2Cchunk-7P7R4QRZ.js%2Cchunk-CNC4F3G7.js%2Cchunk-DB2QAB4F.js%2Cchunk-DBN4EN6V.js%2Cchunk-GSAK3SBP.js%2Cchunk-JXXNWGJS.js%2Cchunk-LWHQTFXI.js%2Cchunk-MMVGZIO7.js%2Cchunk-NWHCE4PN.js%2Cchunk-PWLCJYEK.js%2Cchunk-PWV4WILB.js%2Cchunk-S3LRDUOA.js%2Cchunk-S4K54YVJ.js%2Cchunk-T76UEMU2.js%2Cchunk-UEL76YOM.js%2Ccomp__M3zcnYginwdy.js%2Ccomp__XAh1sC0gY3Zg.js%2Ccomp__ZEEMhSZq7_Pc.js
    case "/api/v1/loader/chunks":
      return {
        type: "loader-chunk",
        projectIds: extractBundleKeyProjectIds(searchParams.get("bundleKey")!),
      };

    // /api/v1/loader/code/preview?platform=nextjs&projectId=proj123&projectId=pro456
    case "/api/v1/loader/code/preview":
      return {
        type: "loader-code-preview",
        projectIds: searchParams.getAll("projectId").map(extractProjectId),
      };

    // /api/v1/loader/code/published?platform=nextjs&projectId=proj123&projectId=proj456
    case "/api/v1/loader/code/published":
      return {
        type: "loader-code-published",
        projectIds: searchParams.getAll("projectId").map(extractProjectId),
      };

    // /api/v1/loader/code/versioned?cb=20&platform=nextjs&loaderVersion=10&projectId=proj123%401.2.3
    case "/api/v1/loader/code/versioned":
      return {
        type: "loader-code-versioned",
        projectIds: searchParams.getAll("projectId").map(extractProjectId),
      };

    default:
      // Now match general loader paths. Examples:
      // - /api/v1/loader/html/preview/proj123/CompA?hydrate=1&embedHydrate=1
      // - /api/v1/loader/repr-v2/published/proj123
      // - /api/v1/loader/repr-v3/versioned/proj123
      const match = parsedUrl.pathname.match(GENERAL_LOADER_REGEXP);
      if (match) {
        const groups = match.groups as {
          type: "html" | "repr-v2" | "repr-v3";
          version: "preview" | "published" | "versioned";
          projectIdSpec: string;
        };
        return {
          type: `loader-${groups.type}-${groups.version}`,
          projectIds: [extractProjectId(groups.projectIdSpec)],
        };
      }

      return null;
  }
}

export const _testonly_ = {
  extractLoaderUrlData,
};
