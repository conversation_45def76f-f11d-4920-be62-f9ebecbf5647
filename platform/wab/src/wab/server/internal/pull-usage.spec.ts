import { _testonly_ } from "./pull-usage";

const { extractLoaderUrlData } = _testonly_;

describe("extractLoaderUrlData", () => {
  describe("/api/v1/loader/chunks URLs", () => {
    it("should parse chunks URL with bundleKey", () => {
      const url =
        "https://codegen.plasmic.app/api/v1/loader/chunks?bundleKey=bundle%2Fcb%3D19%2FloaderVersion%3D10%2Fps%3Dproj123%402.0.3%2Cproj456%400.1.0%2Fplatform%3Dnextjs%2FbrowserOnly%3Dfalse%2Fopts%3D8bdfc7656ef60c0451f269920cc8baeb33e65e309cd627d20ce6df06a598459e&fileName=chunk-3BDEXIIY.js%2Cchunk-7P7R4QRZ.js%2Cchunk-CNC4F3G7.js%2Cchunk-DB2QAB4F.js%2Cchunk-DBN4EN6V.js%2Cchunk-GSAK3SBP.js%2Cchunk-JXXNWGJS.js%2Cchunk-LWHQTFXI.js%2Cchunk-MMVGZIO7.js%2Cchunk-NWHCE4PN.js%2Cchunk-PWLCJYEK.js%2Cchunk-PWV4WILB.js%2Cchunk-S3LRDUOA.js%2Cchunk-S4K54YVJ.js%2Cchunk-T76UEMU2.js%2Cchunk-UEL76YOM.js%2Ccomp__M3zcnYginwdy.js%2Ccomp__XAh1sC0gY3Zg.js%2Ccomp__ZEEMhSZq7_Pc.js";
      expect(extractLoaderUrlData(url)).toEqual({
        type: "loader-chunk",
        projectIds: ["proj123", "proj456"],
      });
    });
  });

  describe("/api/v1/loader/code URLs", () => {
    it("should parse preview URL with multiple projectIds", () => {
      const url =
        "https://codegen.plasmic.app/api/v1/loader/code/preview?platform=nextjs&projectId=proj123&projectId=proj456";
      expect(extractLoaderUrlData(url)).toEqual({
        type: "loader-code-preview",
        projectIds: ["proj123", "proj456"],
      });
    });
    it("should parse published URL with multiple projectIds", () => {
      const url =
        "https://codegen.plasmic.app/api/v1/loader/code/published?platform=nextjs&projectId=proj123&projectId=proj456";
      expect(extractLoaderUrlData(url)).toEqual({
        type: "loader-code-published",
        projectIds: ["proj123", "proj456"],
      });
    });
    it("should parse versioned URL with versioned projectId", () => {
      const url =
        "https://codegen.plasmic.app/api/v1/loader/code/versioned?cb=20&platform=nextjs&loaderVersion=10&projectId=proj123%401.2.3";
      expect(extractLoaderUrlData(url)).toEqual({
        type: "loader-code-versioned",
        projectIds: ["proj123"],
      });
    });
  });

  describe("non-code /api/v1/loader URLs", () => {
    it("should parse HTML preview URL", () => {
      const url =
        "https://codegen.plasmic.app/api/v1/loader/html/preview/proj123/CompA";
      expect(extractLoaderUrlData(url)).toEqual({
        type: "loader-html-preview",
        projectIds: ["proj123"],
      });
    });
    it("should parse repr-v2 published URL", () => {
      const url =
        "https://codegen.plasmic.app/api/v1/loader/repr-v2/published/proj123";
      expect(extractLoaderUrlData(url)).toEqual({
        type: "loader-repr-v2-published",
        projectIds: ["proj123"],
      });
    });
    it("should parse repr-v3 versioned URL", () => {
      const url =
        "https://codegen.plasmic.app/api/v1/loader/repr-v3/versioned/proj123";
      expect(extractLoaderUrlData(url)).toEqual({
        type: "loader-repr-v3-versioned",
        projectIds: ["proj123"],
      });
    });
  });

  it("should return null for invalid URLs", () => {
    expect(
      extractLoaderUrlData(
        "https://codegen.plasmic.app/api/v1/loader/html/invalid/proj123/CompA"
      )
    ).toBeNull();
    expect(
      extractLoaderUrlData("https://codegen.plasmic.app/api/v1/some/other/path")
    ).toBeNull();
  });
});
