{"platform": "react", "code": {"lang": "ts", "scheme": "blackbox", "reactRuntime": "classic"}, "style": {"scheme": "css-modules", "defaultStyleCssFilePath": "wab/client/plasmic/PP__plasmic__default_style.module.css"}, "images": {"scheme": "files"}, "tokens": {"scheme": "theo", "tokensFilePath": "wab/styles/plasmic-tokens.theo.json"}, "srcDir": "./src", "defaultPlasmicDir": "./wab/client/plasmic", "projects": [{"projectId": "aukbrhkegRkQ6KizvhdUPT", "projectName": "[PlasmicKit] Left Pane", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/PP__plasmickit_left_pane.module.css", "components": [{"id": "kkbHZ8nmgGH", "name": "ActivityFeedItem", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/components/widgets/plasmic/PlasmicActivityFeedItem.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/plasmic/ActivityFeedItem.tsx"}, "cssFilePath": "wab/client/components/widgets/plasmic/PlasmicActivityFeedItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "DdZ3EM2HFAD", "name": "ActivityFeed", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/components/widgets/plasmic/PlasmicActivityFeed.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/plasmic/ActivityFeed.tsx"}, "cssFilePath": "wab/client/components/widgets/plasmic/PlasmicActivityFeed.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "XLa52PvduIy", "name": "LeftPaneHeader", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicLeftPaneHeader.tsx", "importSpec": {"modulePath": "wab/client/components/studio/LeftPaneHeader.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit/PlasmicLeftPaneHeader.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "TqAPn0srTq", "name": "LeftSearchPanel", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicLeftSearchPanel.tsx", "importSpec": {"modulePath": "wab/client/components/studio/LeftSearchPanel.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit/PlasmicLeftSearchPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "ZsFxxgE4E8", "name": "LeftMixinsPanel", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicLeftMixinsPanel.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar/MixinControls.tsx", "exportName": "MixinsPanel"}, "cssFilePath": "wab/client/plasmic/plasmic_kit/PlasmicLeftMixinsPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "9I47RGPv62", "name": "LeftThemesPanel", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftThemesPanel.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar/ThemesControls.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftThemesPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "ECu8FUyP0f3", "name": "LeftImagesPanel", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicLeftImagesPanel.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar/image-asset-controls.tsx", "exportName": "ImageAssetsPanel"}, "cssFilePath": "wab/client/plasmic/plasmic_kit/PlasmicLeftImagesPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "5oz1qmvGBe", "name": "LeftFontsPanel", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicLeftFontsPanel.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar/UserManagedFonts.tsx", "exportName": "UserManagedFontsPanel"}, "cssFilePath": "wab/client/plasmic/plasmic_kit/PlasmicLeftFontsPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "MeRxD_0BtJ", "name": "LeftImportsPanel", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicLeftImportsPanel.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar/ProjectDependencies.tsx", "exportName": "ProjectDependenciesPanel"}, "cssFilePath": "wab/client/plasmic/plasmic_kit/PlasmicLeftImportsPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "YldGgVsq6N", "name": "LeftVersionsPanel", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicLeftVersionsPanel.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/versions-tab.tsx", "exportName": "VersionsTab"}, "cssFilePath": "wab/client/plasmic/plasmic_kit/PlasmicLeftVersionsPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "isQPD0RPCw", "name": "AddDrawerItem", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicAddDrawerItem.tsx", "importSpec": {"modulePath": "wab/client/components/studio/add-drawer/AddDrawerItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicAddDrawerItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "yc4AfGXkNH", "name": "LeftActivitiesPanel", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftActivitiesPanel.tsx", "importSpec": {"modulePath": "wab/client/components/LeftActivitiesPanel.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftActivitiesPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "bDbzY5jXLz", "name": "LeftGeneralTokensPanel", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftGeneralTokensPanel.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar/LeftGeneralTokensPanel.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftGeneralTokensPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "eMjSZ8G7mG", "name": "TokenTypeHeader", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicTokenTypeHeader.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar/TokenTypeHeader.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicTokenTypeHeader.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "7Wsvgu6cRd", "name": "LeftComponentsPanel", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftComponentsPanel.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar/LeftComponentsPanel.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftComponentsPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicGeneralTokenControl.module.css", "id": "0LQGzuFK6d", "name": "GeneralTokenControl", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicGeneralTokenControl.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar/GeneralTokenControl.tsx"}, "scheme": "blackbox", "componentType": "component"}, {"id": "JyqCOl0Ccj", "name": "ColorTokenControl", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicColorTokenControl.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar/ColorTokenControl.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicColorTokenControl.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "l7y_rhJyMt2", "name": "LeftTabStrip", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftTabStrip.tsx", "importSpec": {"modulePath": "wab/client/components/studio/LeftTabStrip.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftTabStrip.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "1q_JapBg7U", "name": "LeftTabButton", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftTabButton.tsx", "importSpec": {"modulePath": "wab/client/components/studio/LeftTabButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftTabButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "avrERxAp81S", "name": "LeftPane", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftPane.tsx", "importSpec": {"modulePath": "wab/client/components/studio/LeftPane.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftPane.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "wXKvVcr82I", "name": "LeftPagesPanel", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftPagesPanel.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar/LeftPagesPanel.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftPagesPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "V25hk8i--ck", "name": "PublishDialogContent", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicPublishDialogContent.tsx", "importSpec": {"modulePath": "wab/client/components/PublishDialogContent.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicPublishDialogContent.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "EeT-6P6YTW", "name": "LeftSettingsPanel", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftSettingsPanel.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar/LeftSettingsPanel.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftSettingsPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "OzaoSbFLbl", "name": "LeftSplitsPanel", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftSplitsPanel.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar/LeftSplitsPanel.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftSplitsPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "bobcNPtaTq", "name": "CollapseToggle", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicCollapseToggle.tsx", "importSpec": {"modulePath": "wab/client/components/CollapseToggle.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicCollapseToggle.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "nmt_YiclQJk", "name": "DefaultStylesPanel", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicDefaultStylesPanel.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar/DefaultStylesPanel.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicDefaultStylesPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "hudLjkQJbU", "name": "ThemeLayoutPanel", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicThemeLayoutPanel.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar/ThemeLayoutPanel.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicThemeLayoutPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "neIW4UOiRU", "name": "PresetsSelector", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicPresetsSelector.tsx", "importSpec": {"modulePath": "wab/client/components/PresetsSelector.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicPresetsSelector.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "kZ3Ar3RnLt", "name": "PresetGroup", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicPresetGroup.tsx", "importSpec": {"modulePath": "wab/client/components/PresetGroup.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicPresetGroup.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "eS_Bw5U3wr", "name": "Preset", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicPreset.tsx", "importSpec": {"modulePath": "wab/client/components/Preset.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicPreset.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "ss1yYyG4Pi", "name": "AddButton", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicAddButton.tsx", "importSpec": {"modulePath": "wab/client/components/AddButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicAddButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "93uVZfRMCA", "name": "FilterButton", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicFilterButton.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/FilterButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicFilterButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "xymZo1AIeU", "name": "LeftLintIssuesPanel", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftLintIssuesPanel.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar/LeftLintIssuesPanel.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftLintIssuesPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "T_OF2Q8rJc1U", "name": "ThemeInitialStylesPanel", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicThemeInitialStylesPanel.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar/ThemeInitialStylesPanel.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicThemeInitialStylesPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "d693eBfNDs7j", "name": "MultiAssetsActions", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicMultiAssetsActions.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar/MultiAssetsActions.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicMultiAssetsActions.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "WwK9TyWdjIfT", "name": "LeftGeneralDataTokensPanel", "type": "managed", "projectId": "aukbrhkegRkQ6KizvhdUPT", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftGeneralDataTokensPanel.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar/LeftGeneralDataTokensPanel.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicLeftGeneralDataTokensPanel.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [{"id": "CD14l2YUnk", "name": "IconIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_left_pane/icons/PlasmicIcon__Icon.tsx"}], "images": [{"id": "9D5nIFKHJ", "name": "image", "filePath": "wab/client/plasmic/plasmic_kit_left_pane/images/image.svg"}], "projectApiToken": "ULMSLjLUhtmoY8ad8fSLWbk23c1Y7mAoq78RUQk4Y3gry79a0RnaK8A8Ynma15DBYMGGh1iybREKGHdZN4Uw", "codeComponents": [{"id": "CSs99c3ImQ", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "mDb_PyZCQd", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "indirect": false, "globalContextsFilePath": "wab/client/plasmic/plasmic_kit_left_pane/PlasmicGlobalContextsProvider.tsx", "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "ooL7EhXDmFQWnW9sxtchhE", "projectName": "[PlasmicKit] Dashboard", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/PP__plasmickit_dashboard.module.css", "components": [{"id": "2FvZipCkyxl", "name": "ProjectListItem", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicProjectListItem.tsx", "importSpec": {"modulePath": "wab/client/components/ProjectListItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit/PlasmicProjectListItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "diKNfA_-roE", "name": "ProjectListSection", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicProjectListSection.tsx", "importSpec": {"modulePath": "wab/client/components/ProjectListSection.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit/PlasmicProjectListSection.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "-k-p1OXXphn", "name": "ProjectList", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicProjectList.tsx", "importSpec": {"modulePath": "wab/client/components/ProjectList.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit/PlasmicProjectList.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "CCsDeqqYeoM", "name": "StarterProject", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicStarterProject.tsx", "importSpec": {"modulePath": "wab/client/components/StarterProject.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit/PlasmicStarterProject.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "u6dq5eydCj", "name": "StarterGroup", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicStarterGroup.tsx", "importSpec": {"modulePath": "wab/client/components/StarterGroup.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit/PlasmicStarterGroup.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "IQU7DmjqUs", "name": "Link", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicLink.tsx", "importSpec": {"modulePath": "wab/client/components/Link.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicLink.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "u7TII072Seb", "name": "Copy<PERSON><PERSON><PERSON>", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicCopyButton.tsx", "importSpec": {"modulePath": "wab/client/components/CopyButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicCopyButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "s87vSHZpzQ", "name": "NewProjectModal", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicNewProjectModal.tsx", "importSpec": {"modulePath": "wab/client/components/NewProjectModal.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicNewProjectModal.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "XxbnrpTDqu", "name": "HostUrlInput", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicHostUrlInput.tsx", "importSpec": {"modulePath": "wab/client/components/HostUrlInput.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicHostUrlInput.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "6_CfQ5GVLku", "name": "HostProtocolSelect", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicHostProtocolSelect.tsx", "importSpec": {"modulePath": "wab/client/components/HostProtocolSelect.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicHostProtocolSelect.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "select"}, {"id": "aHgWgR3OVni", "name": "HostProtocolSelect__Option", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicHostProtocolSelect__Option.tsx", "importSpec": {"modulePath": "wab/client/components/HostProtocolSelect__Option.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicHostProtocolSelect__Option.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "select-option"}, {"id": "FB-WsFik1_I", "name": "HostProtocolSelect__OptionGroup", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicHostProtocolSelect__OptionGroup.tsx", "importSpec": {"modulePath": "wab/client/components/HostProtocolSelect__OptionGroup.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicHostProtocolSelect__OptionGroup.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "select-option-group"}, {"id": "WAelYWWWRyr", "name": "HostProtocolSelect__Overlay", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicHostProtocolSelect__Overlay.tsx", "importSpec": {"modulePath": "wab/client/components/HostProtocolSelect__Overlay.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicHostProtocolSelect__Overlay.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "triggered-overlay"}, {"id": "nSkQWLjK-B", "name": "DefaultLayout", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicDefaultLayout.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/DefaultLayout.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicDefaultLayout.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "82ZzbE4hazN", "name": "NavButton", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicNavButton.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/NavButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicNavButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "VfLXr8Uqdd", "name": "SettingsPage", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicSettingsPage.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/SettingsPage.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicSettingsPage.module.css", "scheme": "blackbox", "componentType": "page"}, {"id": "r2L4x5kulJ", "name": "Shared", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicShared.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/Shared.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicShared.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "LW4T36Sq58", "name": "TeamSettingsPage", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicTeamSettingsPage.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/TeamSettingsPage.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicTeamSettingsPage.module.css", "scheme": "blackbox", "componentType": "page"}, {"id": "zRIUpVU0Cm8", "name": "TeamSettings", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicTeamSettings.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/TeamSettings.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicTeamSettings.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "3jXSiWKc1-", "name": "TeamMemberList", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicTeamMemberList.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/TeamMemberList.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicTeamMemberList.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "gdLJj97tYt", "name": "TeamMemberListItem", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicTeamMemberListItem.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/TeamMemberListItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicTeamMemberListItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "ohP9gHR_8Wi", "name": "WorkspacePage", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicWorkspacePage.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/WorkspacePage.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicWorkspacePage.module.css", "scheme": "blackbox", "componentType": "page"}, {"id": "sK-iPs7I1Z", "name": "Bill", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicBill.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/Bill.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicBill.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "5PfErhGRfT", "name": "UpsellCheckout", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicUpsellCheckout.tsx", "importSpec": {"modulePath": "wab/client/components/modals/UpsellCheckout.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicUpsellCheckout.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "C9PGGs5iUd", "name": "UpsellConfirm", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicUpsellConfirm.tsx", "importSpec": {"modulePath": "wab/client/components/modals/UpsellConfirm.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicUpsellConfirm.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "MtL6MGlBxoy", "name": "TeamBilling", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicTeamBilling.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/TeamBilling.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicTeamBilling.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "vM6JbvCArA", "name": "TeamPicker", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicTeamPicker.tsx", "importSpec": {"modulePath": "wab/client/components/modals/TeamPicker.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicTeamPicker.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "3naiwkyPoFj", "name": "TeamPickerItem", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicTeamPickerItem.tsx", "importSpec": {"modulePath": "wab/client/components/modals/TeamPickerItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicTeamPickerItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "O3FCcJ_viT", "name": "TeamPage", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicTeamPage.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/TeamPage.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicTeamPage.module.css", "scheme": "blackbox", "componentType": "page"}, {"id": "pcPdf_yULU3", "name": "TeamPageHeader", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicTeamPageHeader.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/TeamPageHeader.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicTeamPageHeader.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "5cdjGaqBQ4", "name": "WorkspaceSection", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicWorkspaceSection.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/WorkspaceSection.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicWorkspaceSection.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "mdX7wFJOmP", "name": "<PERSON><PERSON><PERSON>er", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicProjectsFilter.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/ProjectsFilter.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicProjectsFilter.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "BOKmukuncx", "name": "ShareButton", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicShareButton.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/ShareButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicShareButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "VqaN_WL-stA", "name": "NavTeamSection", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicNavTeamSection.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/NavTeamSection.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicNavTeamSection.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "Cma6XahJmS", "name": "NavWorkspaceButton", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicNavWorkspaceButton.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/NavWorkspaceButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicNavWorkspaceButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "cOUHQYmbvX", "name": "NavSeparator", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicNavSeparator.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/NavSeparator.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicNavSeparator.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "Mql0DTa_iO", "name": "NavTeamButton", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicNavTeamButton.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/NavTeamButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicNavTeamButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "nMR4ibQ-Ep", "name": "AllProjectsPage", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicAllProjectsPage.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/AllProjectsPage.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicAllProjectsPage.module.css", "scheme": "blackbox", "componentType": "page"}, {"id": "UttGK3xVrb", "name": "EditableResourceName", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicEditableResourceName.tsx", "importSpec": {"modulePath": "wab/client/components/EditableResourceName.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicEditableResourceName.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "mQBPD0GccAU", "name": "UpsellCreditCard", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicUpsellCreditCard.tsx", "importSpec": {"modulePath": "wab/client/components/modals/UpsellCreditCard.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicUpsellCreditCard.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "CpePZH2ffI", "name": "UpsellBanner", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicUpsellBanner.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/UpsellBanner.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicUpsellBanner.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "54ykx6A8G6T", "name": "CmsSection", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicCmsSection.tsx", "importSpec": {"modulePath": "wab/client/components/CmsSection.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicCmsSection.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "DEllwXrn27Q", "name": "CmsListItem", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicCmsListItem.tsx", "importSpec": {"modulePath": "wab/client/components/CmsListItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicCmsListItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "F7n0gyM6hJ6", "name": "CmsPage", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicCmsPage.tsx", "importSpec": {"modulePath": "wab/client/components/CmsPage.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicCmsPage.module.css", "scheme": "blackbox", "componentType": "page"}, {"id": "A4UIAN_FGs", "name": "ContentPage", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicContentPage.tsx", "importSpec": {"modulePath": "wab/client/components/ContentPage.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicContentPage.module.css", "scheme": "blackbox", "componentType": "page"}, {"id": "Ts79yZbRFG", "name": "MenuItem", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicMenuItem.tsx", "importSpec": {"modulePath": "wab/client/components/MenuItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicMenuItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "B2dxgzfI6E", "name": "DataSource", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicDataSource.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/DataSource.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicDataSource.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "G_RLd7TB5Ns", "name": "DatabaseListItem", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicDatabaseListItem.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/DatabaseListItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicDatabaseListItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "O5AxABt3WN", "name": "WorkspaceDataSources", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicWorkspaceDataSources.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/WorkspaceDataSources.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicWorkspaceDataSources.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "89XWXKZUx6q", "name": "DataSourceOption", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicDataSourceOption.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/DataSourceOption.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicDataSourceOption.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "KVpOSX15wJ", "name": "MyPlayground", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicMyPlayground.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/MyPlayground.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicMyPlayground.module.css", "scheme": "blackbox", "componentType": "page"}, {"id": "DWsPKkiyzx1", "name": "StartersSection", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicStartersSection.tsx", "importSpec": {"modulePath": "wab/client/components/StartersSection.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicStartersSection.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "AqMe9uK-Yh", "name": "FreeTrialModal", "type": "managed", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicFreeTrialModal.tsx", "importSpec": {"modulePath": "wab/client/components/dashboard/FreeTrialModal.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicFreeTrialModal.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [{"id": "ApYMo5LVK0", "name": "JoystickIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/icons/PlasmicIcon__Joystick.tsx"}, {"id": "Hyn5Q6kuD9", "name": "HatchIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/icons/PlasmicIcon__Hatch.tsx"}, {"id": "Y08w-xNMit", "name": "ClockIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/icons/PlasmicIcon__Clock.tsx"}, {"id": "UfxL0BbcEe", "name": "Icon18Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/icons/PlasmicIcon__Icon18.tsx"}, {"id": "MHEeMLIhlB", "name": "Icon19Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_dashboard/icons/PlasmicIcon__Icon19.tsx"}], "images": [{"id": "h1Ar4hymw", "name": "<PERSON><PERSON>_<PERSON>_icon.jpg", "filePath": "wab/client/plasmic/plasmic_kit_dashboard/images/kimmySchmidtIconJpg.jpeg"}, {"id": "dQeKTjQST", "name": "image", "filePath": "wab/client/plasmic/plasmic_kit_dashboard/images/image.png"}], "projectApiToken": "YzbgboSZAaDHJCGNghzsdDqkJDAyWIlduYVmCAX0NeqSwwSXgXghqA3IzLsk4G8MZS869d3PmpALwwW47sbw", "codeComponents": [{"id": "6g89xGJ12a", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "0uYnOyPNh4", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}, {"id": "DrSWuPwD87vM", "name": "PricingTooltip", "displayName": "<PERSON><PERSON><PERSON>", "componentImportPath": "./src/wab/client/components/pricing/Tooltip"}], "indirect": false, "globalContextsFilePath": "wab/client/plasmic/plasmic_kit_dashboard/PlasmicGlobalContextsProvider.tsx", "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "kA1Hysr5ZeimtATHTDJz5B", "projectName": "[PlasmicKit] Share Dialog", "version": "latest", "cssFilePath": "wab/client/plasmic/PP__plasmickit_share_dialog.module.css", "components": [{"id": "cWsnP3_PIix", "name": "ShareDialogContent", "type": "managed", "projectId": "kA1Hysr5ZeimtATHTDJz5B", "renderModuleFilePath": "wab/client/components/widgets/plasmic/PlasmicShareDialogContent.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/plasmic/ShareDialogContent.tsx"}, "cssFilePath": "wab/client/components/widgets/plasmic/PlasmicShareDialogContent.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "GFrmKeyhlA", "name": "PermissionItem", "type": "managed", "projectId": "kA1Hysr5ZeimtATHTDJz5B", "renderModuleFilePath": "wab/client/components/widgets/plasmic/PlasmicPermissionItem.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/plasmic/PermissionItem.tsx"}, "cssFilePath": "wab/client/components/widgets/plasmic/PlasmicPermissionItem.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [], "images": [], "projectApiToken": "TgLpuEUl7uuspAFHt7QV159zQ7saKV9YFRKbIN3zbxpr9eX2FVySEByEEj3RrdDGUc9BYaABzcNO9mBRJQgsKA", "codeComponents": [{"id": "0XP85qKwZB", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "Ip4LDxm-Qj", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "indirect": false, "globalContextsFilePath": "", "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "aaggSgVS8yYsAwQffVQB4p", "projectName": "[PlasmicKit] User Settings", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/PP__plasmickit_settings.module.css", "components": [{"id": "XkSd43CUYOB", "name": "SettingsContainer", "type": "managed", "projectId": "aaggSgVS8yYsAwQffVQB4p", "renderModuleFilePath": "wab/client/components/pages/plasmic/PlasmicSettingsContainer.tsx", "importSpec": {"modulePath": "wab/client/components/pages/plasmic/SettingsContainer.tsx"}, "cssFilePath": "wab/client/components/pages/plasmic/PlasmicSettingsContainer.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "F4ZVtfq6Xg", "name": "PersonalAccessToken", "type": "managed", "projectId": "aaggSgVS8yYsAwQffVQB4p", "renderModuleFilePath": "wab/client/components/pages/plasmic/PlasmicPersonalAccessToken.tsx", "importSpec": {"modulePath": "wab/client/components/pages/plasmic/PersonalAccessToken.tsx"}, "cssFilePath": "wab/client/components/pages/plasmic/PlasmicPersonalAccessToken.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "0O5nMBdoCe", "name": "TrustedHost", "type": "managed", "projectId": "aaggSgVS8yYsAwQffVQB4p", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_user_settings/PlasmicTrustedHost.tsx", "importSpec": {"modulePath": "wab/client/components/TrustedHost.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_user_settings/PlasmicTrustedHost.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "61Ev5d6FaD", "name": "ChangePasswordModal", "type": "managed", "projectId": "aaggSgVS8yYsAwQffVQB4p", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_user_settings/PlasmicChangePasswordModal.tsx", "importSpec": {"modulePath": "wab/client/components/ChangePasswordModal.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_user_settings/PlasmicChangePasswordModal.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "m5hJqED4tX", "name": "PasswordStrengthBar", "type": "managed", "projectId": "aaggSgVS8yYsAwQffVQB4p", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_user_settings/PlasmicPasswordStrengthBar.tsx", "importSpec": {"modulePath": "wab/client/components/PasswordStrengthBar.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_user_settings/PlasmicPasswordStrengthBar.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [], "images": [], "projectApiToken": "PxqDt6Wyv2DUTQ8Yu516tc3d8YZFNCrKKT9usvGhQKktAViIT319BjP7sLGdK4rwvPFHSr8ZtmIJCLuhLWvQ", "codeComponents": [{"id": "0_3flIH43R", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "bYCKIY6lRO", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "indirect": false, "globalContextsFilePath": "", "splitsProviderFilePath": "", "styleTokensProviderFilePath": "", "projectModuleFilePath": "", "customFunctions": []}, {"projectId": "29njzcsBEPR4koRddw4knF", "projectName": "[PlasmicKit] <PERSON><PERSON>", "projectBranchName": "feat/hide-auto-open", "version": "latest", "cssFilePath": "wab/client/plasmic/PP__plasmickit_alert_banner.module.css", "components": [{"id": "DCWq1LLaJ6e", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "managed", "projectId": "29njzcsBEPR4koRddw4knF", "renderModuleFilePath": "wab/client/components/widgets/plasmic/PlasmicAlertBanner.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/plasmic/AlertBanner.tsx"}, "cssFilePath": "wab/client/components/widgets/plasmic/PlasmicAlertBanner.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "V-X3eZLINq", "name": "PromoBanner", "type": "managed", "projectId": "29njzcsBEPR4koRddw4knF", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_alert_banner/PlasmicPromoBanner.tsx", "importSpec": {"modulePath": "wab/client/components/PromoBanner.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_alert_banner/PlasmicPromoBanner.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "LlDTs6h34ISG", "name": "Banner", "type": "managed", "projectId": "29njzcsBEPR4koRddw4knF", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_alert_banner/PlasmicBanner.tsx", "importSpec": {"modulePath": "wab/client/components/Banner.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_alert_banner/PlasmicBanner.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "ETj0D1AzSHQn", "name": "AutoOpenBanner", "type": "managed", "projectId": "29njzcsBEPR4koRddw4knF", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_alert_banner/PlasmicAutoOpenBanner.tsx", "importSpec": {"modulePath": "wab/client/components/AutoOpenBanner.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_alert_banner/PlasmicAutoOpenBanner.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [], "images": [], "projectApiToken": "tCKZNKmXrJdgAXgl2hMXTCgo95HgoZiCqOzWuPNTbRGZUQ376Cc2rj78tcaaVY6Bu2M14PCiQSxj3xRzndjw", "codeComponents": [{"id": "NemqEdp92g", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "XOAnIYZKqe", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "indirect": false, "globalContextsFilePath": "", "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "tXkSR39sgCDWSitZxC5xFV", "projectName": "[PlasmicKit] Design System", "version": ">=42.0.0", "cssFilePath": "wab/client/plasmic/PP__plasmickit_design_system.module.css", "components": [{"id": "pA22NEzDCsn_", "name": "Textbox", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicTextbox.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/Textbox.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit/PlasmicTextbox.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "po7gr0PX4_gWo", "name": "Searchbox", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicSearchbox.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/Searchbox.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit/PlasmicSearchbox.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "SEF-sRmSoqV5c", "name": "<PERSON><PERSON>", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/PlasmicButton.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/Button.tsx"}, "cssFilePath": "wab/client/plasmic/PlasmicButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "LPry-TF4j22a", "name": "IconButton", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/PlasmicIconButton.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/IconButton.tsx"}, "cssFilePath": "wab/client/plasmic/PlasmicIconButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "0NaTcyuAGK2dN", "name": "PanelDivider", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicPanelDivider.tsx", "importSpec": {"modulePath": "wab/client/components/PanelDivider.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit/PlasmicPanelDivider.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "v31d9_ANqk", "name": "ListItem", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicListItem.tsx", "importSpec": {"modulePath": "wab/client/components/ListItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicListItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "jW885tExwE", "name": "Chip", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicChip.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/Chip.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicChip.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "wNvxk7eOak", "name": "ListSectionHeader", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicListSectionHeader.tsx", "importSpec": {"modulePath": "wab/client/components/ListSectionHeader.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicListSectionHeader.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "uG5_fPM0sK", "name": "ListSectionSeparator", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicListSectionSeparator.tsx", "importSpec": {"modulePath": "wab/client/components/ListSectionSeparator.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicListSectionSeparator.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "znioE83CPU", "name": "ListSection", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicListSection.tsx", "importSpec": {"modulePath": "wab/client/components/ListSection.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicListSection.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "pFgBG9DS0D", "name": "DropdownOverlay", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicDropdownOverlay.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/DropdownOverlay.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicDropdownOverlay.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "KRNHR6lpj1", "name": "Indicator", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIndicator.tsx", "importSpec": {"modulePath": "wab/client/components/style-controls/Indicator.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIndicator.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "h69wHrrKtL", "name": "MenuButton", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicMenuButton.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/MenuButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicMenuButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "JJhv0MV9DH", "name": "ExpandButton", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicExpandButton.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/ExpandButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicExpandButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "VNi6NC2QOI", "name": "ActionMenuButton", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicActionMenuButton.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/ActionMenuButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicActionMenuButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "btpz7A3thO", "name": "InlineEditable", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicInlineEditable.tsx", "importSpec": {"modulePath": "wab/client/components/InlineEditable.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicInlineEditable.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "rD0wOVzSnE", "name": "Modal", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicModal.tsx", "importSpec": {"modulePath": "wab/client/components/Modal.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicModal.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "-EsDm7v023", "name": "TextWithInfo", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicTextWithInfo.tsx", "importSpec": {"modulePath": "wab/client/components/TextWithInfo.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicTextWithInfo.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "j_4IQyOWK2b", "name": "Select", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicSelect.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/Select.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicSelect.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "select"}, {"id": "rr-LWdMni2G", "name": "Select__Option", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicSelect__Option.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/Select__Option.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicSelect__Option.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "select-option"}, {"id": "_qMm1mtrqOi", "name": "Select__OptionGroup", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicSelect__OptionGroup.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/Select__OptionGroup.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicSelect__OptionGroup.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "select-option-group"}, {"id": "j2qDLcsq5qB", "name": "Select__Overlay", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicSelect__Overlay.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/Select__Overlay.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicSelect__Overlay.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "triggered-overlay"}, {"id": "W-rO7NZqPjZ", "name": "Checkbox", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicCheckbox.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/Checkbox.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicCheckbox.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "checkbox"}, {"id": "b35JDgXpbiF", "name": "Switch", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicSwitch.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/Switch.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicSwitch.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "switch"}, {"id": "p3GgKAlaQe", "name": "FreeTrial", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicFreeTrial.tsx", "importSpec": {"modulePath": "wab/client/components/FreeTrial.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicFreeTrial.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "j8LiUtkTBvsH", "name": "Textarea", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/PlasmicTextarea.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/Textarea.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/PlasmicTextarea.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "Hxtf0EKrkmO5", "name": "Label", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/PlasmicLabel.tsx", "importSpec": {"modulePath": "wab/client/components/plexus/Label.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/PlasmicLabel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "J_e2eE41048e", "name": "TextInput", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/PlasmicTextInput.tsx", "importSpec": {"modulePath": "wab/client/components/plexus/TextInput.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/PlasmicTextInput.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "0wwbx9l7LS5I", "name": "TextAreaInput", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/PlasmicTextAreaInput.tsx", "importSpec": {"modulePath": "wab/client/components/plexus/TextAreaInput.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/PlasmicTextAreaInput.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "hGC02-wRlm3F", "name": "Description", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/PlasmicDescription.tsx", "importSpec": {"modulePath": "wab/client/components/plexus/Description.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/PlasmicDescription.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "tKtZ3ZcVITrx", "name": "TextField", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/PlasmicTextField.tsx", "importSpec": {"modulePath": "wab/client/components/plexus/TextField.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/PlasmicTextField.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "en2IIw2C3_aI", "name": "Dialog", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/PlasmicDialog.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/Dialog.tsx", "exportName": "Dialog"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/PlasmicDialog.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "sbyrU_8SkoWY", "name": "PlexusButton", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/PlasmicPlexusButton.tsx", "importSpec": {"modulePath": "wab/client/components/plexus/PlexusButton.tsx", "exportName": "PlexusButton"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/PlasmicPlexusButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "FRA2fXjcDHo2", "name": "OverlayArrow", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/PlasmicOverlayArrow.tsx", "importSpec": {"modulePath": "wab/client/components/plexus/OverlayArrow.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/PlasmicOverlayArrow.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "FYmdApEkhOiH", "name": "Popover", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/PlasmicPopover.tsx", "importSpec": {"modulePath": "wab/client/components/plexus/Popover.tsx", "exportName": "Popover"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/PlasmicPopover.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "EBbuUzYSewBk", "name": "<PERSON><PERSON><PERSON>", "type": "managed", "projectId": "tXkSR39sgCDWSitZxC5xFV", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicTooltip.tsx", "importSpec": {"modulePath": "wab/client/components/plexus/Tooltip.tsx", "exportName": "<PERSON><PERSON><PERSON>"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicTooltip.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [{"id": "jbni8I3DCtf0N", "name": "OverflowHiddenIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__OverflowHidden.tsx"}, {"id": "oeLyzj1YRe8BQ", "name": "OverflowVisibleIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__OverflowVisible.tsx"}, {"id": "FMSGLwXiQt0qP", "name": "ComponentBaseIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ComponentBase.tsx"}, {"id": "xZ_09gUP02-Rw", "name": "RepeatHIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RepeatH.tsx"}, {"id": "Mj41Bvw-wzzqz", "name": "RepeatGridIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RepeatGrid.tsx"}, {"id": "HSR-IgaPwL0By", "name": "GridViewIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__GridView.tsx"}, {"id": "_iB8nICqsw35f", "name": "ListViewIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ListView.tsx"}, {"id": "uYCd2ha_MOVjz", "name": "CreateIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Create.tsx"}, {"id": "ckLkdbD6DMjvM", "name": "CheckTrueIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__CheckTrue.tsx"}, {"id": "-RXQcn1QrTqlQ", "name": "BoltPlusIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__BoltPlus.tsx"}, {"id": "U5J4vI5vbfdiF", "name": "BoltIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Bolt.tsx"}, {"id": "SqX3rxCv5y3Sh", "name": "EyeDropperIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__EyeDropper.tsx"}, {"id": "fwZgbP_d3EbnX", "name": "GlobeIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Globe.tsx"}, {"id": "gl-TRXE1wBtLP", "name": "PencilIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Pencil.tsx"}, {"id": "j4Qia2PXY8DFD", "name": "RadialIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Radial.tsx"}, {"id": "7iTyhFRwRc0Au", "name": "LinearIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Linear.tsx"}, {"id": "yzAFS1IyVvChx", "name": "AfterIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__After.tsx"}, {"id": "urb9dXzY1pVbS", "name": "AppendIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Append.tsx"}, {"id": "zmUmekYx786YY", "name": "PrependIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Prepend.tsx"}, {"id": "VU26fT14NQttw", "name": "BeforeIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Before.tsx"}, {"id": "Oe-Wko1vnXWN3", "name": "StartIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Start.tsx"}, {"id": "StcV9ltPAVvGO", "name": "EndIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__End.tsx"}, {"id": "Dj3u-HuPv94sN", "name": "ResetIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Reset.tsx"}, {"id": "qZooO-uhGAAb7", "name": "CenterAndPadIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__CenterAndPad.tsx"}, {"id": "-5COu_LiJXz2T", "name": "FrameStretchIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__FrameStretch.tsx"}, {"id": "BfriRIpE4x9fd", "name": "AutoIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Auto.tsx"}, {"id": "7D0GDLdF72udM", "name": "OpenIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Open.tsx"}, {"id": "pawp1H5YxB_3B", "name": "CheckIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Check.tsx"}, {"id": "Qsx4ABGKcDCPl", "name": "RecordingIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Recording.tsx"}, {"id": "GUEh_NVEW0RlX", "name": "StopIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Stop.tsx"}, {"id": "zHk6VWnSPnoBa", "name": "ForwardIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Forward.tsx"}, {"id": "dlwBQ6mTGkOP1", "name": "AddVariantGroupIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__AddVariantGroup.tsx"}, {"id": "pyS6pK4Spx-QF", "name": "VariantGroupIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__VariantGroup.tsx"}, {"id": "j39GoLwZnf7-v", "name": "PlaySvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__PlaySvg.tsx"}, {"id": "BGTwDE0-XM3EQ", "name": "AreaInputIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__AreaInput.tsx"}, {"id": "X561BmD81oKJV", "name": "PassInputIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__PassInput.tsx"}, {"id": "-6sK5SYNMS7X2", "name": "ItalicsTrueIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ItalicsTrue.tsx"}, {"id": "UNdGB6cne8QbW", "name": "ItalicsFalseIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ItalicsFalse.tsx"}, {"id": "D2lB664gxcnDA", "name": "FontIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Font.tsx"}, {"id": "KW4lXUe1GdXHi", "name": "PasteStyleIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__PasteStyle.tsx"}, {"id": "iW2NxsDugiMOV", "name": "CopyStyleIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__CopyStyle.tsx"}, {"id": "X20jrWqZWw7tC", "name": "BlockWithRepeatingIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__BlockWithRepeating.tsx"}, {"id": "GjyIM3ohZdSyY", "name": "BorderDottedIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__BorderDotted.tsx"}, {"id": "FvziGTYcmgsG2", "name": "BorderDashedIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__BorderDashed.tsx"}, {"id": "_OZUlMeBlzhyK", "name": "BorderSolidIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__BorderSolid.tsx"}, {"id": "ZtwQf3HBEdvvN", "name": "TriangleLeftIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__TriangleLeft.tsx"}, {"id": "A8NQUZ7Lg1OHO", "name": "TriangleBottomIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__TriangleBottom.tsx"}, {"id": "uNNciAETDn2TD", "name": "TriangleRightIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__TriangleRight.tsx"}, {"id": "lVRstDPlZX8da", "name": "TriangleTopIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__TriangleTop.tsx"}, {"id": "46CdDsebCI_II", "name": "ReadOnlyIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ReadOnly.tsx"}, {"id": "zfqoj_9EYklm4", "name": "ElementIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Element.tsx"}, {"id": "t3ELHGEW7Mere", "name": "CodeSandboxIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__CodeSandbox.tsx"}, {"id": "vTVU9zQnDPZGl", "name": "WandIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Wand.tsx"}, {"id": "TMsgIjXFPR-ct", "name": "MarkerIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Marker.tsx"}, {"id": "S1b6HroavEOOP", "name": "Recording2Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Recording2.tsx"}, {"id": "0M4uNL7Alfepo", "name": "WordSpacingIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__WordSpacing.tsx"}, {"id": "PytXc2ZCvxmc5", "name": "LetterSpacingIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__LetterSpacing.tsx"}, {"id": "ZmVZmXEc9f_SR", "name": "GearIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Gear.tsx"}, {"id": "8G7yEB3Bs8mxb", "name": "UnsetIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Unset.tsx"}, {"id": "XsCxDv59LcX2V", "name": "LigatureIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Ligature.tsx"}, {"id": "l_MId-_NGj1c4", "name": "EjectIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Eject.tsx"}, {"id": "Io97tJ4tV2frR", "name": "HandIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Hand.tsx"}, {"id": "sVrc1aLRLGQtV", "name": "CodeIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Code.tsx"}, {"id": "sABZLvi9xLkb_", "name": "DotBulletIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__DotBullet.tsx"}, {"id": "OneM44CVos89-", "name": "ShrinkIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Shrink.tsx"}, {"id": "E0URzkzhq00CJ", "name": "WrapIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Wrap.tsx"}, {"id": "M6PUWRfUuNMHE", "name": "GrowIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Grow.tsx"}, {"id": "7U-VDwrz5IyBP", "name": "StretchIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Stretch.tsx"}, {"id": "bkwoEfyBiF2GI", "name": "ExpandIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Expand.tsx"}, {"id": "AIyVpso_hzgbO", "name": "FullIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Full.tsx"}, {"id": "Vg40D0tRpyQa4", "name": "ColumnAlignBaselineIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ColumnAlignBaseline.tsx"}, {"id": "fA6-JUxqgPY22", "name": "ColumnAlignStretchIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ColumnAlignStretch.tsx"}, {"id": "-qElTwE7kGD_Y", "name": "ColumnAlignEndIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ColumnAlignEnd.tsx"}, {"id": "U-_6nWRHWz_cq", "name": "ColumnAlignCenterIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ColumnAlignCenter.tsx"}, {"id": "hoMix-s4LTLZL", "name": "ColumnJustifySpaceAroundIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ColumnJustifySpaceAround.tsx"}, {"id": "_pOp01rT3NCLV", "name": "ColumnJustifySpaceBetweenIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ColumnJustifySpaceBetween.tsx"}, {"id": "47EBzJiQFAVkq", "name": "ColumnJustifyEndIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ColumnJustifyEnd.tsx"}, {"id": "VhWw_U7e9rYcV", "name": "ColumnJustifyCenterIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ColumnJustifyCenter.tsx"}, {"id": "tlu9O4RgFHpKi", "name": "ColumnJustifyStartIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ColumnJustifyStart.tsx"}, {"id": "VgEsQn0-asNl5", "name": "ColumnAlignStartIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ColumnAlignStart.tsx"}, {"id": "7_wCJkgflCOfG", "name": "ColumnWrapReverseSpaceEvenlyIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ColumnWrapReverseSpaceEvenly.tsx"}, {"id": "oU8pNg9UIeMet", "name": "ColumnWrapSpaceEvenlyIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ColumnWrapSpaceEvenly.tsx"}, {"id": "CSLuT1n4q6ZRU", "name": "ColumnWrapReverseSpaceBetweenIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ColumnWrapReverseSpaceBetween.tsx"}, {"id": "2dSSmXx9A6AsN", "name": "ColumnWrapSpaceBetweenIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ColumnWrapSpaceBetween.tsx"}, {"id": "tZ0t9pDiXHdRc", "name": "ColumnWrapReverseStretchIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ColumnWrapReverseStretch.tsx"}, {"id": "Ui2U5jmndE5vl", "name": "ColumnWrapStretchIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ColumnWrapStretch.tsx"}, {"id": "TIyqIS1_v4Fmp", "name": "ColumnWrapReverseEndIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ColumnWrapReverseEnd.tsx"}, {"id": "-QQvGQGRrtLoG", "name": "ColumnWrapEndIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ColumnWrapEnd.tsx"}, {"id": "E35gND8AOlY0p", "name": "ColumnWrapReverseCenterIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ColumnWrapReverseCenter.tsx"}, {"id": "xR5Tl2GXPIokI", "name": "ColumnWrapCenterIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ColumnWrapCenter.tsx"}, {"id": "0O1Mq0xBSTAXh", "name": "ColumnWrapReverseStartIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ColumnWrapReverseStart.tsx"}, {"id": "nT8fkodvsSCa9", "name": "ColumnWrapStartIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ColumnWrapStart.tsx"}, {"id": "284dfvIQuZsR5", "name": "LineHeightIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__LineHeight.tsx"}, {"id": "x3SpPQGcqUzGa", "name": "RowAlignBaselineIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RowAlignBaseline.tsx"}, {"id": "9jCK33Vavdoo5", "name": "RowAlignStretchIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RowAlignStretch.tsx"}, {"id": "_BycRtRoj4vks", "name": "RowAlignEndIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RowAlignEnd.tsx"}, {"id": "bLYxzIN1OJ8ek", "name": "RowAlignCenterIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RowAlignCenter.tsx"}, {"id": "_abfBmcEglw3S", "name": "RowJustifySpaceAroundIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RowJustifySpaceAround.tsx"}, {"id": "GV0UtitVfEbcN", "name": "RowJustifySpaceBetweenIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RowJustifySpaceBetween.tsx"}, {"id": "VwdHZYjmm7BUK", "name": "RowJustifyEndIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RowJustifyEnd.tsx"}, {"id": "W0Cr1X-DpK0DQ", "name": "RowJustifyCenterIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RowJustifyCenter.tsx"}, {"id": "A8fBaSHzF07Xd", "name": "RowJustifyStartIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RowJustifyStart.tsx"}, {"id": "3bXvUjZYs44jx", "name": "RowAlignStartIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RowAlignStart.tsx"}, {"id": "MXUn9x4jiCc1r", "name": "RowWrapReverseCenterIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RowWrapReverseCenter.tsx"}, {"id": "l6b_b-rdcL3hu", "name": "RowWrapCenterIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RowWrapCenter.tsx"}, {"id": "Lxye36mL2fs__", "name": "RowWrapReverseSpaceEvenlyIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RowWrapReverseSpaceEvenly.tsx"}, {"id": "8ncvMTGG9O0pB", "name": "RowWrapSpaceEvenlyIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RowWrapSpaceEvenly.tsx"}, {"id": "fiMzg3VMrfdxv", "name": "RowWrapReverseSpaceBetweenIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RowWrapReverseSpaceBetween.tsx"}, {"id": "3yMNYhs65VOnj", "name": "RowWrapSpaceBetweenIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RowWrapSpaceBetween.tsx"}, {"id": "IzHQUJhvZuNL8", "name": "RowWrapReverseStretchIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RowWrapReverseStretch.tsx"}, {"id": "ySruQOx7VkAxz", "name": "RowWrapStretchIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RowWrapStretch.tsx"}, {"id": "-ilQpmbonXgDO", "name": "RowWrapReverseEndIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RowWrapReverseEnd.tsx"}, {"id": "Tq9JbioKZUurF", "name": "RowWrapEndIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RowWrapEnd.tsx"}, {"id": "aodydyD_A27i_", "name": "RowWrapReverseStartIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RowWrapReverseStart.tsx"}, {"id": "44RmfMxIN3ue9", "name": "RowWrapStartIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RowWrapStart.tsx"}, {"id": "vrE0GHgUiSGkm", "name": "HStackBlockIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__HStackBlock.tsx"}, {"id": "DTUk351ydDQGv", "name": "VStackBlockIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__VStackBlock.tsx"}, {"id": "tc9rvSrU8chF3", "name": "GridIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Grid.tsx"}, {"id": "5bfQ64bu7lE1W", "name": "SlotIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Slot.tsx"}, {"id": "nzc8B7oWFfsYG", "name": "TextBlockIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__TextBlock.tsx"}, {"id": "Sta1tgTjp-AlG", "name": "ImageBlockIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ImageBlock.tsx"}, {"id": "52oeoNiO91g3E", "name": "HeadingIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Heading.tsx"}, {"id": "_bmaLjOFc6A0c", "name": "ButtonInputIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ButtonInput.tsx"}, {"id": "uXgUjievEdNRp", "name": "UpperIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Upper.tsx"}, {"id": "jzCnIICyZLjTa", "name": "CapitalizeIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Capitalize.tsx"}, {"id": "fkmPu5HMXPk3v", "name": "LowerIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Lower.tsx"}, {"id": "KS-Ed2skfgOT-", "name": "UnderIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Under.tsx"}, {"id": "HO4Y577KUrPTc", "name": "StrikeIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Strike.tsx"}, {"id": "C2Q81bKNkO2iA", "name": "CoverIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Cover.tsx"}, {"id": "p0UbEeOdrsSDh", "name": "ContainIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Contain.tsx"}, {"id": "t4dEQqvFBLYO0", "name": "UserSelectIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__UserSelect.tsx"}, {"id": "A2FnGYgDh4e3U", "name": "EyeIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Eye.tsx"}, {"id": "9_8br5VomSnvn", "name": "EyeClosedIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__EyeClosed.tsx"}, {"id": "nMkHNFwp_HRuJ", "name": "FrameIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Frame.tsx"}, {"id": "nNWEF4jI3s5DI", "name": "ComponentIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Component.tsx"}, {"id": "htrQJQCkMImYW", "name": "ComponentInstanceIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ComponentInstance.tsx"}, {"id": "XA82Nhrgqj98Z", "name": "MixinIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Mixin.tsx"}, {"id": "2dOodD9TzfBr7", "name": "TokenIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Token.tsx"}, {"id": "k8wDoDw3p7qCe", "name": "BorderAllIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__BorderAll.tsx"}, {"id": "r3R5cZmB7b_Ir", "name": "BorderRadiusAllIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__BorderRadiusAll.tsx"}, {"id": "iRzJpyWQiyukf", "name": "BorderRadiusSideIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__BorderRadiusSide.tsx"}, {"id": "9ef1D8sWsc7q9", "name": "BorderSideIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__BorderSide.tsx"}, {"id": "fHlx_TS-5A8q3", "name": "PositionCornerIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__PositionCorner.tsx"}, {"id": "saZBjZKOUZpKg", "name": "PositionSideIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__PositionSide.tsx"}, {"id": "749Om5paOxYG1", "name": "PositionCoverIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__PositionCover.tsx"}, {"id": "2Z2LBvZI2d92B", "name": "AlignLeftIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__AlignLeft.tsx"}, {"id": "SVobUoWymkH-3", "name": "AlignCenterIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__AlignCenter.tsx"}, {"id": "m0ULYiWZcVQIc", "name": "AlignRightIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__AlignRight.tsx"}, {"id": "TXPpMJmnfIo7p", "name": "JustifyIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Justify.tsx"}, {"id": "jzPNgxwRto4ES", "name": "ThemeIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Theme.tsx"}, {"id": "-k064DlQ8k8-L", "name": "PlusIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Plus.tsx"}, {"id": "dHjXz96374PLA", "name": "MinusIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Minus.tsx"}, {"id": "hy7vKrgdAZwW4", "name": "CloseIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Close.tsx"}, {"id": "sjONHoK61vpSz", "name": "SearchIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Search.tsx"}, {"id": "miOAezEgkL3Po", "name": "PlusCircleIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__PlusCircle.tsx"}, {"id": "gU-8UYs9RllyJ", "name": "CheckCircleIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__CheckCircle.tsx"}, {"id": "1SZe8CLQrM-D1", "name": "DotsVerticalIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__DotsVertical.tsx"}, {"id": "7bxap5bzcUODa", "name": "TrashIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Trash.tsx"}, {"id": "7menVCTQRZjRE", "name": "ClipIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Clip.tsx"}, {"id": "6WntF2TJV1ePx", "name": "CopyIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Copy.tsx"}, {"id": "-9-68IGPdLG-5", "name": "HelpIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Help.tsx"}, {"id": "BjAly3N4fWuWe", "name": "InfoIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Info.tsx"}, {"id": "Q-k3Cfjm7dAW-", "name": "ArrowTopIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ArrowTop.tsx"}, {"id": "etkVJMeZvaFjM", "name": "ArrowRightIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ArrowRight.tsx"}, {"id": "imFfDXWS8TtTH", "name": "ArrowBottomIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ArrowBottom.tsx"}, {"id": "fS_r8u6Un0zKx", "name": "ArrowLeftIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ArrowLeft.tsx"}, {"id": "1q1PeGaGnsM9w", "name": "ArrowTopLeftIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ArrowTopLeft.tsx"}, {"id": "fs4_EBy1h8PAh", "name": "ArrowTopRightIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ArrowTopRight.tsx"}, {"id": "Tf_BSie1FYpBf", "name": "ArrowBottomLeftIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ArrowBottomLeft.tsx"}, {"id": "YERD2AmQWOfTG", "name": "ArrowBottomRightIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ArrowBottomRight.tsx"}, {"id": "BIUTBoMetOJnG", "name": "RadioFalseIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__RadioFalse.tsx"}, {"id": "xekzrDkLu0xdb", "name": "CheckFalseIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__CheckFalse.tsx"}, {"id": "BQBWbw0fg66Lw", "name": "LinkIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Link.tsx"}, {"id": "L1GrIYxdm_MJL", "name": "LightBulbIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__LightBulb.tsx"}, {"id": "4KZjuPY_m0VTb", "name": "TreeIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Tree.tsx"}, {"id": "ZSWshs5q2keYb", "name": "ActivityLogIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ActivityLog.tsx"}, {"id": "0e-yZ0qeSHb12", "name": "ImageUploadsIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ImageUploads.tsx"}, {"id": "TBhqPtLhSazsc", "name": "FetchIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Fetch.tsx"}, {"id": "0f0UCj2s6zd46", "name": "SlackIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Slack.tsx"}, {"id": "qLI3YUXuhrHAJ", "name": "FigmaIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__Figma.tsx"}, {"id": "VGC_5x40piw71", "name": "HidePlaceholderIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__HidePlaceholder.tsx"}, {"id": "iM2eS4rdLfeLa", "name": "ShowPlaceholderIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__ShowPlaceholder.tsx"}, {"id": "6ZOswzsUR", "name": "HistoryIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__History.tsx"}, {"id": "DYyeu-AtoO", "name": "BlockIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__Block.tsx"}, {"id": "-URGgj7hu6", "name": "TextInputIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__TextInput.tsx"}, {"id": "RPe-osqEKr", "name": "BoxControlsIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__BoxControls.tsx"}, {"id": "tSLSUCy1RH", "name": "KeyboardIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__Keyboard.tsx"}, {"id": "mZMZr0AmTY", "name": "IconIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__Icon.tsx"}, {"id": "vcLrcTni3c", "name": "DragGripIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__DragGrip.tsx"}, {"id": "B_JznpDb3g", "name": "PresetsIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__Presets.tsx"}, {"id": "BXy5xeQP60", "name": "AddPresetIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__AddPreset.tsx"}, {"id": "Bd5oeRNusF", "name": "VerticalDashIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__VerticalDash.tsx"}, {"id": "FHpqecUMh", "name": "CombinationIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__Combination.tsx"}, {"id": "WBXMPcPdoM", "name": "VariantIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__Variant.tsx"}, {"id": "AdCG7SM2vW", "name": "ScreenIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__Screen.tsx"}, {"id": "wtrcX3czS", "name": "CursorIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__Cursor.tsx"}, {"id": "aRHl6_hzQA", "name": "AddComponentIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__AddComponent.tsx"}, {"id": "xrbfvNe7FJ", "name": "AddSlotIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__AddSlot.tsx"}, {"id": "xWjo2JPAc6", "name": "LockIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__Lock.tsx"}, {"id": "PSEm2Mv7Z6", "name": "PropsIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__Props.tsx"}, {"id": "nVw7Q_oKAm", "name": "WrapElementIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__WrapElement.tsx"}, {"id": "j004PFuz7X", "name": "DataIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__Data.tsx"}, {"id": "Bg-ZlWgLuQ", "name": "CollapseAllIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__CollapseAll.tsx"}, {"id": "zCExKvD0Do", "name": "ExpandAllIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__ExpandAll.tsx"}, {"id": "oBcoqKZ43R", "name": "CirclePlusOutlineIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__CirclePlusOutline.tsx"}, {"id": "_zRDtyAhvz", "name": "CircleCloseOutlineIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__CircleCloseOutline.tsx"}, {"id": "H0P4odXsD7", "name": "ZoomInIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__ZoomIn.tsx"}, {"id": "Fe_71xyMPV", "name": "ZoomOutIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__ZoomOut.tsx"}, {"id": "KlHojyEimj", "name": "DesktopIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__Desktop.tsx"}, {"id": "8ScKv_DDh4", "name": "TabletIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__Tablet.tsx"}, {"id": "OTMpxbCTJc", "name": "PhoneIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__Phone.tsx"}, {"id": "5jTJ9kgoG", "name": "RowJustifySpaceEvenlyIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__RowJustifySpaceEvenly.tsx"}, {"id": "BKMOrXp5j", "name": "ColumnJustifySpaceEvenlyIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__ColumnJustifySpaceEvenly.tsx"}, {"id": "GkkhQuMH0", "name": "DotsHorizontalIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__DotsHorizontal.tsx"}, {"id": "l_n_OBLJg", "name": "MarkFullColorIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__MarkFullColor.tsx"}, {"id": "a6KJVu0om", "name": "MarkIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__Mark.tsx"}, {"id": "Y1Zd2XyVx", "name": "GroupIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__Group.tsx"}, {"id": "NvgzIE-xj9", "name": "UnlockIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__Unlock.tsx"}, {"id": "Ifi8dn__W4", "name": "FeedbackIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__Feedback.tsx"}, {"id": "NMgEhhWUNF", "name": "CircleIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicIcon__Circle.tsx"}, {"id": "kgkyNFJK8", "name": "PaintBucketFillIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__PaintBucketFill.tsx"}, {"id": "p8KOsO82kk", "name": "PageIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__Page.tsx"}, {"id": "JuZ41tZRcH", "name": "AddPageIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__AddPage.tsx"}, {"id": "eF-vmSklpE", "name": "AddArtboardIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__AddArtboard.tsx"}, {"id": "X6uI49oa3M", "name": "DividerIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__Divider.tsx"}, {"id": "F428orf4fpe", "name": "MenuTreeTriangleNewIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__MenuTreeTriangleNew.tsx"}, {"id": "rZZFoPoxE", "name": "ResponsivenessIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__Responsiveness.tsx"}, {"id": "5kR2BNf5c2", "name": "CircleCloseIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__CircleClose.tsx"}, {"id": "bYJ_6pFTV", "name": "WebhooksIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__Webhooks.tsx"}, {"id": "PRUWjlcPtN", "name": "StopSignIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__StopSign.tsx"}, {"id": "dZQqL4otrm", "name": "ZoomFitIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__ZoomFit.tsx"}, {"id": "F0M2GWyw-k", "name": "FilterIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__Filter.tsx"}, {"id": "qWsF-hGQoA", "name": "PublishIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__Publish.tsx"}, {"id": "_w2_Oh0qPI", "name": "FlexWrapIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__FlexWrap.tsx"}, {"id": "snVL_jxwTy", "name": "ReverseIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__Reverse.tsx"}, {"id": "ArS5N_7TRj", "name": "MixedArenaIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__MixedArena.tsx"}, {"id": "_NWs-bLkUG", "name": "PaintbrushIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__Paintbrush.tsx"}, {"id": "hRo7v6cqW6", "name": "FolderIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__Folder.tsx"}, {"id": "bbJ7sGynPQ", "name": "PaletteIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__Palette.tsx"}, {"id": "VHE96lS4k", "name": "PlumeIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__Plume.tsx"}, {"id": "98jhOvxuuF", "name": "EyeNoneIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__EyeNone.tsx"}, {"id": "f2nJFLC75", "name": "PlumeMarkIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__PlumeMark.tsx"}, {"id": "YXOqHAan-", "name": "FlexWrapHorizontalIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__FlexWrapHorizontal.tsx"}, {"id": "1brZ2tW2-", "name": "FlexWrapVerticalIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__FlexWrapVertical.tsx"}, {"id": "osrVUu7bV", "name": "FlexReverseIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__FlexReverse.tsx"}, {"id": "8LnIIxwYW", "name": "FlexDirectionRowIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__FlexDirectionRow.tsx"}, {"id": "ChOzaRz3G", "name": "FlexDirectionColumnIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__FlexDirectionColumn.tsx"}, {"id": "xqXuukMkE", "name": "ColorStopIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__ColorStop.tsx"}, {"id": "KhPnIdPOS", "name": "BoldIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__Bold.tsx"}, {"id": "bdKQtrlMa", "name": "EdgeHandleUpwardIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__EdgeHandleUpward.tsx"}, {"id": "yrY_aO5Ol", "name": "EdgeHandleDownwardIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__EdgeHandleDownward.tsx"}, {"id": "9jlM27RA5", "name": "EdgeHandleRightwardIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__EdgeHandleRightward.tsx"}, {"id": "4Dff0T6Uj", "name": "EdgeHandleLeftwardIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__EdgeHandleLeftward.tsx"}, {"id": "cRhITljQuV", "name": "DoubleDotsVerticalSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__DoubleDotsVerticalSvg.tsx"}, {"id": "eayXbyej4Q", "name": "Icon4Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__Icon4.tsx"}, {"id": "GblhqXeZ9m", "name": "Icon5Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__Icon5.tsx"}, {"id": "zOPv4eezG5", "name": "Icon6Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__Icon6.tsx"}, {"id": "hPBDwf8f70", "name": "Icon7Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__Icon7.tsx"}, {"id": "ZPpW4b17Mv", "name": "Icon8Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system/icons/PlasmicIcon__Icon8.tsx"}, {"id": "y6Ei15gXp_-T", "name": "Circle2Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/icons/PlasmicIcon__Circle2.tsx"}, {"id": "C21gcG8B3Wlx", "name": "ChevronDownIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/icons/PlasmicIcon__ChevronDown.tsx"}, {"id": "292IBDA68F8r", "name": "TriangleFilledIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_design_system_deprecated/icons/PlasmicIcon__TriangleFilled.tsx"}], "images": [{"id": "CnSA9ESJHWP4n", "name": "Alert.svg", "filePath": "wab/client/plasmic/plasmic_kit_design_system/images/alertSvg.svg"}, {"id": "Zx-kyHRa6Q6PA", "name": "Eye.svg", "filePath": "wab/client/plasmic/plasmic_kit_design_system/images/eyeSvg.svg"}, {"id": "tU5tnPRdetL29", "name": "pin outline", "filePath": "wab/client/plasmic/plasmic_kit_design_system/images/pinOutline.svg"}, {"id": "AsQa0EBURwYLP", "name": "image", "filePath": "wab/client/plasmic/plasmic_kit_design_system/images/image.svg"}, {"id": "ZkRcayLl5AFVV", "name": "image 2", "filePath": "wab/client/plasmic/plasmic_kit_design_system/images/image2.svg"}, {"id": "yherfIxkolNXF", "name": "image 3", "filePath": "wab/client/plasmic/plasmic_kit_design_system/images/image3.svg"}, {"id": "9X6ZsC5ww5", "name": "image 4", "filePath": "wab/client/plasmic/plasmic_kit_design_system/images/image4.svg"}], "projectApiToken": "f9TfKXJe23qS3iCt3r5tO0XpXcUU95Z2Z1LiWlFs45SfMLL9DCegTFwELvsHnfCjeBBGoOpSkOYLPeciWMw", "codeComponents": [{"id": "dj8MXRZ2vN", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "MELqZW8b3u", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "indirect": false, "globalContextsFilePath": "", "customFunctions": [], "splitsProviderFilePath": "", "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "wT5BWZPEc2fYxyqbTLXMt2", "projectName": "[PlasmicKit] V<PERSON>ts", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/plasmic_kit_variants/plasmic_plasmic_kit_variants.module.css", "components": [{"id": "PDpx0GMKsd", "name": "VariantSection", "type": "managed", "projectId": "wT5BWZPEc2fYxyqbTLXMt2", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_variants/PlasmicVariantSection.tsx", "importSpec": {"modulePath": "wab/client/components/variants/VariantSection.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_variants/PlasmicVariantSection.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "ZAqVPmZmi-", "name": "VariantRow", "type": "managed", "projectId": "wT5BWZPEc2fYxyqbTLXMt2", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_variants/PlasmicVariantRow.tsx", "importSpec": {"modulePath": "wab/client/components/variants/VariantRow.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_variants/PlasmicVariantRow.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "FskUdXzKp5L", "name": "VariantComboRow", "type": "managed", "projectId": "wT5BWZPEc2fYxyqbTLXMt2", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_variants/PlasmicVariantComboRow.tsx", "importSpec": {"modulePath": "wab/client/components/variants/VariantComboRow.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_variants/PlasmicVariantComboRow.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "iPC_skyaMh", "name": "V<PERSON>t<PERSON>in<PERSON><PERSON><PERSON>", "type": "managed", "projectId": "wT5BWZPEc2fYxyqbTLXMt2", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_variants/PlasmicVariantPinButton.tsx", "importSpec": {"modulePath": "wab/client/components/variants/VariantPinButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_variants/PlasmicVariantPinButton.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [], "images": [], "projectApiToken": "Ix7ER4Xrxygv7Ka9K48knID6gEM6f2fw6QZ8ehzPpN3CcXeZ30W2ih4MIzGBHcRnLy8G4KsvFmAhQLTXmjqQ", "codeComponents": [{"id": "DMjtc-GfU4", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "i916S7G_mx", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "indirect": false, "globalContextsFilePath": "", "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "gYEVvAzCcLMHDVPvuYxkFh", "projectName": "[PlasmicKit] Style Controls", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/plasmic_kit_style_controls/plasmic_plasmic_kit_styles_pane.module.css", "components": [{"id": "s1ridHP4Z3T", "name": "DimTokenSelector", "type": "managed", "projectId": "gYEVvAzCcLMHDVPvuYxkFh", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicDimTokenSelector.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/DimTokenSelector.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicDimTokenSelector.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "bqUvK9cs5w", "name": "StyleToggleButton", "type": "managed", "projectId": "gYEVvAzCcLMHDVPvuYxkFh", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicStyleToggleButton.tsx", "importSpec": {"modulePath": "wab/client/components/style-controls/StyleToggleButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicStyleToggleButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "nTolMugov4", "name": "ColorButton", "type": "managed", "projectId": "gYEVvAzCcLMHDVPvuYxkFh", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicColorButton.tsx", "importSpec": {"modulePath": "wab/client/components/style-controls/ColorButton.tsx", "exportName": "ColorButton"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicColorButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "OcKjGNdq-h", "name": "StyleToggleButtonGroup", "type": "managed", "projectId": "gYEVvAzCcLMHDVPvuYxkFh", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicStyleToggleButtonGroup.tsx", "importSpec": {"modulePath": "wab/client/components/style-controls/StyleToggleButtonGroup.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicStyleToggleButtonGroup.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "ho6fjXelhV", "name": "SplitRow", "type": "managed", "projectId": "gYEVvAzCcLMHDVPvuYxkFh", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicSplitRow.tsx", "importSpec": {"modulePath": "wab/client/components/SplitRow.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicSplitRow.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "E0bKgamUEin", "name": "StyleSelect", "type": "managed", "projectId": "gYEVvAzCcLMHDVPvuYxkFh", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicStyleSelect.tsx", "importSpec": {"modulePath": "wab/client/components/style-controls/StyleSelect.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicStyleSelect.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "select"}, {"id": "fVzKJ6hzd6u", "name": "StyleSelect__Option", "type": "managed", "projectId": "gYEVvAzCcLMHDVPvuYxkFh", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicStyleSelect__Option.tsx", "importSpec": {"modulePath": "wab/client/components/style-controls/StyleSelect__Option.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicStyleSelect__Option.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "select-option"}, {"id": "pQfj4ZYSnAW", "name": "StyleSelect__OptionGroup", "type": "managed", "projectId": "gYEVvAzCcLMHDVPvuYxkFh", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicStyleSelect__OptionGroup.tsx", "importSpec": {"modulePath": "wab/client/components/style-controls/StyleSelect__OptionGroup.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicStyleSelect__OptionGroup.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "select-option-group"}, {"id": "4xhJ1XtuOem", "name": "StyleSelect__Overlay", "type": "managed", "projectId": "gYEVvAzCcLMHDVPvuYxkFh", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicStyleSelect__Overlay.tsx", "importSpec": {"modulePath": "wab/client/components/style-controls/StyleSelect__Overlay.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicStyleSelect__Overlay.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "triggered-overlay"}, {"id": "nZHA7E5OiTx", "name": "StyleCheckbox", "type": "managed", "projectId": "gYEVvAzCcLMHDVPvuYxkFh", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicStyleCheckbox.tsx", "importSpec": {"modulePath": "wab/client/components/style-controls/StyleCheckbox.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicStyleCheckbox.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "checkbox"}, {"id": "0hwZcM2HAXr", "name": "StyleSwitch", "type": "managed", "projectId": "gYEVvAzCcLMHDVPvuYxkFh", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicStyleSwitch.tsx", "importSpec": {"modulePath": "wab/client/components/style-controls/StyleSwitch.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicStyleSwitch.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "switch"}, {"id": "4AYfEug-RA", "name": "ColorSwatch", "type": "managed", "projectId": "gYEVvAzCcLMHDVPvuYxkFh", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicColorSwatch.tsx", "importSpec": {"modulePath": "wab/client/components/style-controls/ColorSwatch.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_style_controls/PlasmicColorSwatch.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "-L2zZ5Mvmr", "name": "ListItem", "type": "managed", "projectId": "gYEVvAzCcLMHDVPvuYxkFh", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicListItem.tsx", "importSpec": {"modulePath": "wab/client/components/ListItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_design_system/PlasmicListItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "1OCmfT86EB3", "name": "TextboxLike", "type": "managed", "projectId": "gYEVvAzCcLMHDVPvuYxkFh", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_new_design_system_former_style_controls/PlasmicTextboxLike.tsx", "importSpec": {"modulePath": "wab/client/components/TextboxLike.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_new_design_system_former_style_controls/PlasmicTextboxLike.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "lzLkhV0UJA", "name": "<PERSON><PERSON><PERSON>", "type": "managed", "projectId": "gYEVvAzCcLMHDVPvuYxkFh", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_new_design_system_former_style_controls/PlasmicTooltip.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/Tooltip.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_new_design_system_former_style_controls/PlasmicTooltip.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "lHRivspQeB", "name": "HiliteTabButton", "type": "managed", "projectId": "gYEVvAzCcLMHDVPvuYxkFh", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_new_design_system_former_style_controls/PlasmicHiliteTabButton.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/HiliteTabButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_new_design_system_former_style_controls/PlasmicHiliteTabButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "a0-WHzk-U8", "name": "HiliteTabs", "type": "managed", "projectId": "gYEVvAzCcLMHDVPvuYxkFh", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_new_design_system_former_style_controls/PlasmicHiliteTabs.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/HiliteTabs.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_new_design_system_former_style_controls/PlasmicHiliteTabs.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "-L2zZ5Mvmr", "name": "LabeledListItem", "type": "managed", "projectId": "gYEVvAzCcLMHDVPvuYxkFh", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_new_design_system_former_style_controls/PlasmicLabeledListItem.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/LabeledListItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_new_design_system_former_style_controls/PlasmicLabeledListItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "8AZoGEGjWc", "name": "SectionCollapseButton", "type": "managed", "projectId": "gYEVvAzCcLMHDVPvuYxkFh", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_new_design_system_former_style_controls/PlasmicSectionCollapseButton.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/SectionCollapseButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_new_design_system_former_style_controls/PlasmicSectionCollapseButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "OW_7AtJANr", "name": "CollapsableSection", "type": "managed", "projectId": "gYEVvAzCcLMHDVPvuYxkFh", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_new_design_system_former_style_controls/PlasmicCollapsableSection.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/CollapsableSection.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_new_design_system_former_style_controls/PlasmicCollapsableSection.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "gkx-PRZnjFPo", "name": "RowItem", "type": "managed", "projectId": "gYEVvAzCcLMHDVPvuYxkFh", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_new_design_system_former_style_controls/PlasmicRowItem.tsx", "importSpec": {"modulePath": "wab/client/components/RowItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_new_design_system_former_style_controls/PlasmicRowItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "fgHLE_9XtAei", "name": "RowGroup", "type": "managed", "projectId": "gYEVvAzCcLMHDVPvuYxkFh", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_new_design_system_former_style_controls/PlasmicRowGroup.tsx", "importSpec": {"modulePath": "wab/client/components/RowGroup.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_new_design_system_former_style_controls/PlasmicRowGroup.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [{"id": "xluxXtVhs", "name": "CheckFalseIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_style_controls/icons/PlasmicIcon__CheckFalse.tsx"}, {"id": "47qMNx3RV", "name": "WildcardIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_style_controls/icons/PlasmicIcon__Wildcard.tsx"}, {"id": "F-3cn-vrB", "name": "ArrowDownIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_style_controls/icons/PlasmicIcon__ArrowDown.tsx"}, {"id": "D4ucGF8xb", "name": "ArrowRightIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_style_controls/icons/PlasmicIcon__ArrowRight.tsx"}], "images": [{"id": "r7sZ_Vt9G", "name": "transparent-background", "filePath": "wab/client/plasmic/plasmic_kit_style_controls/images/transparentBackground.svg"}, {"id": "YQFHPdeAw", "name": "image", "filePath": "wab/client/plasmic/plasmic_kit_new_design_system_former_style_controls/images/image.png"}], "projectApiToken": "dU41owbFU4Fvh48F9zxIwO5l2cXJV5vi8IjU7LwJRPAeviRu43kkU9anUpc6IkuJh6sdxcOHH5TEn4v8i1jVA", "codeComponents": [{"id": "iAaAgk60FY", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "BQqN0XVgr5", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "indirect": false, "globalContextsFilePath": "wab/client/plasmic/plasmic_kit_new_design_system_former_style_controls/PlasmicGlobalContextsProvider.tsx", "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "dyzP6dbCdycwJpqiR2zkwe", "projectName": "[PlasmicKit] Docs Portal", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/plasmic_plasmic_kit_docs_portal.module.css", "components": [{"id": "6yrnCqYwJf", "name": "DocsPortalHeader", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsPortalHeader.tsx", "importSpec": {"modulePath": "wab/client/components/docs/DocsPortalHeader.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsPortalHeader.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "-491oma_4M", "name": "DocsPortal", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsPortal.tsx", "importSpec": {"modulePath": "wab/client/components/docs/DocsPortal.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsPortal.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "c-G65M7vor", "name": "ComponentsPanel", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicComponentsPanel.tsx", "importSpec": {"modulePath": "wab/client/components/docs/ComponentsPanel.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicComponentsPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "vY12pF45uf", "name": "ComponentListItem", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicComponentListItem.tsx", "importSpec": {"modulePath": "wab/client/components/docs/ComponentListItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicComponentListItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "dwF8TMwvPf", "name": "ComponentTogglesPanel", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicComponentTogglesPanel.tsx", "importSpec": {"modulePath": "wab/client/components/docs/ComponentTogglesPanel.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicComponentTogglesPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "buRRLzgjkH", "name": "ComponentPropsSubHeader", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicComponentPropsSubHeader.tsx", "importSpec": {"modulePath": "wab/client/components/docs/ComponentPropsSubHeader.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicComponentPropsSubHeader.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "95ed9ODv12", "name": "LabeledProp", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicLabeledProp.tsx", "importSpec": {"modulePath": "wab/client/components/docs/LabeledProp.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicLabeledProp.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "QTY-rRQEAY", "name": "VariantProp", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicVariantProp.tsx", "importSpec": {"modulePath": "wab/client/components/docs/VariantProp.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicVariantProp.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "jW-aUu5X3W", "name": "SlotProp", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicSlotProp.tsx", "importSpec": {"modulePath": "wab/client/components/docs/SlotProp.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicSlotProp.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "RiY7IxtDrH", "name": "LinkedProp", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicLinkedProp.tsx", "importSpec": {"modulePath": "wab/client/components/docs/LinkedProp.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicLinkedProp.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "TrtPQzZ8M2", "name": "ElementProp", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicElementProp.tsx", "importSpec": {"modulePath": "wab/client/components/docs/ElementProp.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicElementProp.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "lckuNAFyZg", "name": "MarkComponent", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicMarkComponent.tsx", "importSpec": {"modulePath": "wab/client/components/docs/MarkComponent.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicMarkComponent.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "g_uMeV_Uh6", "name": "DocsPortalTabs", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsPortalTabs.tsx", "importSpec": {"modulePath": "wab/client/components/docs/DocsPortalTabs.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsPortalTabs.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "MQIZtdluUpm", "name": "DocsComponentsPortal", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsComponentsPortal.tsx", "importSpec": {"modulePath": "wab/client/components/docs/DocsComponentsPortal.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsComponentsPortal.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "p94ACk9Ka-", "name": "ImagesPanel", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicImagesPanel.tsx", "importSpec": {"modulePath": "wab/client/components/docs/ImagesPanel.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicImagesPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "UROUkkTIR8X", "name": "DocsImagesPortal", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsImagesPortal.tsx", "importSpec": {"modulePath": "wab/client/components/docs/DocsImagesPortal.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsImagesPortal.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "tnA9SknzQ5", "name": "ImageListItem", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicImageListItem.tsx", "importSpec": {"modulePath": "wab/client/components/docs/ImageListItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicImageListItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "9qTu7qylBlP", "name": "IconTogglesPanel", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicIconTogglesPanel.tsx", "importSpec": {"modulePath": "wab/client/components/docs/IconTogglesPanel.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicIconTogglesPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "J18H-n0ADz", "name": "DocsPortalTab", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsPortalTab.tsx", "importSpec": {"modulePath": "wab/client/components/docs/DocsPortalTab.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsPortalTab.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "P6rYMyYSiZ", "name": "DocsCodeSnippet", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsCodeSnippet.tsx", "importSpec": {"modulePath": "wab/client/components/docs/DocsCodeSnippet.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsCodeSnippet.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "13UGPPY1WI6", "name": "DocsPortalIntro", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsPortalIntro.tsx", "importSpec": {"modulePath": "wab/client/components/docs/DocsPortalIntro.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsPortalIntro.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "twJQ9idqHQ", "name": "DocsCollapsibleExplanation", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsCollapsibleExplanation.tsx", "importSpec": {"modulePath": "wab/client/components/docs/DocsCollapsibleExplanation.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsCollapsibleExplanation.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "7KLBF1De9n", "name": "SyncProjectSnippet", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicSyncProjectSnippet.tsx", "importSpec": {"modulePath": "wab/client/components/docs/SyncProjectSnippet.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicSyncProjectSnippet.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "-rnSZERM6Kf", "name": "DocsPortalBranches", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsPortalBranches.tsx", "importSpec": {"modulePath": "wab/client/components/docs/DocsPortalBranches.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsPortalBranches.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "mJFZOBWGNu", "name": "DocsPortalBranch", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsPortalBranch.tsx", "importSpec": {"modulePath": "wab/client/components/docs/DocsPortalBranch.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsPortalBranch.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "X5avWz1hNF", "name": "CodePreviewSnippet", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicCodePreviewSnippet.tsx", "importSpec": {"modulePath": "wab/client/components/docs/CodePreviewSnippet.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicCodePreviewSnippet.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "9vACp1cwGL", "name": "DocsPropsTable", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsPropsTable.tsx", "importSpec": {"modulePath": "wab/client/components/docs/DocsPropsTable.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsPropsTable.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "MQ5YoyUM0K", "name": "DocsPropsTableRow", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsPropsTableRow.tsx", "importSpec": {"modulePath": "wab/client/components/docs/DocsPropsTableRow.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicDocsPropsTableRow.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "fiIuU8gs9A", "name": "ComponentView", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicComponentView.tsx", "importSpec": {"modulePath": "wab/client/components/docs/ComponentView.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicComponentView.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "qFYdDOtl6B", "name": "TemplateRow", "type": "managed", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicTemplateRow.tsx", "importSpec": {"modulePath": "wab/client/components/docs/TemplateRow.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicTemplateRow.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [{"id": "zDswZrFKQ1z", "name": "ChevronLeftIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/icons/PlasmicIcon__ChevronLeft.tsx"}, {"id": "c8L1Wu5s-LH", "name": "ChevronBottomIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/icons/PlasmicIcon__ChevronBottom.tsx"}], "images": [], "projectApiToken": "M4y6bLbirTvvr7127BRNqwl63h7sUXuoMdnByMlLAh2Cx5AQDtHqLkY383DNTXg4Ptkj8xCOY8YamPdXOkA", "codeComponents": [], "indirect": false, "globalContextsFilePath": "", "splitsProviderFilePath": "", "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "oYWs1jXLUht24zyQBdCd5F", "projectName": "[PlasmicKit] Init token", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/PP__plasmickit_init_token.module.css", "components": [{"id": "dWRKivg8dUht", "name": "InitTokenContainer", "type": "managed", "projectId": "oYWs1jXLUht24zyQBdCd5F", "renderModuleFilePath": "wab/client/components/pages/plasmic/PlasmicInitTokenContainer.tsx", "importSpec": {"modulePath": "wab/client/components/pages/InitTokenContainer.tsx"}, "cssFilePath": "wab/client/components/pages/plasmic/PlasmicInitTokenContainer.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [], "images": [], "codeComponents": [], "projectApiToken": "4FaVpmyWwxktNlVFKz8Yuo9113f7yefdZywq9p4aYqPECZ2Yuvmiw7Qxm3jWzw5jgB36rJ1NeivkZvL0slQ", "indirect": false, "globalContextsFilePath": "", "splitsProviderFilePath": "", "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "fpbcKyXdMTvY59T4C5fjcC", "projectName": "[PlasmicKit] Continuous Deployment", "version": ">=0.0.0", "cssFilePath": "wab/client/components/modals/plasmic/plasmic_kit_project_settings/plasmic_plasmic_kit_project_settings.module.css", "components": [{"id": "FuvSZfvXL5", "name": "GithubIntegration", "type": "managed", "projectId": "fpbcKyXdMTvY59T4C5fjcC", "renderModuleFilePath": "wab/client/components/github/plasmic/plasmic_kit_continuous_deployment/PlasmicGithubIntegration.tsx", "importSpec": {"modulePath": "wab/client/components/github/GithubIntegration.tsx"}, "cssFilePath": "wab/client/components/github/plasmic/plasmic_kit_continuous_deployment/PlasmicGithubIntegration.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "mSgnlB96I5A", "name": "WebhooksItem", "type": "managed", "projectId": "fpbcKyXdMTvY59T4C5fjcC", "renderModuleFilePath": "wab/client/components/webhooks/plasmic/plasmic_kit_continuous_deployment/PlasmicWebhooksItem.tsx", "importSpec": {"modulePath": "wab/client/components/webhooks/WebhooksItem.tsx"}, "cssFilePath": "wab/client/components/webhooks/plasmic/plasmic_kit_continuous_deployment/PlasmicWebhooksItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "Ynwp30ZgYk", "name": "WebhooksHistory", "type": "managed", "projectId": "fpbcKyXdMTvY59T4C5fjcC", "renderModuleFilePath": "wab/client/components/webhooks/plasmic/plasmic_kit_continuous_deployment/PlasmicWebhooksHistory.tsx", "importSpec": {"modulePath": "wab/client/components/webhooks/WebhooksHistory.tsx"}, "cssFilePath": "wab/client/components/webhooks/plasmic/plasmic_kit_continuous_deployment/PlasmicWebhooksHistory.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "MtBpr4iNob", "name": "WebhookEvent", "type": "managed", "projectId": "fpbcKyXdMTvY59T4C5fjcC", "renderModuleFilePath": "wab/client/components/webhooks/plasmic/plasmic_kit_continuous_deployment/PlasmicWebhookEvent.tsx", "importSpec": {"modulePath": "wab/client/components/webhooks/WebhookEvent.tsx"}, "cssFilePath": "wab/client/components/webhooks/plasmic/plasmic_kit_continuous_deployment/PlasmicWebhookEvent.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "8dA5vGT9N9E", "name": "ConsoleOutput", "type": "managed", "projectId": "fpbcKyXdMTvY59T4C5fjcC", "renderModuleFilePath": "wab/client/components/github/plasmic/plasmic_kit_continuous_deployment/PlasmicConsoleOutput.tsx", "importSpec": {"modulePath": "wab/client/components/github/ConsoleOutput.tsx"}, "cssFilePath": "wab/client/components/github/plasmic/plasmic_kit_continuous_deployment/PlasmicConsoleOutput.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "aXXfRDkhD-", "name": "PublishFlowDialog", "type": "managed", "projectId": "fpbcKyXdMTvY59T4C5fjcC", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/PlasmicPublishFlowDialog.tsx", "importSpec": {"modulePath": "wab/client/components/TopFrame/TopBar/PublishFlowDialog.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/PlasmicPublishFlowDialog.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "74wUdEnJhwr", "name": "SubsectionSaveVersion", "type": "managed", "projectId": "fpbcKyXdMTvY59T4C5fjcC", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/PlasmicSubsectionSaveVersion.tsx", "importSpec": {"modulePath": "wab/client/components/TopFrame/TopBar/SubsectionSaveVersion.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/PlasmicSubsectionSaveVersion.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "0HHLsxeAqF8", "name": "SubsectionPushDeploy", "type": "managed", "projectId": "fpbcKyXdMTvY59T4C5fjcC", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/PlasmicSubsectionPushDeploy.tsx", "importSpec": {"modulePath": "wab/client/components/TopFrame/TopBar/SubsectionPushDeploy.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/PlasmicSubsectionPushDeploy.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "9EUA-QZFp69", "name": "SubsectionWebhooks", "type": "managed", "projectId": "fpbcKyXdMTvY59T4C5fjcC", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/PlasmicSubsectionWebhooks.tsx", "importSpec": {"modulePath": "wab/client/components/TopFrame/TopBar/SubsectionWebhooks.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/PlasmicSubsectionWebhooks.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "OkB-fXuJPc", "name": "WebhookHeader", "type": "managed", "projectId": "fpbcKyXdMTvY59T4C5fjcC", "renderModuleFilePath": "wab/client/components/webhooks/plasmic/plasmic_kit_continuous_deployment/PlasmicWebhookHeader.tsx", "importSpec": {"modulePath": "wab/client/components/webhooks/WebhookHeader.tsx"}, "cssFilePath": "wab/client/components/webhooks/plasmic/plasmic_kit_continuous_deployment/PlasmicWebhookHeader.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "JzpEJAQTjPX", "name": "GitJobStep", "type": "managed", "projectId": "fpbcKyXdMTvY59T4C5fjcC", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/PlasmicGitJobStep.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/GitJobStep.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/PlasmicGitJobStep.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "6ztKJ9-EG9Y", "name": "ErrorFeedback", "type": "managed", "projectId": "fpbcKyXdMTvY59T4C5fjcC", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/PlasmicErrorFeedback.tsx", "importSpec": {"modulePath": "wab/client/components/github/ErrorFeedback.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/PlasmicErrorFeedback.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "JhFt3V1Imn", "name": "PublishWizard", "type": "managed", "projectId": "fpbcKyXdMTvY59T4C5fjcC", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/PlasmicPublishWizard.tsx", "importSpec": {"modulePath": "wab/client/components/TopFrame/TopBar/PublishWizard.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/PlasmicPublishWizard.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "aeDQsBfp-eA", "name": "SubsectionPlasmicHosting", "type": "managed", "projectId": "fpbcKyXdMTvY59T4C5fjcC", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/PlasmicSubsectionPlasmicHosting.tsx", "importSpec": {"modulePath": "wab/client/components/TopFrame/TopBar/SubsectionPlasmicHosting.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/PlasmicSubsectionPlasmicHosting.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "aFapl-YUjv9", "name": "PlasmicHostingSettings", "type": "managed", "projectId": "fpbcKyXdMTvY59T4C5fjcC", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/PlasmicPlasmicHostingSettings.tsx", "importSpec": {"modulePath": "wab/client/components/TopFrame/TopBar/PlasmicHostingSettings.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/PlasmicPlasmicHostingSettings.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "eqF_n5a1-6b", "name": "DomainCard", "type": "managed", "projectId": "fpbcKyXdMTvY59T4C5fjcC", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/PlasmicDomainCard.tsx", "importSpec": {"modulePath": "wab/client/components/TopFrame/TopBar/DomainCard.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/PlasmicDomainCard.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "oo-lLDZ5qnA", "name": "Spinner", "type": "managed", "projectId": "fpbcKyXdMTvY59T4C5fjcC", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/PlasmicSpinner.tsx", "importSpec": {"modulePath": "wab/client/components/TopFrame/TopBar/Spinner.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/PlasmicSpinner.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [{"id": "vvFDLf-9Nl", "name": "IconIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/icons/PlasmicIcon__Icon.tsx"}, {"id": "ICm0kU5OI3", "name": "Icon4Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/icons/PlasmicIcon__Icon4.tsx"}, {"id": "mwpa_Gia6i", "name": "Spinner1S200PxSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/icons/PlasmicIcon__Spinner1S200PxSvg.tsx"}], "images": [{"id": "XpXXn3d3xS", "name": "image", "filePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/images/image.svg"}, {"id": "dAMIiI_twd", "name": "image 2", "filePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/images/image2.svg"}, {"id": "sq-oDPlYUT", "name": "image 3", "filePath": "wab/client/plasmic/plasmic_kit_continuous_deployment/images/image3.svg"}], "projectApiToken": "xMX2CrZ1MeebFfK5WouA7NV5ADTRqEeTDCHEKmVxHsOrnNC1sS5WTrdW2pPiPRlKiqU812GiBWvoob4MISw", "codeComponents": [{"id": "4aI0ECNr28", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "Wuez_LskCz", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "indirect": false, "globalContextsFilePath": "", "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "6CrqkTcB6gSAHoA8c8zpNz", "projectName": "[PlasmicKit] Top Bar", "version": "latest", "cssFilePath": "wab/client/plasmic/plasmic_kit_top_bar/plasmic_plasmic_kit_top_bar.module.css", "components": [{"id": "tNBvs5bIAy", "name": "TopBar", "type": "managed", "projectId": "6CrqkTcB6gSAHoA8c8zpNz", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicTopBar.tsx", "importSpec": {"modulePath": "wab/client/components/top-bar/TopBar.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicTopBar.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "UsJCR-Jtn5", "name": "ZoomButton", "type": "managed", "projectId": "6CrqkTcB6gSAHoA8c8zpNz", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicZoomButton.tsx", "importSpec": {"modulePath": "wab/client/components/top-bar/ZoomButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicZoomButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "FCNHcPh1ZR", "name": "CodeButton", "type": "managed", "projectId": "6CrqkTcB6gSAHoA8c8zpNz", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicCodeButton.tsx", "importSpec": {"modulePath": "wab/client/components/top-bar/CodeButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicCodeButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "mnPFthIw2I", "name": "ShareButton", "type": "managed", "projectId": "6CrqkTcB6gSAHoA8c8zpNz", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicShareButton.tsx", "importSpec": {"modulePath": "wab/client/components/top-bar/ShareButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicShareButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "-r2DBYss6", "name": "ViewButton", "type": "managed", "projectId": "6CrqkTcB6gSAHoA8c8zpNz", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicViewButton.tsx", "importSpec": {"modulePath": "wab/client/components/top-bar/ViewButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicViewButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "ND5ZuEZMUe", "name": "LivePopOutButton", "type": "managed", "projectId": "6CrqkTcB6gSAHoA8c8zpNz", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicLivePopOutButton.tsx", "importSpec": {"modulePath": "wab/client/components/top-bar/LivePopOutButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicLivePopOutButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "yXRcEjTceQ", "name": "PublishButton", "type": "managed", "projectId": "6CrqkTcB6gSAHoA8c8zpNz", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicPublishButton.tsx", "importSpec": {"modulePath": "wab/client/components/top-bar/PublishButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicPublishButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "I6gjdy639O", "name": "VariantsComboSelect", "type": "managed", "projectId": "6CrqkTcB6gSAHoA8c8zpNz", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicVariantsComboSelect.tsx", "importSpec": {"modulePath": "wab/client/components/top-bar/VariantsComboSelect.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicVariantsComboSelect.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "cwS3NAy41ya", "name": "VariantsMenu", "type": "managed", "projectId": "6CrqkTcB6gSAHoA8c8zpNz", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicVariantsMenu.tsx", "importSpec": {"modulePath": "wab/client/components/top-bar/VariantsMenu.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicVariantsMenu.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "MAl3yYpW3c", "name": "TitleEditable", "type": "managed", "projectId": "6CrqkTcB6gSAHoA8c8zpNz", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicTitleEditable.tsx", "importSpec": {"modulePath": "wab/client/components/top-bar/TitleEditable.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicTitleEditable.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "HYPZr2nWSgs", "name": "SaveIndicator", "type": "managed", "projectId": "6CrqkTcB6gSAHoA8c8zpNz", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicSaveIndicator.tsx", "importSpec": {"modulePath": "wab/client/components/top-bar/SaveIndicator.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicSaveIndicator.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "OAMl2pw5C9W", "name": "ArenaSwitcher", "type": "managed", "projectId": "6CrqkTcB6gSAHoA8c8zpNz", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicArenaSwitcher.tsx", "importSpec": {"modulePath": "wab/client/components/top-bar/ArenaSwitcher.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicArenaSwitcher.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "IQWpmX8J3t", "name": "BranchSwitcher", "type": "managed", "projectId": "6CrqkTcB6gSAHoA8c8zpNz", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicBranchSwitcher.tsx", "importSpec": {"modulePath": "wab/client/components/top-bar/BranchSwitcher.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicBranchSwitcher.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "MJoB9g7giNL", "name": "ViewAsButton", "type": "managed", "projectId": "6CrqkTcB6gSAHoA8c8zpNz", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicViewAsButton.tsx", "importSpec": {"modulePath": "wab/client/components/app-auth/ViewAsButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicViewAsButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "JnxWw8hitac", "name": "CommentButton", "type": "managed", "projectId": "6CrqkTcB6gSAHoA8c8zpNz", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicCommentButton.tsx", "importSpec": {"modulePath": "wab/client/components/CommentButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicCommentButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "BqSsxlQdj2F0", "name": "AiButton", "type": "managed", "projectId": "6CrqkTcB6gSAHoA8c8zpNz", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicAiButton.tsx", "importSpec": {"modulePath": "wab/client/components/AiButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_top_bar/PlasmicAiButton.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [{"id": "jnoWwmZ86t", "name": "IconIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_top_bar/icons/PlasmicIcon__Icon.tsx"}, {"id": "yzguJQZL37", "name": "SegmentSeparatorIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_top_bar/icons/PlasmicIcon__SegmentSeparator.tsx"}], "images": [{"id": "ay93RhuWbc", "name": "image", "filePath": "wab/client/plasmic/plasmic_kit_top_bar/images/image.svg"}], "projectApiToken": "h9rSzR0nBH4n7Ges0HC3BNleSfLePCGYV9P6UzN6C3meqQdMvC9Gpj5efjEx5nIvnmd8PC6FvaSENp080jcQ", "codeComponents": [{"id": "_ZM9CXUVLP", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "O56cPS-IMG", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "indirect": false, "globalContextsFilePath": "", "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "m8VxGcigeLAEXFe8c12w5Q", "projectName": "[PlasmicKit] ProjectPanel", "version": "latest", "cssFilePath": "wab/client/plasmic/project_panel/plasmic_project_panel.module.css", "components": [{"id": "iWeSjEMdI3", "name": "FolderItem", "type": "managed", "projectId": "m8VxGcigeLAEXFe8c12w5Q", "renderModuleFilePath": "wab/client/plasmic/project_panel/PlasmicFolderItem.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/ProjectPanel/FolderItem.tsx"}, "cssFilePath": "wab/client/plasmic/project_panel/PlasmicFolderItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "CHoUJxFMpo", "name": "SearchInput", "type": "managed", "projectId": "m8VxGcigeLAEXFe8c12w5Q", "renderModuleFilePath": "wab/client/plasmic/project_panel/PlasmicSearchInput.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/ProjectPanel/SearchInput.tsx"}, "cssFilePath": "wab/client/plasmic/project_panel/PlasmicSearchInput.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "BSjTPez6aCjk", "name": "OutlineTab", "type": "managed", "projectId": "m8VxGcigeLAEXFe8c12w5Q", "renderModuleFilePath": "wab/client/plasmic/project_panel/PlasmicOutlineTab.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/outline-tab.tsx", "exportName": "OutlineTab"}, "cssFilePath": "wab/client/plasmic/project_panel/PlasmicOutlineTab.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "Kyrn_lAAwr", "name": "NavigationDropdown", "type": "managed", "projectId": "m8VxGcigeLAEXFe8c12w5Q", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_project_panel/PlasmicNavigationDropdown.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/ProjectPanel/NavigationDropdown.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_project_panel/PlasmicNavigationDropdown.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [], "images": [], "codeComponents": [{"id": "eu3GIKsVNh", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "ukG7fFRJIr", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "projectApiToken": "fl06a7X18fSpoPofcNH7s11u3b2GLfyhHkM4NzgpId9pqN5ANzQZIWB2LRm0WC90o3nPOZtyENzWTeVonOg", "indirect": false, "globalContextsFilePath": "", "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "fQPf2UiMEMhB52C8QQXwWe", "projectName": "[PlasmicKit] Omnibar", "version": "latest", "cssFilePath": "wab/client/plasmic/plasmic_kit_omnibar/plasmic_plasmic_kit_omnibar.module.css", "components": [{"id": "KnUjAGcQKT", "name": "OmnibarAddItem", "type": "managed", "projectId": "fQPf2UiMEMhB52C8QQXwWe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_omnibar/PlasmicOmnibarAddItem.tsx", "importSpec": {"modulePath": "wab/client/components/omnibar/OmnibarAddItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_omnibar/PlasmicOmnibarAddItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "qx4iENdAfF", "name": "OmnibarGroup", "type": "managed", "projectId": "fQPf2UiMEMhB52C8QQXwWe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_omnibar/PlasmicOmnibarGroup.tsx", "importSpec": {"modulePath": "wab/client/components/omnibar/OmnibarGroup.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_omnibar/PlasmicOmnibarGroup.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "A2li_iO_iw", "name": "OmnibarCommandItem", "type": "managed", "projectId": "fQPf2UiMEMhB52C8QQXwWe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_omnibar/PlasmicOmnibarCommandItem.tsx", "importSpec": {"modulePath": "wab/client/components/omnibar/OmnibarCommandItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_omnibar/PlasmicOmnibarCommandItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "paIlCoZKcm", "name": "Omnibar", "type": "managed", "projectId": "fQPf2UiMEMhB52C8QQXwWe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_omnibar/PlasmicOmnibar.tsx", "importSpec": {"modulePath": "wab/client/components/omnibar/Omnibar.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_omnibar/PlasmicOmnibar.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "hrLkFMfsYv", "name": "OmnibarTabHeader", "type": "managed", "projectId": "fQPf2UiMEMhB52C8QQXwWe", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_omnibar/PlasmicOmnibarTabHeader.tsx", "importSpec": {"modulePath": "wab/client/components/omnibar/OmnibarTabHeader.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_omnibar/PlasmicOmnibarTabHeader.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [], "images": [], "projectApiToken": "1UWteYNm5YQAm3k1Bg1iSPDkZUU7QqZV2aAhmCpwQnY10Txx9Gkw3lo7CXsDX63QuZo5EDsnOAfqKUzVDOuQ", "codeComponents": [{"id": "6FV4bnzp21", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "vfzEC59DF8", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "indirect": false, "globalContextsFilePath": "", "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "kdj5vahTyUKxznuR6rrtt6", "projectName": "[PlasmicKit] VariantsBar", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/plasmic_kit_variants_bar/plasmic_plasmic_kit_variants_bar.module.css", "components": [{"id": "98t4Edcdrb", "name": "VariantsBar", "type": "managed", "projectId": "kdj5vahTyUKxznuR6rrtt6", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_variants_bar/PlasmicVariantsBar.tsx", "importSpec": {"modulePath": "wab/client/components/canvas/VariantsBar/VariantsBar.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_variants_bar/PlasmicVariantsBar.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "8q06bNJu0e", "name": "V<PERSON>tsDrawer", "type": "managed", "projectId": "kdj5vahTyUKxznuR6rrtt6", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_variants_bar/PlasmicVariantsDrawer.tsx", "importSpec": {"modulePath": "wab/client/components/canvas/VariantsBar/VariantsDrawer.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_variants_bar/PlasmicVariantsDrawer.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "GPePwGKSYX", "name": "VariantsSectionDivider", "type": "managed", "projectId": "kdj5vahTyUKxznuR6rrtt6", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_variants_bar/PlasmicVariantsSectionDivider.tsx", "importSpec": {"modulePath": "wab/client/components/canvas/VariantsBar/VariantsSectionDivider.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_variants_bar/PlasmicVariantsSectionDivider.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "73fZB1b9iN", "name": "VariantsGroupLabel", "type": "managed", "projectId": "kdj5vahTyUKxznuR6rrtt6", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_variants_bar/PlasmicVariantsGroupLabel.tsx", "importSpec": {"modulePath": "wab/client/components/canvas/VariantsBar/VariantsGroupLabel.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_variants_bar/PlasmicVariantsGroupLabel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "0Rv3wK0NN-", "name": "VariantRow", "type": "managed", "projectId": "kdj5vahTyUKxznuR6rrtt6", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_variants_bar/PlasmicVariantRow.tsx", "importSpec": {"modulePath": "wab/client/components/canvas/VariantsBar/VariantRow.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_variants_bar/PlasmicVariantRow.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "4OLKnpGnTY", "name": "VariantBadge", "type": "managed", "projectId": "kdj5vahTyUKxznuR6rrtt6", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_variants_bar/PlasmicVariantBadge.tsx", "importSpec": {"modulePath": "wab/client/components/canvas/VariantsBar/VariantBadge.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_variants_bar/PlasmicVariantBadge.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "j_Jdg2E_a5", "name": "ToggleRecordingButton", "type": "managed", "projectId": "kdj5vahTyUKxznuR6rrtt6", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_variants_bar/PlasmicToggleRecordingButton.tsx", "importSpec": {"modulePath": "wab/client/components/canvas/VariantsBar/ToggleRecordingButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_variants_bar/PlasmicToggleRecordingButton.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [], "images": [], "projectApiToken": "PZeH8nU3tn3nkXdeh04bUzh6HBrFTv5WeKs0xS79MkbvZ2tF3u23lywMcZKZDgJztRNyxQr5JDy4BFCC37Q", "codeComponents": [{"id": "s9WjzeRqst", "name": "PlasmicHead", "componentImportPath": "@plasmicapp/react-web"}, {"id": "euFC_nhTaO", "name": "<PERSON><PERSON><PERSON>", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "indirect": false, "globalContextsFilePath": "", "splitsProviderFilePath": "", "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "6BCq4vMow1yqGKFdcP68Rz", "projectName": "[Plasmic Kit] Page Settings", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/plasmic_kit_page_settings/plasmic_plasmic_kit_page_settings.module.css", "components": [{"id": "jTLog2H3DE", "name": "PageSettings", "type": "managed", "projectId": "6BCq4vMow1yqGKFdcP68Rz", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_page_settings/PlasmicPageSettings.tsx", "importSpec": {"modulePath": "wab/client/components/PageSettings.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_page_settings/PlasmicPageSettings.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "ntKkcfMNg2s", "name": "Switch", "type": "managed", "projectId": "6BCq4vMow1yqGKFdcP68Rz", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_page_settings/PlasmicSwitch.tsx", "importSpec": {"modulePath": "wab/client/components/Switch.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_page_settings/PlasmicSwitch.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "switch"}], "icons": [], "images": [], "projectApiToken": "e4YfupA609sspSY80GQQM5iWFktmTmTrPJoxWxBIbNZXwUZVXQdCt1rJl2AqGeyv96iktO5egbmdmX0CgrNpA", "codeComponents": [{"id": "h5vSs27HsV", "name": "PlasmicHead", "componentImportPath": "@plasmicapp/react-web"}, {"id": "VEviSgYTLL", "name": "<PERSON><PERSON><PERSON>", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "indirect": false, "globalContextsFilePath": "", "splitsProviderFilePath": "", "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "oermwjefjidrRRHcrxyCjQ", "projectName": "[PlasmicKit] New Component", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/plasmic_kit_new_component/plasmic_plasmic_kit_new_component.module.css", "components": [{"id": "ZDk8OKbbuW", "name": "NewComponentModal", "type": "managed", "projectId": "oermwjefjidrRRHcrxyCjQ", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_new_component/PlasmicNewComponentModal.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/NewComponentModal.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_new_component/PlasmicNewComponentModal.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "3_QVitiqMh", "name": "NewComponentSection", "type": "managed", "projectId": "oermwjefjidrRRHcrxyCjQ", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_new_component/PlasmicNewComponentSection.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/NewComponentSection.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_new_component/PlasmicNewComponentSection.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "csXhXQDIqh", "name": "NewComponentItem", "type": "managed", "projectId": "oermwjefjidrRRHcrxyCjQ", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_new_component/PlasmicNewComponentItem.tsx", "importSpec": {"modulePath": "wab/client/components/widgets/NewComponentItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_new_component/PlasmicNewComponentItem.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [{"id": "yV__Xr76s", "name": "ChevronRightIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_new_component/icons/PlasmicIcon__ChevronRight.tsx"}, {"id": "uSxcbtzK1j", "name": "ChevronBottomIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_new_component/icons/PlasmicIcon__ChevronBottom.tsx"}], "images": [], "projectApiToken": "2h8iTWhOR6E43NIqaQHzTSELhepdLOKHGCTBALpv9zqlohVqBNgMloCSDXjtX7HjpY5pZe6yKhkDVKPDZnqSQ", "codeComponents": [], "indirect": false, "globalContextsFilePath": "", "splitsProviderFilePath": "", "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "9csusiyEETC5n9fFKLeYNK", "projectName": "[PlasmicKit] Data Queries", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/plasmic_kit_data_queries/plasmic_plasmic_kit_data_queries.module.css", "components": [{"id": "hkmuxJmyM9", "name": "RestBuilder", "type": "managed", "projectId": "9csusiyEETC5n9fFKLeYNK", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_queries/PlasmicRestBuilder.tsx", "importSpec": {"modulePath": "wab/client/components/RestBuilder.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_queries/PlasmicRestBuilder.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "_VRtHiszCx", "name": "ListBuilder", "type": "managed", "projectId": "9csusiyEETC5n9fFKLeYNK", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_queries/PlasmicListBuilder.tsx", "importSpec": {"modulePath": "wab/client/components/ListBuilder.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_queries/PlasmicListBuilder.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "udG9wNYCNL", "name": "KeyValueRow", "type": "managed", "projectId": "9csusiyEETC5n9fFKLeYNK", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_queries/PlasmicKeyValueRow.tsx", "importSpec": {"modulePath": "wab/client/components/KeyValueRow.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_queries/PlasmicKeyValueRow.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "1ooaehe0m9", "name": "AuthForm", "type": "managed", "projectId": "9csusiyEETC5n9fFKLeYNK", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_queries/PlasmicAuthForm.tsx", "importSpec": {"modulePath": "wab/client/components/AuthForm.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_queries/PlasmicAuthForm.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "SsRHEyfw_M", "name": "QueriesSection", "type": "managed", "projectId": "9csusiyEETC5n9fFKLeYNK", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_queries/PlasmicQueriesSection.tsx", "importSpec": {"modulePath": "wab/client/components/QueriesSection.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_queries/PlasmicQueriesSection.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "dtgx0NGfys", "name": "QueryRow", "type": "managed", "projectId": "9csusiyEETC5n9fFKLeYNK", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_queries/PlasmicQueryRow.tsx", "importSpec": {"modulePath": "wab/client/components/QueryRow.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_queries/PlasmicQueryRow.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "_Lp0iIQjbN", "name": "DataSource", "type": "managed", "projectId": "9csusiyEETC5n9fFKLeYNK", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_queries/PlasmicDataSource.tsx", "importSpec": {"modulePath": "wab/client/components/DataSource.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_queries/PlasmicDataSource.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "Rh23GExBNXe", "name": "ConnectToDataSource", "type": "managed", "projectId": "9csusiyEETC5n9fFKLeYNK", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_queries/PlasmicConnectToDataSource.tsx", "importSpec": {"modulePath": "wab/client/components/ConnectToDataSource.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_queries/PlasmicConnectToDataSource.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [{"id": "6YWYAmDA19k", "name": "ChevronBottomIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_data_queries/icons/PlasmicIcon__ChevronBottom.tsx"}], "images": [{"id": "9iFo08KkR", "name": "image", "filePath": "wab/client/plasmic/plasmic_kit_data_queries/images/image.png"}, {"id": "i3GyxvVFz", "name": "image 2", "filePath": "wab/client/plasmic/plasmic_kit_data_queries/images/image2.png"}, {"id": "PzwwbIP93", "name": "image 3", "filePath": "wab/client/plasmic/plasmic_kit_data_queries/images/image3.png"}, {"id": "gm-UM3pEu", "name": "image 4", "filePath": "wab/client/plasmic/plasmic_kit_data_queries/images/image4.png"}, {"id": "lYsHu22AE", "name": "image 5", "filePath": "wab/client/plasmic/plasmic_kit_data_queries/images/image5.png"}, {"id": "SIWLjberf", "name": "image 6", "filePath": "wab/client/plasmic/plasmic_kit_data_queries/images/image6.png"}, {"id": "aBOYClima", "name": "image 7", "filePath": "wab/client/plasmic/plasmic_kit_data_queries/images/image7.png"}, {"id": "Pd4OAIwV_", "name": "image 8", "filePath": "wab/client/plasmic/plasmic_kit_data_queries/images/image8.png"}, {"id": "hbmHndPeK", "name": "image 9", "filePath": "wab/client/plasmic/plasmic_kit_data_queries/images/image9.png"}, {"id": "R71lPt9hC", "name": "image 10", "filePath": "wab/client/plasmic/plasmic_kit_data_queries/images/image10.png"}, {"id": "DCKWvMlr3", "name": "image 11", "filePath": "wab/client/plasmic/plasmic_kit_data_queries/images/image11.png"}, {"id": "fAt8vyqin", "name": "image 12", "filePath": "wab/client/plasmic/plasmic_kit_data_queries/images/image12.png"}, {"id": "jLLOVv4um", "name": "image 13", "filePath": "wab/client/plasmic/plasmic_kit_data_queries/images/image13.png"}, {"id": "tclh459HJ", "name": "image 14", "filePath": "wab/client/plasmic/plasmic_kit_data_queries/images/image14.png"}, {"id": "Q-19QWMVR", "name": "image 15", "filePath": "wab/client/plasmic/plasmic_kit_data_queries/images/image15.png"}, {"id": "waXA3wvyg", "name": "image 16", "filePath": "wab/client/plasmic/plasmic_kit_data_queries/images/image16.png"}], "projectApiToken": "S5TER5KAuMZ3Wf9Iy2A01juaVBkGwGob8VksgxmALZFAczXn20uU1ax0EJDrgUuisISHMSqlxd8gKT3OhQ", "codeComponents": [], "indirect": false, "globalContextsFilePath": "", "splitsProviderFilePath": "", "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "4nEqjj19Sbp3EVnBkgQMP1", "projectName": "[PlasmicKit] Data Expressions", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/plasmic_kit_data_expressions/plasmic_plasmic_kit_data_expressions.module.css", "components": [{"id": "cOWhlnv8o5", "name": "SimplePathBuilder", "type": "managed", "projectId": "4nEqjj19Sbp3EVnBkgQMP1", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_expressions/PlasmicSimplePathBuilder.tsx", "importSpec": {"modulePath": "wab/client/components/SimplePathBuilder.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_expressions/PlasmicSimplePathBuilder.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "FOLsgsm2iy", "name": "SimplePathRow", "type": "managed", "projectId": "4nEqjj19Sbp3EVnBkgQMP1", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_expressions/PlasmicSimplePathRow.tsx", "importSpec": {"modulePath": "wab/client/components/SimplePathRow.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_expressions/PlasmicSimplePathRow.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "D_TguRKWxB", "name": "SimplePathColumn", "type": "managed", "projectId": "4nEqjj19Sbp3EVnBkgQMP1", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_expressions/PlasmicSimplePathColumn.tsx", "importSpec": {"modulePath": "wab/client/components/SimplePathColumn.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_expressions/PlasmicSimplePathColumn.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "LRUE0mIhfL", "name": "SimplePathRowHeader", "type": "managed", "projectId": "4nEqjj19Sbp3EVnBkgQMP1", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_expressions/PlasmicSimplePathRowHeader.tsx", "importSpec": {"modulePath": "wab/client/components/SimplePathRowHeader.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_expressions/PlasmicSimplePathRowHeader.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [], "images": [], "projectApiToken": "gCQeXJm9PSxbK3mjIxGTRyQPApTB7LsC5Kw2IlbONhZyuwOsIrp6KMeHuoeIAwohLJahhEFSCe7hl379B0m10A", "codeComponents": [], "indirect": false, "globalContextsFilePath": "", "splitsProviderFilePath": "", "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "pTmuho7nuNtDcvZAf2kJgx", "projectName": "[PlasmicKit] Code Display and Onboarding", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/plasmic_kit_code_display_and_onboarding/plasmic_plasmic_kit_code_display_and_onboarding.module.css", "components": [{"id": "jLDeDF206V", "name": "CodeQuickstartDisplay", "type": "managed", "projectId": "pTmuho7nuNtDcvZAf2kJgx", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_code_display_and_onboarding/PlasmicCodeQuickstartDisplay.tsx", "importSpec": {"modulePath": "wab/client/components/studio/code-quickstart/CodeQuickstartDisplay.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_code_display_and_onboarding/PlasmicCodeQuickstartDisplay.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "aSLlLoswhi", "name": "FrameworkTab", "type": "managed", "projectId": "pTmuho7nuNtDcvZAf2kJgx", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_code_display_and_onboarding/PlasmicFrameworkTab.tsx", "importSpec": {"modulePath": "wab/client/components/studio/code-quickstart/FrameworkTab.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_code_display_and_onboarding/PlasmicFrameworkTab.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "tf_fQvs5kI8", "name": "FrameworkTabs", "type": "managed", "projectId": "pTmuho7nuNtDcvZAf2kJgx", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_code_display_and_onboarding/PlasmicFrameworkTabs.tsx", "importSpec": {"modulePath": "wab/client/components/studio/code-quickstart/FrameworkTabs.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_code_display_and_onboarding/PlasmicFrameworkTabs.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [{"id": "XYB65iJ0Dd", "name": "NextjsIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_code_display_and_onboarding/icons/PlasmicIcon__Nextjs.tsx"}, {"id": "tVE40jlrgY", "name": "GraphqlIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_code_display_and_onboarding/icons/PlasmicIcon__Graphql.tsx"}, {"id": "0QyD0vO4WB", "name": "RestIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_code_display_and_onboarding/icons/PlasmicIcon__Rest.tsx"}, {"id": "iHJMrwqTt", "name": "ReactIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_code_display_and_onboarding/icons/PlasmicIcon__React.tsx"}], "images": [{"id": "HFAyqV1qc2", "name": "gatsby", "filePath": "wab/client/plasmic/plasmic_kit_code_display_and_onboarding/images/gatsby.svg"}, {"id": "Sk5qCRrjj2", "name": "php", "filePath": "wab/client/plasmic/plasmic_kit_code_display_and_onboarding/images/php.svg"}, {"id": "GLPcV-_90n", "name": "javascript", "filePath": "wab/client/plasmic/plasmic_kit_code_display_and_onboarding/images/javascript.svg"}, {"id": "gyW-GjPYh", "name": "hydrogen", "filePath": "wab/client/plasmic/plasmic_kit_code_display_and_onboarding/images/hydrogen.png"}, {"id": "pu_IajWxD", "name": "remix", "filePath": "wab/client/plasmic/plasmic_kit_code_display_and_onboarding/images/remix.png"}, {"id": "sERoZ_FuGIf4", "name": "tanstack", "filePath": "wab/client/plasmic/plasmic_kit_code_display_and_onboarding/images/tanstack.png"}], "codeComponents": [{"id": "oO7ExdUoV3", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "wJ_Zj0doAI", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "projectApiToken": "3fXpGeJRKUtki3BjZYejcOf4aelbtrgQusidUlvDAYx430OSNcXCjoowZYTxxlghNlfx4TelnEJxHKzJNg", "indirect": false, "globalContextsFilePath": "", "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "sDniSX4oPUZFyk2sXXb3nh", "projectName": "[PlasmicKit] Text Mixins: Product", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/q_4_text_mixins_product/plasmic_q_4_text_mixins_product.module.css", "components": [], "icons": [], "images": [], "codeComponents": [{"id": "g9Es4bt3-h", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "3QMYyAfbYR", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "projectApiToken": "WZnqNPTrQWzh5I8cONNBKCxmEf6EHRATFi0toD2IS019rO2Lk6XuFRZFQ3y7JBhKPWhROn9mnR7VjJiVFtw", "indirect": false, "globalContextsFilePath": "", "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "oT38tGyqov9SPWHpf3Y2Rf", "projectName": "[PlasmicKit] Icons", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/plasmic_kit_icons/plasmic_q_4_icons.module.css", "components": [], "icons": [{"id": "-9fV-CD81", "name": "PlaySvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit/PlasmicIcon__PlaySvg.tsx"}, {"id": "acQSGk0ooHoA", "name": "ThemeIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Theme.tsx"}, {"id": "igh8wrhpn", "name": "ActivitySvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ActivitySvg.tsx"}, {"id": "vqVlPuFzm", "name": "AerialSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AerialSvg.tsx"}, {"id": "WLEN1J3xu", "name": "AlarmSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AlarmSvg.tsx"}, {"id": "hQH1yApvz", "name": "AlignArrowDownSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AlignArrowDownSvg.tsx"}, {"id": "oae69XYvx", "name": "AlignArrowLeftSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AlignArrowLeftSvg.tsx"}, {"id": "2qjc<PERSON><PERSON>b", "name": "AlignArrowRightSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AlignArrowRightSvg.tsx"}, {"id": "qygg8HW42", "name": "AlignArrowUpSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AlignArrowUpSvg.tsx"}, {"id": "VwV6Ulj5-", "name": "AlignBottomSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AlignBottomSvg.tsx"}, {"id": "jFtJFE8eu", "name": "AlignHorizontalCenterSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AlignHorizontalCenterSvg.tsx"}, {"id": "XcxCdeeOk", "name": "AlignLeftSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AlignLeftSvg.tsx"}, {"id": "VHiiQU2S7", "name": "AlignRightSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AlignRightSvg.tsx"}, {"id": "p6IPdv_hX", "name": "AlignTopSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AlignTopSvg.tsx"}, {"id": "tM3GRrOCz", "name": "AlignVerticalCenterSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AlignVerticalCenterSvg.tsx"}, {"id": "qot1p_V-p", "name": "AnchorSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AnchorSvg.tsx"}, {"id": "pE4NBOIDO", "name": "AngleSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AngleSvg.tsx"}, {"id": "UHxJQfjPm", "name": "AnnotationSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AnnotationSvg.tsx"}, {"id": "6itmRPZ70", "name": "ApplePaySvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ApplePaySvg.tsx"}, {"id": "FzrInIPnx", "name": "AppsSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AppsSvg.tsx"}, {"id": "Z8rFV09Hx", "name": "AquariusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AquariusSvg.tsx"}, {"id": "S_h-vQLLj", "name": "AriesSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AriesSvg.tsx"}, {"id": "ihaJ7xNgI", "name": "ArrowDownCircleSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ArrowDownCircleSvg.tsx"}, {"id": "wGs9VaAWT", "name": "ArrowDownLeftCircleSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ArrowDownLeftCircleSvg.tsx"}, {"id": "bvcLso9JM", "name": "ArrowDownRightCircleSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ArrowDownRightCircleSvg.tsx"}, {"id": "8YZcL7EJC", "name": "ArrowUpCircleSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ArrowUpCircleSvg.tsx"}, {"id": "oISRcOZA0", "name": "ArrowUpLeftCircleSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ArrowUpLeftCircleSvg.tsx"}, {"id": "M0PeB80nl", "name": "ArrowUpRightCircleSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ArrowUpRightCircleSvg.tsx"}, {"id": "4BNOoe_IX", "name": "AwardSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AwardSvg.tsx"}, {"id": "XI0DEJCdE", "name": "Badge2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Badge2Svg.tsx"}, {"id": "vTuOPPCLb", "name": "BallotBoxSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BallotBoxSvg.tsx"}, {"id": "treZFZkvm", "name": "BallotSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BallotSvg.tsx"}, {"id": "MOQRlrif2", "name": "BandAidSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BandAidSvg.tsx"}, {"id": "QMdU7u4YU", "name": "BarcodeSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BarcodeSvg.tsx"}, {"id": "j3NpIy3Tu", "name": "BaseballSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BaseballSvg.tsx"}, {"id": "HoyimMrcp", "name": "BeachSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BeachSvg.tsx"}, {"id": "T8oqxdSot", "name": "BedSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BedSvg.tsx"}, {"id": "-3k87lWDx", "name": "BellRingingSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BellRingingSvg.tsx"}, {"id": "n1ry5UKaR", "name": "BitcoinSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BitcoinSvg.tsx"}, {"id": "xmAZ-8GGm", "name": "BoatSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BoatSvg.tsx"}, {"id": "OiX2-qUyR", "name": "BoldSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BoldSvg.tsx"}, {"id": "IRA31HnIa", "name": "BoltSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BoltSvg.tsx"}, {"id": "I5z7WRVzl", "name": "Book2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Book2Svg.tsx"}, {"id": "5nM_HEMwG", "name": "Briefcase2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Briefcase2Svg.tsx"}, {"id": "p4lIbDxAb", "name": "BroadcastSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BroadcastSvg.tsx"}, {"id": "QAcjTlbLZ", "name": "BrowserSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BrowserSvg.tsx"}, {"id": "dr-taeFRA", "name": "BugSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BugSvg.tsx"}, {"id": "UZE5v8voG", "name": "BusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BusSvg.tsx"}, {"id": "po3ctVDKQ", "name": "CancerSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CancerSvg.tsx"}, {"id": "buZNlBMu6", "name": "CapricornSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CapricornSvg.tsx"}, {"id": "OD4XsOluj", "name": "CarDashboardSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CarDashboardSvg.tsx"}, {"id": "3ksqxZZrh", "name": "CarSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CarSvg.tsx"}, {"id": "xCnZMCArQ", "name": "CaratDownSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CaratDownSvg.tsx"}, {"id": "BxRVRfKSR", "name": "CaratLeftSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CaratLeftSvg.tsx"}, {"id": "maY6BJoBJ", "name": "CaratRightSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CaratRightSvg.tsx"}, {"id": "z4l_sMLYz", "name": "CaratUpSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CaratUpSvg.tsx"}, {"id": "8gBFK07dp", "name": "CelsiusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CelsiusSvg.tsx"}, {"id": "2Q2zjFpZN", "name": "CircleIntersectSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CircleIntersectSvg.tsx"}, {"id": "E1ZODqLSR", "name": "ColorSwatchSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ColorSwatchSvg.tsx"}, {"id": "y1jYDlN5t", "name": "Columns2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Columns2Svg.tsx"}, {"id": "Q6f2WbvO1", "name": "CommandSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CommandSvg.tsx"}, {"id": "HPi2p0E2E", "name": "CompassSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CompassSvg.tsx"}, {"id": "coPzxnFyi", "name": "ComponentsSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ComponentsSvg.tsx"}, {"id": "50dPI-xUS", "name": "CreditCardSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CreditCardSvg.tsx"}, {"id": "nT-yQpyfW", "name": "CrosshairSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CrosshairSvg.tsx"}, {"id": "qVAtg_gFz", "name": "Crown2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Crown2Svg.tsx"}, {"id": "ZhdL0Qi1n", "name": "CrownSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CrownSvg.tsx"}, {"id": "LhM-KJAvS", "name": "CursorClickSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CursorClickSvg.tsx"}, {"id": "Xll5GBkyE", "name": "CursorTextSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CursorTextSvg.tsx"}, {"id": "U7vmNRf_p", "name": "DatabaseErrorSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DatabaseErrorSvg.tsx"}, {"id": "CWRCiAGbu", "name": "DatabaseMinusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DatabaseMinusSvg.tsx"}, {"id": "kCkGxwu-c", "name": "DatabasePlusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DatabasePlusSvg.tsx"}, {"id": "I6B50v8zj", "name": "DatabaseSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DatabaseSvg.tsx"}, {"id": "ZfCYqdahE", "name": "Dice1SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Dice1Svg.tsx"}, {"id": "fi0EybSSQ", "name": "Dice2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Dice2Svg.tsx"}, {"id": "HcbNpRcEM", "name": "Dice3SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Dice3Svg.tsx"}, {"id": "mh2Wair68", "name": "Dice4SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Dice4Svg.tsx"}, {"id": "arJVEn32T", "name": "Dice5SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Dice5Svg.tsx"}, {"id": "i4qk2vmX5", "name": "Dice6SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Dice6Svg.tsx"}, {"id": "0fa_GOpNv", "name": "DimensionsSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DimensionsSvg.tsx"}, {"id": "7B3pyeusm", "name": "Dogecoin2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Dogecoin2Svg.tsx"}, {"id": "bJdKgSNUi", "name": "DogecoinSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DogecoinSvg.tsx"}, {"id": "g3CtGQ0JO", "name": "Dollar2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Dollar2Svg.tsx"}, {"id": "QOvRIC8Sf", "name": "DoorEnterSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DoorEnterSvg.tsx"}, {"id": "WoBGlg51H", "name": "DoorExitSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DoorExitSvg.tsx"}, {"id": "cq-fLP3K_", "name": "DoorSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DoorSvg.tsx"}, {"id": "xdn8wiJBv", "name": "DotsHorizontalCircleSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DotsHorizontalCircleSvg.tsx"}, {"id": "53AAjlkdE", "name": "DotsVerticalCircleSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DotsVerticalCircleSvg.tsx"}, {"id": "hCX-Nw1aN", "name": "DownloadCloudSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DownloadCloudSvg.tsx"}, {"id": "6O7AzEyUC", "name": "DribbbleSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DribbbleSvg.tsx"}, {"id": "Tmg-EC9an", "name": "DrillSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DrillSvg.tsx"}, {"id": "El-dMVtxb", "name": "DrinkSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DrinkSvg.tsx"}, {"id": "AZ-7Q0X4d", "name": "DropboxSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DropboxSvg.tsx"}, {"id": "hiOVmqIHj", "name": "EggTimerSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__EggTimerSvg.tsx"}, {"id": "CQETqdCzG", "name": "EmojiNeutralSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__EmojiNeutralSvg.tsx"}, {"id": "xvJAFfCO1", "name": "EthereumSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__EthereumSvg.tsx"}, {"id": "8wJ54aB98", "name": "EuroSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__EuroSvg.tsx"}, {"id": "trnlWGn9z", "name": "FaceIdErrorSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FaceIdErrorSvg.tsx"}, {"id": "G7s0YmuGw", "name": "FacebookSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FacebookSvg.tsx"}, {"id": "emJqOiCtU", "name": "FahrenheitSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FahrenheitSvg.tsx"}, {"id": "bvGzeO1pk", "name": "FeatherSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FeatherSvg.tsx"}, {"id": "cReVjwCaJ", "name": "FigmaSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FigmaSvg.tsx"}, {"id": "zldfLXBdc", "name": "File2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__File2Svg.tsx"}, {"id": "sXmaMJmUQ", "name": "FileDownload2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FileDownload2Svg.tsx"}, {"id": "-Rt1rK3rU", "name": "FileMinus2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FileMinus2Svg.tsx"}, {"id": "7g7eA2esl", "name": "FilePlus2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FilePlus2Svg.tsx"}, {"id": "fXK6I-4Vb", "name": "FileText2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FileText2Svg.tsx"}, {"id": "wOWg4bnpO", "name": "FingerprintSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FingerprintSvg.tsx"}, {"id": "hXBIfhsbj", "name": "FlagPriority2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FlagPriority2Svg.tsx"}, {"id": "h0aes-KgI", "name": "FlagPriority3SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FlagPriority3Svg.tsx"}, {"id": "HTncQnRhf", "name": "FlagPrioritySvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FlagPrioritySvg.tsx"}, {"id": "XSkmUZQzW", "name": "FlipHorizontallySvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FlipHorizontallySvg.tsx"}, {"id": "Uzx3QAEtV", "name": "FlipVerticallySvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FlipVerticallySvg.tsx"}, {"id": "HZXffCxYA", "name": "FloatCenterSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FloatCenterSvg.tsx"}, {"id": "1qOrjWeCk", "name": "FloatLeftSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FloatLeftSvg.tsx"}, {"id": "RLgQjkks1", "name": "FloatRightSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FloatRightSvg.tsx"}, {"id": "_qwDVzspV", "name": "FolderCheckSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FolderCheckSvg.tsx"}, {"id": "z2x5bfpl9", "name": "FontFamilySvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FontFamilySvg.tsx"}, {"id": "26dKxOhX0", "name": "FontSizeSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FontSizeSvg.tsx"}, {"id": "8RMifFG7Y", "name": "Forbid2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Forbid2Svg.tsx"}, {"id": "zGZ5m7RLu", "name": "ForbidSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ForbidSvg.tsx"}, {"id": "iPpNdG10U", "name": "ForwardSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ForwardSvg.tsx"}, {"id": "lcERBKegv", "name": "FrameSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FrameSvg.tsx"}, {"id": "VpRw--4HI", "name": "FramerSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FramerSvg.tsx"}, {"id": "4R4kI_NqZ", "name": "FullscreenSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FullscreenSvg.tsx"}, {"id": "S2s0nwTfz", "name": "GamingSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__GamingSvg.tsx"}, {"id": "p0LXoqrs8", "name": "GavelSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__GavelSvg.tsx"}, {"id": "wCqH2thrR", "name": "GbpSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__GbpSvg.tsx"}, {"id": "NFQyfQ7_0", "name": "GeminiSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__GeminiSvg.tsx"}, {"id": "IJenFS-RR", "name": "GiftSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__GiftSvg.tsx"}, {"id": "4OBJfCUZH", "name": "GitBranchSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__GitBranchSvg.tsx"}, {"id": "TNOBvgIn3", "name": "GitCommitSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__GitCommitSvg.tsx"}, {"id": "JG-hb2GyP", "name": "GitDiffSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__GitDiffSvg.tsx"}, {"id": "rrDGdCiID", "name": "GitForkSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__GitForkSvg.tsx"}, {"id": "dLlMqzpnQ", "name": "GitMergeSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__GitMergeSvg.tsx"}, {"id": "VG8e82icZ", "name": "GitPullRequestSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__GitPullRequestSvg.tsx"}, {"id": "gkKuB3ud3", "name": "GithubSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__GithubSvg.tsx"}, {"id": "-SHgAXCY8", "name": "GmailSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__GmailSvg.tsx"}, {"id": "eJ5NbJFL5", "name": "GolfBallSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__GolfBallSvg.tsx"}, {"id": "U5dSOeF1P", "name": "GolfSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__GolfSvg.tsx"}, {"id": "nGjRKgSAl", "name": "GoogleDriveSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__GoogleDriveSvg.tsx"}, {"id": "EJFlbmWSA", "name": "GoogleSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__GoogleSvg.tsx"}, {"id": "QurmMGhHi", "name": "HammerSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__HammerSvg.tsx"}, {"id": "BiX8j3MLg", "name": "HeadingSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__HeadingSvg.tsx"}, {"id": "vv085SiWV", "name": "HealthSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__HealthSvg.tsx"}, {"id": "c48iKRQzi", "name": "HelicopterSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__HelicopterSvg.tsx"}, {"id": "rCDPKMqH2", "name": "HubspotSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__HubspotSvg.tsx"}, {"id": "seOPkhidE", "name": "IceSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__IceSvg.tsx"}, {"id": "ryHL7ERmv", "name": "InstagramSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__InstagramSvg.tsx"}, {"id": "r4OWu7WT_", "name": "IphoneSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__IphoneSvg.tsx"}, {"id": "taSQD6Nog", "name": "ItalicSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ItalicSvg.tsx"}, {"id": "-kK8XMeMZ", "name": "LampSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LampSvg.tsx"}, {"id": "ZB5uOsMFD", "name": "LanguageSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LanguageSvg.tsx"}, {"id": "L5ksrV7aC", "name": "LanguagesSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LanguagesSvg.tsx"}, {"id": "u5ioyscr2", "name": "LaptopSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LaptopSvg.tsx"}, {"id": "_lbxYEIyU", "name": "LeaderboardSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LeaderboardSvg.tsx"}, {"id": "vUoVxVkvM", "name": "LeafSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LeafSvg.tsx"}, {"id": "HhSXpOnKQ", "name": "LeoSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LeoSvg.tsx"}, {"id": "JOxbQeaiE", "name": "LetterSpacingSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LetterSpacingSvg.tsx"}, {"id": "VGeoor2Yp", "name": "LibraSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LibraSvg.tsx"}, {"id": "fUvsJ85PD", "name": "LightningSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LightningSvg.tsx"}, {"id": "K49b5_G22", "name": "LineHeightSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LineHeightSvg.tsx"}, {"id": "Xz5nlnOMd", "name": "LinkBreakSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LinkBreakSvg.tsx"}, {"id": "VqPPYYn0j", "name": "LinkedSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LinkedSvg.tsx"}, {"id": "Soyh0g3hh", "name": "LinkedinSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LinkedinSvg.tsx"}, {"id": "GFqu6RPnc", "name": "MagicHatSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__MagicHatSvg.tsx"}, {"id": "JDQY7SekV", "name": "MagnetSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__MagnetSvg.tsx"}, {"id": "iaag049qq", "name": "MailOpenSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__MailOpenSvg.tsx"}, {"id": "96Ef5pD-e", "name": "Maximize2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Maximize2Svg.tsx"}, {"id": "Z9SVfq2Q0", "name": "Minimize2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Minimize2Svg.tsx"}, {"id": "rOskwy-1O", "name": "MountainSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__MountainSvg.tsx"}, {"id": "6ILeslvy-", "name": "MoveSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__MoveSvg.tsx"}, {"id": "msmZ4iuF2", "name": "Mug2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Mug2Svg.tsx"}, {"id": "J_yLTnQPW", "name": "Mug3SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Mug3Svg.tsx"}, {"id": "X5z9t20mG", "name": "MugSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__MugSvg.tsx"}, {"id": "3UzX_X5ad", "name": "NavigationSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__NavigationSvg.tsx"}, {"id": "HEYbxWhJs", "name": "NordvpnSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__NordvpnSvg.tsx"}, {"id": "gZJumPitX", "name": "NotebookSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__NotebookSvg.tsx"}, {"id": "wgRtqHaDo", "name": "OctagonSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__OctagonSvg.tsx"}, {"id": "RRQ8nHz93", "name": "OrderedListSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__OrderedListSvg.tsx"}, {"id": "Oi2fvLw2B", "name": "PaddingSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PaddingSvg.tsx"}, {"id": "WPHwb7V6d", "name": "PaintbrushSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PaintbrushSvg.tsx"}, {"id": "Pn_eVlOVJ", "name": "Paintbucket2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Paintbucket2Svg.tsx"}, {"id": "uDpH3xBf5", "name": "PaperclipSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PaperclipSvg.tsx"}, {"id": "qimUc_U-Q", "name": "ParenthesisSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ParenthesisSvg.tsx"}, {"id": "2yA7IzUe9", "name": "PassportSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PassportSvg.tsx"}, {"id": "gp_DwSd-z", "name": "PenToolSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PenToolSvg.tsx"}, {"id": "bGCkFMqnI", "name": "PenSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PenSvg.tsx"}, {"id": "xpdvV87ZB", "name": "Pencil2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Pencil2Svg.tsx"}, {"id": "540duoJvb", "name": "PencilSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PencilSvg.tsx"}, {"id": "5nTaRqzCu", "name": "PhotoErrorSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PhotoErrorSvg.tsx"}, {"id": "a43wJ8Xvp", "name": "PhotoPlusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PhotoPlusSvg.tsx"}, {"id": "bhUV0lOod", "name": "PhotoshopSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PhotoshopSvg.tsx"}, {"id": "XqZfCR6sg", "name": "PillsSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PillsSvg.tsx"}, {"id": "OfCasDDRP", "name": "PinTack2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PinTack2Svg.tsx"}, {"id": "6Y0COia7b", "name": "PiscesSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PiscesSvg.tsx"}, {"id": "pVtaE6EWa", "name": "PlaneSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PlaneSvg.tsx"}, {"id": "_b7kbcePS", "name": "PolyworkSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PolyworkSvg.tsx"}, {"id": "RajTx62ji", "name": "PowerSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PowerSvg.tsx"}, {"id": "HXGSn0tkg", "name": "QuoteSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__QuoteSvg.tsx"}, {"id": "_DrdN7d2s", "name": "RainSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__RainSvg.tsx"}, {"id": "L0dposNbx", "name": "RedditSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__RedditSvg.tsx"}, {"id": "yxGlT5JuT", "name": "Refresh2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Refresh2Svg.tsx"}, {"id": "ro1KQEYbb", "name": "ReplySvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ReplySvg.tsx"}, {"id": "uRQfbBjV9", "name": "RocketSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__RocketSvg.tsx"}, {"id": "y7bsPqEtH", "name": "Rotate2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Rotate2Svg.tsx"}, {"id": "Dhb-9si9H", "name": "RotateSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__RotateSvg.tsx"}, {"id": "qYnpa1pnu", "name": "RoundedCornersBlSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__RoundedCornersBlSvg.tsx"}, {"id": "qn1GMFsob", "name": "RoundedCornersBrSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__RoundedCornersBrSvg.tsx"}, {"id": "eoAeezknE", "name": "RoundedCornersTlSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__RoundedCornersTlSvg.tsx"}, {"id": "6aDMmk0rk", "name": "RoundedCornersTrSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__RoundedCornersTrSvg.tsx"}, {"id": "Ou1BLFBh7", "name": "Rows2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Rows2Svg.tsx"}, {"id": "FWguCkhD_", "name": "RssFeedSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__RssFeedSvg.tsx"}, {"id": "yQTuK78DX", "name": "SafeSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SafeSvg.tsx"}, {"id": "qId9EiZe_", "name": "SagittariusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SagittariusSvg.tsx"}, {"id": "kgIRLzUBo", "name": "SalesforceSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SalesforceSvg.tsx"}, {"id": "kqXR30v6r", "name": "ScanMinusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ScanMinusSvg.tsx"}, {"id": "JicfEY61y", "name": "ScanPlusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ScanPlusSvg.tsx"}, {"id": "9b34zWJKS", "name": "ScrewSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ScrewSvg.tsx"}, {"id": "jFYH6BL9T", "name": "Settings2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Settings2Svg.tsx"}, {"id": "7eYxDDkA7", "name": "ShapeRotateSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ShapeRotateSvg.tsx"}, {"id": "qUZtFEqYQ", "name": "ShapeSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ShapeSvg.tsx"}, {"id": "hJstzMLhF", "name": "Share2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Share2Svg.tsx"}, {"id": "afZsx7Rqd", "name": "SketchSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SketchSvg.tsx"}, {"id": "QZw9S1g2R", "name": "SkullSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SkullSvg.tsx"}, {"id": "LpatjWKer", "name": "SlidersHorizontalSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SlidersHorizontalSvg.tsx"}, {"id": "IaaA9cSea", "name": "SlidersVerticalSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SlidersVerticalSvg.tsx"}, {"id": "k_43sGa5M", "name": "SlideshowSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SlideshowSvg.tsx"}, {"id": "I_sLyPq1N", "name": "SortAscendingSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SortAscendingSvg.tsx"}, {"id": "N6nTBakNb", "name": "SortDescendingSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SortDescendingSvg.tsx"}, {"id": "6oabvAkLR", "name": "SoundcloudSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SoundcloudSvg.tsx"}, {"id": "J--pPPOUr", "name": "SpacingHorizontalSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SpacingHorizontalSvg.tsx"}, {"id": "9Z0Cu-c5J", "name": "SparklesSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SparklesSvg.tsx"}, {"id": "63pBbdz-j", "name": "Speaker2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Speaker2Svg.tsx"}, {"id": "ma70zLLmI", "name": "SpeakerMute2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SpeakerMute2Svg.tsx"}, {"id": "SbarV-87j", "name": "SpeakerMuteSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SpeakerMuteSvg.tsx"}, {"id": "n6eolUKOy", "name": "SpeakerOffSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SpeakerOffSvg.tsx"}, {"id": "-QEcPiTNN", "name": "SpeakerVolumeHighSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SpeakerVolumeHighSvg.tsx"}, {"id": "wbxvTkSuu", "name": "SpeakerVolumeLowSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SpeakerVolumeLowSvg.tsx"}, {"id": "cJCpmEQFZ", "name": "SpeechBubbleMinusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SpeechBubbleMinusSvg.tsx"}, {"id": "g2gTPsRaJ", "name": "SpeechBubblePlusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SpeechBubblePlusSvg.tsx"}, {"id": "nkJ1joJAv", "name": "SpeechBubbleSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SpeechBubbleSvg.tsx"}, {"id": "p_Tng7_Oi", "name": "SquareCheckFilledSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SquareCheckFilledSvg.tsx"}, {"id": "u-WS63xap", "name": "SquareCheckSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SquareCheckSvg.tsx"}, {"id": "Zr0cufS1Y", "name": "SquareCrossSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SquareCrossSvg.tsx"}, {"id": "x12N2UVE8", "name": "SquareIntersectSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SquareIntersectSvg.tsx"}, {"id": "6a2Ojnos7", "name": "SquareMinusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SquareMinusSvg.tsx"}, {"id": "cajTWPzUC", "name": "SquarePlusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SquarePlusSvg.tsx"}, {"id": "zkj00JjZV", "name": "SquareSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SquareSvg.tsx"}, {"id": "87bP3-Fr<PERSON>", "name": "StackOverflowSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__StackOverflowSvg.tsx"}, {"id": "K2rOwFXAb", "name": "StethoscopeSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__StethoscopeSvg.tsx"}, {"id": "vwCTHrGcW", "name": "StreamToTv2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__StreamToTv2Svg.tsx"}, {"id": "8Dr3dHE5X", "name": "StrikethroughSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__StrikethroughSvg.tsx"}, {"id": "a3BXRQWY3", "name": "Sunrise2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Sunrise2Svg.tsx"}, {"id": "W2Gg6GU1g", "name": "SunriseSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SunriseSvg.tsx"}, {"id": "IqlmgBOHe", "name": "Switch2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Switch2Svg.tsx"}, {"id": "vsY9_836a", "name": "SwitchSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SwitchSvg.tsx"}, {"id": "PhUhHMaAc", "name": "TaurusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TaurusSvg.tsx"}, {"id": "xvg3hTWgl", "name": "TemperatureSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TemperatureSvg.tsx"}, {"id": "UX5NhlzBx", "name": "TextAlignCenterSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TextAlignCenterSvg.tsx"}, {"id": "b9QtfdzaM", "name": "TextAlignJustifiedSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TextAlignJustifiedSvg.tsx"}, {"id": "ELDliqOhK", "name": "TextAlignLeftSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TextAlignLeftSvg.tsx"}, {"id": "i7rvZLboi", "name": "TextAlignRightSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TextAlignRightSvg.tsx"}, {"id": "aSsCKBQhI", "name": "TextCapitaliseSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TextCapitaliseSvg.tsx"}, {"id": "qskRDaImj", "name": "TextLowercaseSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TextLowercaseSvg.tsx"}, {"id": "apnOQ6tRU", "name": "TextUppercaseSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TextUppercaseSvg.tsx"}, {"id": "e3P4cMPJR", "name": "TextSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TextSvg.tsx"}, {"id": "TJ3f0bZG2", "name": "TiktokSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TiktokSvg.tsx"}, {"id": "rUxKYtQ59", "name": "TimerSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TimerSvg.tsx"}, {"id": "eaDECQ83Y", "name": "ToggleLeftSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ToggleLeftSvg.tsx"}, {"id": "9J0KKbwHR", "name": "ToggleRightSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ToggleRightSvg.tsx"}, {"id": "psiuKHu0a", "name": "TrafficLightsSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TrafficLightsSvg.tsx"}, {"id": "nS4_I75qv", "name": "Trash2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Trash2Svg.tsx"}, {"id": "5kt39PjM_", "name": "TrashCanSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TrashCanSvg.tsx"}, {"id": "Pflx6OHws", "name": "TreesSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TreesSvg.tsx"}, {"id": "khvcmb8gq", "name": "TrophySvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TrophySvg.tsx"}, {"id": "nZ0oZKSVq", "name": "TwitchSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TwitchSvg.tsx"}, {"id": "iPU0TVsM0", "name": "TwitterSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TwitterSvg.tsx"}, {"id": "397nrVblB", "name": "UfoSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__UfoSvg.tsx"}, {"id": "qHqmU-L1G", "name": "UmbrellaSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__UmbrellaSvg.tsx"}, {"id": "LSpOu6_w3", "name": "UnderlineSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__UnderlineSvg.tsx"}, {"id": "suHkgkKOX", "name": "UnorderedListSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__UnorderedListSvg.tsx"}, {"id": "BgHLiXQLs", "name": "UploadCloudSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__UploadCloudSvg.tsx"}, {"id": "HJW5BG2gk", "name": "VaccineSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__VaccineSvg.tsx"}, {"id": "l9MTq2Rsu", "name": "VaseSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__VaseSvg.tsx"}, {"id": "L8cwmOppY", "name": "VirgoSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__VirgoSvg.tsx"}, {"id": "hxe2HBiSA", "name": "WallpaperSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__WallpaperSvg.tsx"}, {"id": "mMuK3rUJk", "name": "WandSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__WandSvg.tsx"}, {"id": "O83ns8Kpy", "name": "WindSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__WindSvg.tsx"}, {"id": "DymCcN2xU", "name": "WorldSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__WorldSvg.tsx"}, {"id": "jdMWGNZ6e", "name": "WrenchSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__WrenchSvg.tsx"}, {"id": "GweBIEpy6", "name": "XAxisSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__XAxisSvg.tsx"}, {"id": "1CytF7ymT", "name": "YAxisSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__YAxisSvg.tsx"}, {"id": "RBrT8-9LR", "name": "ZipFile2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ZipFile2Svg.tsx"}, {"id": "XNV-uVioa", "name": "AirplayToTvSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AirplayToTvSvg.tsx"}, {"id": "-k0Y2Oe2y", "name": "AnnotationDotsSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AnnotationDotsSvg.tsx"}, {"id": "Epua1czZC", "name": "AnnotationWarningSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AnnotationWarningSvg.tsx"}, {"id": "RyPYfBXrU", "name": "AnnouncementSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AnnouncementSvg.tsx"}, {"id": "-EPzsLkxb", "name": "ArchiveSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ArchiveSvg.tsx"}, {"id": "PIIvt05Eq", "name": "ArrowDownLeftSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ArrowDownLeftSvg.tsx"}, {"id": "uwJrAFT2a", "name": "ArrowDownRightSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ArrowDownRightSvg.tsx"}, {"id": "G2ToiXzCf", "name": "ArrowDownSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ArrowDownSvg.tsx"}, {"id": "-d8Kjj4sp", "name": "ArrowLeftSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ArrowLeftSvg.tsx"}, {"id": "9Jv8jb253", "name": "ArrowRightSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ArrowRightSvg.tsx"}, {"id": "AulWJRWcB", "name": "ArrowUpLeftSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ArrowUpLeftSvg.tsx"}, {"id": "N_BtK6grX", "name": "ArrowUpRightSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ArrowUpRightSvg.tsx"}, {"id": "zV3oP-tGj", "name": "ArrowUpSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ArrowUpSvg.tsx"}, {"id": "oWKJW4zkL", "name": "AtSignSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AtSignSvg.tsx"}, {"id": "quNnyW9Us", "name": "BadgeSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BadgeSvg.tsx"}, {"id": "rbcauJTXi", "name": "BankSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BankSvg.tsx"}, {"id": "qA39vYT23", "name": "BaseSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BaseSvg.tsx"}, {"id": "zHRDnhPGI", "name": "BatteryChargingSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BatteryChargingSvg.tsx"}, {"id": "uX4ADbmmj", "name": "BatteryFullSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BatteryFullSvg.tsx"}, {"id": "d49HWFLhU", "name": "BatteryLowSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BatteryLowSvg.tsx"}, {"id": "sOeR4sIIg", "name": "BatteryMediumSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BatteryMediumSvg.tsx"}, {"id": "f7M_519hz", "name": "BatterySvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BatterySvg.tsx"}, {"id": "v5660MTuj", "name": "BellOffSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BellOffSvg.tsx"}, {"id": "eCJ0k221t", "name": "BellSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BellSvg.tsx"}, {"id": "hxRmy8Nhq", "name": "BookSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BookSvg.tsx"}, {"id": "ifTRn8aE8", "name": "BookmarkSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BookmarkSvg.tsx"}, {"id": "0qLNxfRGB", "name": "BoxSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BoxSvg.tsx"}, {"id": "WuOohkH_c", "name": "BriefcaseSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BriefcaseSvg.tsx"}, {"id": "tCQfpLKDZ", "name": "BuildingStoreSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BuildingStoreSvg.tsx"}, {"id": "M04UrsVix", "name": "BuildingSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BuildingSvg.tsx"}, {"id": "ybKOJf_Q8", "name": "CalendarMinusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CalendarMinusSvg.tsx"}, {"id": "Gsw_v3Loe", "name": "CalendarPlusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CalendarPlusSvg.tsx"}, {"id": "BIfwFxs6b", "name": "CalendarSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CalendarSvg.tsx"}, {"id": "mIas_o1p8", "name": "CameraOffSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CameraOffSvg.tsx"}, {"id": "S50UWkKJd", "name": "CameraSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CameraSvg.tsx"}, {"id": "7ILIRqrYh", "name": "ChartSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ChartSvg.tsx"}, {"id": "h7sB2KeL-", "name": "CheckCircleSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CheckCircleSvg.tsx"}, {"id": "f0RrtBrXp", "name": "CheckSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CheckSvg.tsx"}, {"id": "xZrB9_0ir", "name": "ChevronDownSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ChevronDownSvg.tsx"}, {"id": "0-_N6JM-u", "name": "ChevronLeftSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ChevronLeftSvg.tsx"}, {"id": "HBGx-zeiX", "name": "ChevronRightSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ChevronRightSvg.tsx"}, {"id": "i9D87DzsX", "name": "ChevronUpSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ChevronUpSvg.tsx"}, {"id": "VBf-n64uS", "name": "CircleSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CircleSvg.tsx"}, {"id": "I4MAKaUBn", "name": "ClipboardSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ClipboardSvg.tsx"}, {"id": "9MhqTIkUT", "name": "ClockSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ClockSvg.tsx"}, {"id": "fJk_6BdDE", "name": "CloseCircleSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CloseCircleSvg.tsx"}, {"id": "DhvEHyCHT", "name": "CloseSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CloseSvg.tsx"}, {"id": "HsmX1Ngks", "name": "CloudSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CloudSvg.tsx"}, {"id": "PRwKEGpqv", "name": "CodeSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CodeSvg.tsx"}, {"id": "vt6PZs_I8", "name": "ColumnsHorizontalSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ColumnsHorizontalSvg.tsx"}, {"id": "cm8DQc0q-", "name": "ColumnsVerticalSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ColumnsVerticalSvg.tsx"}, {"id": "xD171fLyB", "name": "ColumnsSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ColumnsSvg.tsx"}, {"id": "vJVrKlrDD", "name": "ComponentSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ComponentSvg.tsx"}, {"id": "aGIZL6Ec9", "name": "CopySvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CopySvg.tsx"}, {"id": "CWhLiWprm", "name": "CropSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CropSvg.tsx"}, {"id": "WkJxLXV8n", "name": "CursorSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CursorSvg.tsx"}, {"id": "E_QXtB-Hf", "name": "DashboardSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DashboardSvg.tsx"}, {"id": "bbAxEiIwy", "name": "DevicesSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DevicesSvg.tsx"}, {"id": "uzBbTcdyE", "name": "DiamondsSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DiamondsSvg.tsx"}, {"id": "0ZHC3A__e", "name": "DollarSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DollarSvg.tsx"}, {"id": "XXTtWqB5P", "name": "DotSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DotSvg.tsx"}, {"id": "0NpI5-0F-", "name": "DotsSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DotsSvg.tsx"}, {"id": "f6tG0M1iK", "name": "DotsHorizontalSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DotsHorizontalSvg.tsx"}, {"id": "joYBQwH-P", "name": "DotsVerticalSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DotsVerticalSvg.tsx"}, {"id": "Bu7POPssl", "name": "DownloadSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DownloadSvg.tsx"}, {"id": "8Mn_oV3pg", "name": "DropletSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DropletSvg.tsx"}, {"id": "_Qa2gdunG", "name": "EditSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__EditSvg.tsx"}, {"id": "1Vli2Q2_d", "name": "EmojiHappySvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__EmojiHappySvg.tsx"}, {"id": "Df5ypwCLv", "name": "EmojiSadSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__EmojiSadSvg.tsx"}, {"id": "7OEXtT9yd", "name": "EyeOffSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__EyeOffSvg.tsx"}, {"id": "oFYcZi8LU", "name": "EyeSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__EyeSvg.tsx"}, {"id": "fVZc-usOD", "name": "FastForwardSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FastForwardSvg.tsx"}, {"id": "DxSAEuwlh", "name": "FileMinusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FileMinusSvg.tsx"}, {"id": "94DM39ysJ", "name": "FilePlusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FilePlusSvg.tsx"}, {"id": "I_xiNsW6B", "name": "FileTextSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FileTextSvg.tsx"}, {"id": "EUQHbIv9T", "name": "FileSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FileSvg.tsx"}, {"id": "7BhXMlSrH", "name": "FilmSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FilmSvg.tsx"}, {"id": "Tur0nARlL", "name": "FilterSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FilterSvg.tsx"}, {"id": "cRga1bCxm", "name": "FlagSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FlagSvg.tsx"}, {"id": "BFfpOGfdF", "name": "FolderMinusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FolderMinusSvg.tsx"}, {"id": "2W-w5dkHB", "name": "FolderPlusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FolderPlusSvg.tsx"}, {"id": "zvkxMkUIX", "name": "FolderSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FolderSvg.tsx"}, {"id": "gcxY0Mwvj", "name": "GlobeSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__GlobeSvg.tsx"}, {"id": "f5dXpZvP5", "name": "GridMasonrySvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__GridMasonrySvg.tsx"}, {"id": "dAixi7Lp7", "name": "GridSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__GridSvg.tsx"}, {"id": "jxIRSIMqs", "name": "GripSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__GripSvg.tsx"}, {"id": "_aGnHGgCP", "name": "HashtagSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__HashtagSvg.tsx"}, {"id": "CEenS4FsH", "name": "HeadphonesSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__HeadphonesSvg.tsx"}, {"id": "kIPugRFqu", "name": "HeartSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__HeartSvg.tsx"}, {"id": "zY-2PPrFT", "name": "HelpCircleSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__HelpCircleSvg.tsx"}, {"id": "RJTjU6wLp", "name": "HelpSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__HelpSvg.tsx"}, {"id": "c8yr0PMQi", "name": "HomeSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__HomeSvg.tsx"}, {"id": "iSl915wHU", "name": "IconSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__IconSvg.tsx"}, {"id": "MYq4lzBmH", "name": "InboxSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__InboxSvg.tsx"}, {"id": "hqBNVBJWB", "name": "InformationSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__InformationSvg.tsx"}, {"id": "xlWv-mkvk", "name": "KeySvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__KeySvg.tsx"}, {"id": "cu6SQ9NY6", "name": "LinkSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LinkSvg.tsx"}, {"id": "AKtnLFlAP", "name": "LockUnlockedSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LockUnlockedSvg.tsx"}, {"id": "sFoHJtcBA", "name": "LockSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LockSvg.tsx"}, {"id": "FHyeP93rC", "name": "LogInSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LogInSvg.tsx"}, {"id": "bi4hoppVE", "name": "LogOutSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LogOutSvg.tsx"}, {"id": "QjjUVoVFa", "name": "MailSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__MailSvg.tsx"}, {"id": "iu9sNT3Az", "name": "MapSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__MapSvg.tsx"}, {"id": "HaphO4gm_", "name": "MaximizeSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__MaximizeSvg.tsx"}, {"id": "_PQ1TYFnr", "name": "MenuSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__MenuSvg.tsx"}, {"id": "P1Z3l66od", "name": "MessageSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__MessageSvg.tsx"}, {"id": "C4_9pu6k6", "name": "MicrophoneMuteSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__MicrophoneMuteSvg.tsx"}, {"id": "VxIZlTaqU", "name": "MicrophoneSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__MicrophoneSvg.tsx"}, {"id": "OHypr8EW_", "name": "MinimizeSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__MinimizeSvg.tsx"}, {"id": "JhmDN8TZW", "name": "MinusCircleSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__MinusCircleSvg.tsx"}, {"id": "WzWOZWGul", "name": "MinusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__MinusSvg.tsx"}, {"id": "t5gTKmWZV", "name": "Monitor2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Monitor2Svg.tsx"}, {"id": "hGmkLiM1N", "name": "MonitorSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__MonitorSvg.tsx"}, {"id": "mXO7tQl7N", "name": "MoonSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__MoonSvg.tsx"}, {"id": "wkwENb89K", "name": "MusicSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__MusicSvg.tsx"}, {"id": "SZsZaLEDU", "name": "PaintbucketSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PaintbucketSvg.tsx"}, {"id": "5PQTAhN2c", "name": "PauseSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PauseSvg.tsx"}, {"id": "vXWQKpRM4", "name": "PercentageSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PercentageSvg.tsx"}, {"id": "pCaGA4DmB", "name": "PhoneCallCrossSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PhoneCallCrossSvg.tsx"}, {"id": "5BAmFdOyz", "name": "PhoneCallForwardSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PhoneCallForwardSvg.tsx"}, {"id": "SbLYjZ2i9", "name": "PhoneCallHangUpSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PhoneCallHangUpSvg.tsx"}, {"id": "Qnrgf0Yh2", "name": "PhoneCallIncomingSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PhoneCallIncomingSvg.tsx"}, {"id": "vOH023v3s", "name": "PhoneCallOutgoingSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PhoneCallOutgoingSvg.tsx"}, {"id": "AC-OSkB9F", "name": "PhoneCallSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PhoneCallSvg.tsx"}, {"id": "Tg_iB1_iQ", "name": "PhoneSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PhoneSvg.tsx"}, {"id": "BzQDBpjA9", "name": "PhotoSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PhotoSvg.tsx"}, {"id": "yZPYUV2X6", "name": "PictureInPictureSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PictureInPictureSvg.tsx"}, {"id": "29Bravksq", "name": "PieChartSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PieChartSvg.tsx"}, {"id": "bvXFOdbyy", "name": "PillSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PillSvg.tsx"}, {"id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON>", "name": "PinTackSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PinTackSvg.tsx"}, {"id": "kRzGzOZSP", "name": "PinSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PinSvg.tsx"}, {"id": "s2iV8XTPW", "name": "PlugSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PlugSvg.tsx"}, {"id": "tPPI666-2", "name": "PlusCircleSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PlusCircleSvg.tsx"}, {"id": "sQKgd2GNr", "name": "PlusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PlusSvg.tsx"}, {"id": "DlHshmuIF", "name": "PrintSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PrintSvg.tsx"}, {"id": "VdFCXTF3a", "name": "ProjectorSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ProjectorSvg.tsx"}, {"id": "Avpc2MU6i", "name": "RedoSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__RedoSvg.tsx"}, {"id": "PEaq_S7gQ", "name": "RefreshSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__RefreshSvg.tsx"}, {"id": "iPiYqPkQ-", "name": "RewindSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__RewindSvg.tsx"}, {"id": "M5gthPJRM", "name": "RowsSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__RowsSvg.tsx"}, {"id": "R5DLz11OA", "name": "SearchSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SearchSvg.tsx"}, {"id": "h2npYh74m", "name": "SendSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SendSvg.tsx"}, {"id": "_7ZNiHQCp", "name": "ServerSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ServerSvg.tsx"}, {"id": "Y1oJwH9hP", "name": "SettingsSlidersSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SettingsSlidersSvg.tsx"}, {"id": "I2yCGDSun", "name": "SettingsSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SettingsSvg.tsx"}, {"id": "vRB2dtcKk", "name": "ShareSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ShareSvg.tsx"}, {"id": "uPS0_dcoT", "name": "ShieldSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ShieldSvg.tsx"}, {"id": "kerZJktUr", "name": "ShoppingBagSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ShoppingBagSvg.tsx"}, {"id": "Q28wP3efP", "name": "ShoppingBasketSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ShoppingBasketSvg.tsx"}, {"id": "ut9oeGX9y", "name": "ShoppingCartSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ShoppingCartSvg.tsx"}, {"id": "kW2-goi8Q", "name": "SkipBackSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SkipBackSvg.tsx"}, {"id": "wbQ5b5hHO", "name": "SkipForwardSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SkipForwardSvg.tsx"}, {"id": "E22RifHVw", "name": "SmartphoneSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SmartphoneSvg.tsx"}, {"id": "KiDNHpAl8", "name": "SpacingVerticalSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SpacingVerticalSvg.tsx"}, {"id": "BP3Oc27pL", "name": "SpeakerSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SpeakerSvg.tsx"}, {"id": "F2ZajSMKM", "name": "StampSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__StampSvg.tsx"}, {"id": "GjERAl_hQ", "name": "StarSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__StarSvg.tsx"}, {"id": "qjRQWD<PERSON>cj", "name": "StickerSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__StickerSvg.tsx"}, {"id": "gGAwI_Ti3", "name": "StopSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__StopSvg.tsx"}, {"id": "CODlKDD8A", "name": "StreamToTvSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__StreamToTvSvg.tsx"}, {"id": "wOO-yJ5BZ", "name": "SunSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SunSvg.tsx"}, {"id": "0lcquWMcz", "name": "TableColumnsSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TableColumnsSvg.tsx"}, {"id": "21GEg9hjb", "name": "TableRowsSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TableRowsSvg.tsx"}, {"id": "_sOrVFs-h", "name": "TabletSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TabletSvg.tsx"}, {"id": "yeZZVeB1o", "name": "TagSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TagSvg.tsx"}, {"id": "tyffEH_bL", "name": "TargetSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TargetSvg.tsx"}, {"id": "bf1z-88Om", "name": "TicketSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TicketSvg.tsx"}, {"id": "TMujQuuPA", "name": "TrashSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TrashSvg.tsx"}, {"id": "j4Xbcv4G8", "name": "TrendingDownSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TrendingDownSvg.tsx"}, {"id": "Y7Trphd5-", "name": "TrendingUpSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TrendingUpSvg.tsx"}, {"id": "khhYD7PhX", "name": "TruckSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TruckSvg.tsx"}, {"id": "c6W2e2EwV", "name": "UndoSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__UndoSvg.tsx"}, {"id": "PSNPDHlqT", "name": "UploadSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__UploadSvg.tsx"}, {"id": "_W3LZbaNQ", "name": "UserCheckSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__UserCheckSvg.tsx"}, {"id": "OEDQxtC2W", "name": "UserCrossSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__UserCrossSvg.tsx"}, {"id": "5bhUAm_MX", "name": "UserMinusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__UserMinusSvg.tsx"}, {"id": "P-Kzkylx-", "name": "UserPlusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__UserPlusSvg.tsx"}, {"id": "ejczgMIkT", "name": "UserSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__UserSvg.tsx"}, {"id": "iQM3kwX9l", "name": "UsersMinusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__UsersMinusSvg.tsx"}, {"id": "OqMJdWElK", "name": "UsersPlusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__UsersPlusSvg.tsx"}, {"id": "SQUWUgO0N", "name": "UsersSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__UsersSvg.tsx"}, {"id": "mSYwardqQ", "name": "VariantSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__VariantSvg.tsx"}, {"id": "OYOSXoWkv", "name": "VideoCameraOffSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__VideoCameraOffSvg.tsx"}, {"id": "PjWstPdXj", "name": "VideoCameraSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__VideoCameraSvg.tsx"}, {"id": "v02GLwlLw", "name": "VideoSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__VideoSvg.tsx"}, {"id": "FapuEoQGE", "name": "VirusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__VirusSvg.tsx"}, {"id": "pr51LHapW", "name": "WalletSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__WalletSvg.tsx"}, {"id": "S0L-xosWD", "name": "WarningTriangleSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__WarningTriangleSvg.tsx"}, {"id": "R4oCyyX3B", "name": "WifiNoConnectionSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__WifiNoConnectionSvg.tsx"}, {"id": "XWEW2nSa9", "name": "WifiSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__WifiSvg.tsx"}, {"id": "2ttDkj-bX", "name": "ZoomInSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ZoomInSvg.tsx"}, {"id": "pAYT2apGW", "name": "ZoomOutSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ZoomOutSvg.tsx"}, {"id": "_eXD9v-7V", "name": "LayoutTopSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LayoutTopSvg.tsx"}, {"id": "CCsLpDISa", "name": "LayoutBottomSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LayoutBottomSvg.tsx"}, {"id": "AobbKyfJE", "name": "BeforeSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BeforeSvg.tsx"}, {"id": "RYZCFr_yc", "name": "AfterSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AfterSvg.tsx"}, {"id": "Mc4gZv0qv", "name": "InputFieldSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__InputFieldSvg.tsx"}, {"id": "EYHEqxNq3", "name": "PasswordSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PasswordSvg.tsx"}, {"id": "Dmpla7yKC", "name": "Paintbrush2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Paintbrush2Svg.tsx"}, {"id": "J-uLxdDPm", "name": "PaintBucket3SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PaintBucket3Svg.tsx"}, {"id": "9uQwMAZhn", "name": "RepeatingSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__RepeatingSvg.tsx"}, {"id": "mlYvYWbm5", "name": "FontFamily2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FontFamily2Svg.tsx"}, {"id": "c2jaHrKCp", "name": "TextareaGripSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TextareaGripSvg.tsx"}, {"id": "IV2ptjA4E", "name": "Circle2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Circle2Svg.tsx"}, {"id": "aheV_vpN5", "name": "PresentationSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PresentationSvg.tsx"}, {"id": "nLt6_YN8G", "name": "Code3SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Code3Svg.tsx"}, {"id": "Q3kTyjJIF", "name": "LightBulbSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LightBulbSvg.tsx"}, {"id": "rtrSZZiat", "name": "EmojiPlusSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__EmojiPlusSvg.tsx"}, {"id": "xccUkUfeC", "name": "PollSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PollSvg.tsx"}, {"id": "Z3PPfy9jD", "name": "SkewSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SkewSvg.tsx"}, {"id": "bMnYdA0NL", "name": "Grid3SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Grid3Svg.tsx"}, {"id": "MvDAZQ7D2", "name": "AccessibilitySvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AccessibilitySvg.tsx"}, {"id": "o_Fm-pChC", "name": "AlertSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AlertSvg.tsx"}, {"id": "MhbLa_zgc", "name": "AZSortSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AZSortSvg.tsx"}, {"id": "oX1e3h5Pe", "name": "ContactsSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ContactsSvg.tsx"}, {"id": "6JRdKcLEO", "name": "StickyNoteSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__StickyNoteSvg.tsx"}, {"id": "pTs_X-5iH", "name": "AnimationEnterSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AnimationEnterSvg.tsx"}, {"id": "bOdef7dEt", "name": "AnimationExitSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AnimationExitSvg.tsx"}, {"id": "VXD02sWS4", "name": "ReceiptSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ReceiptSvg.tsx"}, {"id": "UaogAIns6", "name": "ChartIncreaseSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ChartIncreaseSvg.tsx"}, {"id": "Vi3Pj-xpn", "name": "ChartDecreaseSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ChartDecreaseSvg.tsx"}, {"id": "9TzsbO4iJ", "name": "GaugeSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__GaugeSvg.tsx"}, {"id": "_b2qO1u5R", "name": "PageFlipSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PageFlipSvg.tsx"}, {"id": "6a9D876gs", "name": "SearchFieldSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SearchFieldSvg.tsx"}, {"id": "HDDWeDccy", "name": "HookSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__HookSvg.tsx"}, {"id": "PQwkicZTa", "name": "QuestionMarkSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__QuestionMarkSvg.tsx"}, {"id": "7Eq-jdswQ", "name": "BringForwardSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BringForwardSvg.tsx"}, {"id": "nVh6uzgX0", "name": "SendToBackSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SendToBackSvg.tsx"}, {"id": "ekggWGfmC", "name": "SendToFrontSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SendToFrontSvg.tsx"}, {"id": "cK2b23x48", "name": "LayersSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LayersSvg.tsx"}, {"id": "IEvJ_DoZz", "name": "UnionMaskSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__UnionMaskSvg.tsx"}, {"id": "Wi2nNs5VS", "name": "Refresh3SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Refresh3Svg.tsx"}, {"id": "nDl-VATlI", "name": "OptionSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__OptionSvg.tsx"}, {"id": "OiZYXM7sl", "name": "AsteriskSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AsteriskSvg.tsx"}, {"id": "QH8WULa1w", "name": "AlignCenterHorizontalSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AlignCenterHorizontalSvg.tsx"}, {"id": "Uc_QK9-QS", "name": "AlignCenterVerticalSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__AlignCenterVerticalSvg.tsx"}, {"id": "Mdw4KAaiF", "name": "DoubleChatBubbleSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DoubleChatBubbleSvg.tsx"}, {"id": "PRGySniyl", "name": "PlanetSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__PlanetSvg.tsx"}, {"id": "ijmTgvSFI", "name": "_3DRotateSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon___3DRotateSvg.tsx"}, {"id": "feZBDhp1h", "name": "TagAddSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TagAddSvg.tsx"}, {"id": "QHejykUam", "name": "CardsSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CardsSvg.tsx"}, {"id": "VuGmsD9VW", "name": "TriangleCircleSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TriangleCircleSvg.tsx"}, {"id": "0PXKNP26_", "name": "MoneySvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__MoneySvg.tsx"}, {"id": "VWNqOtqHY", "name": "LayoutLeftSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LayoutLeftSvg.tsx"}, {"id": "yUxCQiSF7", "name": "MailboxSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__MailboxSvg.tsx"}, {"id": "3X2Kehrca", "name": "BackSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BackSvg.tsx"}, {"id": "M_ZLTltnm", "name": "FolderPersonSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__FolderPersonSvg.tsx"}, {"id": "e56dkUFjf", "name": "DirectionsSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DirectionsSvg.tsx"}, {"id": "Yr4v9MQYb", "name": "ChecklistSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ChecklistSvg.tsx"}, {"id": "cqNijmTIM", "name": "DeleteSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__DeleteSvg.tsx"}, {"id": "wADPSlOfQ", "name": "WriteNoteSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__WriteNoteSvg.tsx"}, {"id": "8KhvJLLs1", "name": "WidthSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__WidthSvg.tsx"}, {"id": "gHI1MB-D8", "name": "CoinsSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CoinsSvg.tsx"}, {"id": "MdSsqd6ac", "name": "LadderSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__LadderSvg.tsx"}, {"id": "xMHa47g0f", "name": "ConeSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ConeSvg.tsx"}, {"id": "f_6mLd5wY", "name": "Cone2SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Cone2Svg.tsx"}, {"id": "uIEp832Sf", "name": "SearchGlobeSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SearchGlobeSvg.tsx"}, {"id": "Alvt-lfC7", "name": "VerifiedSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__VerifiedSvg.tsx"}, {"id": "LP4wxEbH0", "name": "CpuIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Cpu.tsx"}, {"id": "BrfqNxcPy", "name": "NetworkSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__NetworkSvg.tsx"}, {"id": "_rqr1-id4", "name": "KioskSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__KioskSvg.tsx"}, {"id": "sUoXKfJbb", "name": "ReceiveMoneySvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__ReceiveMoneySvg.tsx"}, {"id": "lfrEaU4f5", "name": "SendMoneySvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SendMoneySvg.tsx"}, {"id": "VxYpXIyAe", "name": "BookOpenSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__BookOpenSvg.tsx"}, {"id": "CR2irjlzU", "name": "TreeSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TreeSvg.tsx"}, {"id": "fTw6e0M9Y", "name": "WidthStandardStretchIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__WidthStandardStretch.tsx"}, {"id": "Ys4UIc_oQ", "name": "WidthWideIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__WidthWide.tsx"}, {"id": "GJvut9H9_", "name": "WidthFullBleedIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__WidthFullBleed.tsx"}, {"id": "0tTYj7Hza", "name": "TableRowsPageSectionIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__TableRowsPageSection.tsx"}, {"id": "InkxX5RyM", "name": "Star2Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Star2.tsx"}, {"id": "opf4n115uVEp", "name": "Star3SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Star3Svg.tsx"}, {"id": "I-nHECEa6sim", "name": "SuperscriptIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Superscript.tsx"}, {"id": "AJjjov-8OFjW", "name": "SubscriptIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Subscript.tsx"}, {"id": "tzSml-ZqphbQ", "name": "SortSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__SortSvg.tsx"}, {"id": "WYUJK2ligiuj", "name": "Share3SvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__Share3Svg.tsx"}, {"id": "FOJsdThB5rU-", "name": "CheckedCheckboxSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__CheckedCheckboxSvg.tsx"}, {"id": "0dLCQ-imlG_u", "name": "EyeExclamationSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_icons/icons/PlasmicIcon__EyeExclamationSvg.tsx"}], "images": [], "codeComponents": [{"id": "D08d74dU_2", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "9e8nnBK0hk", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "projectApiToken": "el302sauzLq9TF1HjJbObNrtsIniSCy7av8KnADA1NPzWfOMMo6gfGVN8u3bDJ5r3aP2nZukJ5x4xSEaXig", "indirect": false, "globalContextsFilePath": "", "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "95xp9cYcv7HrNWpFWWhbcv", "projectName": "[PlasmicKit] Color Tokens", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css", "components": [], "icons": [], "images": [{"id": "pAcQ-9j-MkaY", "name": "image", "filePath": "wab/client/plasmic/plasmic_kit_q_4_color_tokens/images/image.svg"}], "codeComponents": [{"id": "JjeDtoC6QT", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "acaPZmYRco", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "projectApiToken": "X9RF4QUgEjfqAlBk6Nl7OhgXLqkhClEfwbGYrPzja9eIAqXGaTkltUmNaeX46ylhQdL0q5oCn6BzRE43wsMyg", "indirect": false, "globalContextsFilePath": "", "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "sZNLVjiaeNGLXmdsrbRQAX", "projectName": "Q4 Color Tokens - old -", "version": ">=0.0.0", "cssFilePath": "", "components": [], "icons": [], "images": [], "codeComponents": [], "projectApiToken": "HjRO5MWIelVWzYCrcP0jlzH9cNOf5UnF0MjHve2DtyCLcOToqxetdUjsP88WJLSva31eqvrqCnvkZ2VFnA", "indirect": true, "globalContextsFilePath": "", "splitsProviderFilePath": "", "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "oB885NJtg5rwT11s7yCnwW", "projectApiToken": "5lcVhjHv9wsRrHpnQBQWlAn06QvqOuSerEOoJxtGqSha2gELSqQeFSa7lPoBkMkCi6m3cWX3jfo2gNsmBRcg", "projectName": "[PlasmicKit] FindReferencesModal", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/plasmic_kit_find_references_modal/plasmic_plasmic_kit_find_references_modal.module.css", "components": [{"id": "YWyR9ESU0CU", "name": "FindReferencesModal", "type": "managed", "projectId": "oB885NJtg5rwT11s7yCnwW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_find_references_modal/PlasmicFindReferencesModal.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar/FindReferencesModal.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_find_references_modal/PlasmicFindReferencesModal.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "i-BferDjrAl", "name": "ReferenceItem", "type": "managed", "projectId": "oB885NJtg5rwT11s7yCnwW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_find_references_modal/PlasmicReferenceItem.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar/ReferenceItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_find_references_modal/PlasmicReferenceItem.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [{"id": "QWzHJOf09S", "name": "ChevronBottomIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_find_references_modal/icons/PlasmicIcon__ChevronBottom.tsx"}, {"id": "uFvmqTmkO9", "name": "ChevronRightIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_find_references_modal/icons/PlasmicIcon__ChevronRight.tsx"}], "images": [], "indirect": false, "codeComponents": [], "globalContextsFilePath": "", "splitsProviderFilePath": "", "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "uLddf5fC1aQbF7tmV1WQ1a", "projectApiToken": "MOnS4nTlfCGbQrfeuzmVjPr20L8bl6O5uUqb8otTSMq3jxlWyK1oAyJJmxT36y0qilcw2R3fZT9AVFqjmQgg", "projectName": "[PlasmicKit] Rich Text Toolbar", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/plasmic_kit_rich_text_toolbar/plasmic_plasmic_kit_rich_text_toolbar.module.css", "components": [{"id": "GzEy-XDJM8", "name": "RichTextToolbar", "type": "managed", "projectId": "uLddf5fC1aQbF7tmV1WQ1a", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_rich_text_toolbar/PlasmicRichTextToolbar.tsx", "importSpec": {"modulePath": "wab/client/components/canvas/RichText/RichTextToolbar.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_rich_text_toolbar/PlasmicRichTextToolbar.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "kVTvUj4Wyf", "name": "RichTextToolbarDivider", "type": "managed", "projectId": "uLddf5fC1aQbF7tmV1WQ1a", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_rich_text_toolbar/PlasmicRichTextToolbarDivider.tsx", "importSpec": {"modulePath": "wab/client/components/canvas/RichText/RichTextToolbarDivider.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_rich_text_toolbar/PlasmicRichTextToolbarDivider.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [], "images": [], "indirect": false, "codeComponents": [{"id": "Y8tOttpCjtka", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "gqSex0QkvFz7", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "globalContextsFilePath": "", "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "ieacQ3Z46z4gwo1FnaB5vY", "projectApiToken": "qBgq9DX7J1VoEfo9CzSl3dDgYUSsmtrG7HoDGCN4fPYKoMwgS7YPEtZRDLuLjinH2gQ78CzOq2Q1gw7bdSA", "projectName": "[PlasmicKit] CMS", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/plasmic_kit_cms/plasmic_plasmic_kit_cms.module.css", "components": [{"id": "FxC1c7NZtR", "name": "CmsTopBar", "type": "managed", "projectId": "ieacQ3Z46z4gwo1FnaB5vY", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsTopBar.tsx", "importSpec": {"modulePath": "wab/client/components/cms/CmsTopBar.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsTopBar.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "kX5_DA_mZR", "name": "CmsLeftTabs", "type": "managed", "projectId": "ieacQ3Z46z4gwo1FnaB5vY", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsLeftTabs.tsx", "importSpec": {"modulePath": "wab/client/components/cms/CmsLeftTabs.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsLeftTabs.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "fC6EeUMrpE", "name": "CmsContentPage", "type": "managed", "projectId": "ieacQ3Z46z4gwo1FnaB5vY", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsContentPage.tsx", "importSpec": {"modulePath": "wab/client/components/cms/CmsContentPage.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsContentPage.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "M3aa84scyXT", "name": "CmsModelsList", "type": "managed", "projectId": "ieacQ3Z46z4gwo1FnaB5vY", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsModelsList.tsx", "importSpec": {"modulePath": "wab/client/components/cms/CmsModelsList.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsModelsList.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "FpZFUfiTA6", "name": "CmsModelItem", "type": "managed", "projectId": "ieacQ3Z46z4gwo1FnaB5vY", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsModelItem.tsx", "importSpec": {"modulePath": "wab/client/components/cms/CmsModelItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsModelItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "girCdMST6R", "name": "CmsEntryItem", "type": "managed", "projectId": "ieacQ3Z46z4gwo1FnaB5vY", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsEntryItem.tsx", "importSpec": {"modulePath": "wab/client/components/cms/CmsEntryItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsEntryItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "k2vc2stl18", "name": "CmsEntriesList", "type": "managed", "projectId": "ieacQ3Z46z4gwo1FnaB5vY", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsEntriesList.tsx", "importSpec": {"modulePath": "wab/client/components/cms/CmsEntriesList.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsEntriesList.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "9vM3ZFGR4eV", "name": "CmsEntryDetails", "type": "managed", "projectId": "ieacQ3Z46z4gwo1FnaB5vY", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsEntryDetails.tsx", "importSpec": {"modulePath": "wab/client/components/cms/CmsEntryDetails.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsEntryDetails.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "FiuFB1wXjp", "name": "CmsRoot", "type": "managed", "projectId": "ieacQ3Z46z4gwo1FnaB5vY", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsRoot.tsx", "importSpec": {"modulePath": "wab/client/components/cms/CmsRoot.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsRoot.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "y1ZiXuS8BD", "name": "CmsSchemaPage", "type": "managed", "projectId": "ieacQ3Z46z4gwo1FnaB5vY", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsSchemaPage.tsx", "importSpec": {"modulePath": "wab/client/components/cms/CmsSchemaPage.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsSchemaPage.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "pLQf-lY112u", "name": "CmsModelDetails", "type": "managed", "projectId": "ieacQ3Z46z4gwo1FnaB5vY", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsModelDetails.tsx", "importSpec": {"modulePath": "wab/client/components/cms/CmsModelDetails.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsModelDetails.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "Tz8Unep1qu", "name": "CmsModelContent", "type": "managed", "projectId": "ieacQ3Z46z4gwo1FnaB5vY", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsModelContent.tsx", "importSpec": {"modulePath": "wab/client/components/cms/CmsModelContent.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsModelContent.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "a5viGetjMi", "name": "CmsSettingsPage", "type": "managed", "projectId": "ieacQ3Z46z4gwo1FnaB5vY", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsSettingsPage.tsx", "importSpec": {"modulePath": "wab/client/components/cms/CmsSettingsPage.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicCmsSettingsPage.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [{"id": "Z52l5ocdyYWLv", "name": "IconIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_cms/icons/PlasmicIcon__Icon.tsx"}, {"id": "KdAPT7ecvKBXe", "name": "Icon2Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_cms/icons/PlasmicIcon__Icon2.tsx"}, {"id": "G_dU2TC5Bu", "name": "Icon3Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_cms/icons/PlasmicIcon__Icon3.tsx"}], "images": [{"id": "vvVTexRhlGWhm", "name": "image.png", "filePath": "wab/client/plasmic/plasmic_kit_cms/images/imagePng.jpg"}], "indirect": false, "globalContextsFilePath": "", "codeComponents": [{"id": "x-e9Zyx57R", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "CQq5EcpI2T", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "gtUDvxG6cmBbSzqLikNzoP", "projectApiToken": "8sTDQ90WrZpZSIjf7mRSepoprKPxl0YutFZkjvy94QbzZcYi0qbtCsHFiumDNi6kaCNdl8gUmMjKDsdUyDhg", "projectName": "[PlasmicKit] Optimize", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/plasmic_kit_optimize/plasmic_plasmic_kit_optimize.module.css", "components": [{"id": "nlaW16gbH_n", "name": "ExperimentModal", "type": "managed", "projectId": "gtUDvxG6cmBbSzqLikNzoP", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicExperimentModal.tsx", "importSpec": {"modulePath": "wab/client/components/splits/ExperimentModal.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicExperimentModal.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "HtHxjHknmj_", "name": "ExperimentPanel", "type": "managed", "projectId": "gtUDvxG6cmBbSzqLikNzoP", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicExperimentPanel.tsx", "importSpec": {"modulePath": "wab/client/components/splits/ExperimentPanel.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicExperimentPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "ohFfsSdDUeCq", "name": "SplitsContent", "type": "managed", "projectId": "gtUDvxG6cmBbSzqLikNzoP", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicSplitsContent.tsx", "importSpec": {"modulePath": "wab/client/components/splits/SplitsContent.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicSplitsContent.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "fjdDZovo7S", "name": "ExperimentEvent", "type": "managed", "projectId": "gtUDvxG6cmBbSzqLikNzoP", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicExperimentEvent.tsx", "importSpec": {"modulePath": "wab/client/components/splits/ExperimentEvent.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicExperimentEvent.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "74OdgMxR-T", "name": "ExperimentRow", "type": "managed", "projectId": "gtUDvxG6cmBbSzqLikNzoP", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicExperimentRow.tsx", "importSpec": {"modulePath": "wab/client/components/splits/ExperimentRow.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicExperimentRow.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "APlN8dajrS9", "name": "ExperimentEternalIds", "type": "managed", "projectId": "gtUDvxG6cmBbSzqLikNzoP", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicExperimentEternalIds.tsx", "importSpec": {"modulePath": "wab/client/components/splits/ExperimentEternalIds.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicExperimentEternalIds.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "SRI244k7gOA", "name": "ExperimentEvents", "type": "managed", "projectId": "gtUDvxG6cmBbSzqLikNzoP", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicExperimentEvents.tsx", "importSpec": {"modulePath": "wab/client/components/splits/ExperimentEvents.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicExperimentEvents.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "5hDgjGS3IR", "name": "SegmentAddField", "type": "managed", "projectId": "gtUDvxG6cmBbSzqLikNzoP", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicSegmentAddField.tsx", "importSpec": {"modulePath": "wab/client/components/splits/SegmentAddField.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicSegmentAddField.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "KCou38FwxL", "name": "ScheduleControls", "type": "managed", "projectId": "gtUDvxG6cmBbSzqLikNzoP", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicScheduleControls.tsx", "importSpec": {"modulePath": "wab/client/components/splits/ScheduleControls.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicScheduleControls.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "60hnxzzKks", "name": "SegmentControls", "type": "managed", "projectId": "gtUDvxG6cmBbSzqLikNzoP", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicSegmentControls.tsx", "importSpec": {"modulePath": "wab/client/components/splits/SegmentControls.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicSegmentControls.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "b4qqi4IFfg", "name": "ExperimentToggle", "type": "managed", "projectId": "gtUDvxG6cmBbSzqLikNzoP", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicExperimentToggle.tsx", "importSpec": {"modulePath": "wab/client/components/splits/ExperimentToggle.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicExperimentToggle.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "T79mlJEJy9", "name": "ExperimentCanvasButton", "type": "managed", "projectId": "gtUDvxG6cmBbSzqLikNzoP", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicExperimentCanvasButton.tsx", "importSpec": {"modulePath": "wab/client/components/splits/ExperimentCanvasButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicExperimentCanvasButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "jARgXSx3Oz", "name": "ExperimentRows", "type": "managed", "projectId": "gtUDvxG6cmBbSzqLikNzoP", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicExperimentRows.tsx", "importSpec": {"modulePath": "wab/client/components/splits/ExperimentRows.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicExperimentRows.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "QUkOz1f5TI", "name": "EditOverrideToggleButton", "type": "managed", "projectId": "gtUDvxG6cmBbSzqLikNzoP", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicEditOverrideToggleButton.tsx", "importSpec": {"modulePath": "wab/client/components/splits/EditOverrideToggleButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicEditOverrideToggleButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "evzY7iORow", "name": "SegmentModal", "type": "managed", "projectId": "gtUDvxG6cmBbSzqLikNzoP", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicSegmentModal.tsx", "importSpec": {"modulePath": "wab/client/components/splits/SegmentModal.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicSegmentModal.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [{"id": "6dMbk6C2QI_L_", "name": "IconIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_optimize/icons/PlasmicIcon__Icon.tsx"}, {"id": "PAf6ri2Zfos6H", "name": "Icon2Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_optimize/icons/PlasmicIcon__Icon2.tsx"}, {"id": "t_IhkVbHjnz", "name": "UnionIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_optimize/icons/PlasmicIcon__Union.tsx"}, {"id": "1fOBAQgR_YC", "name": "SearchsvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_optimize/icons/PlasmicIcon__Searchsvg.tsx"}, {"id": "WwUBV0GQ7FQ", "name": "ChecksvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_optimize/icons/PlasmicIcon__Checksvg.tsx"}, {"id": "X6hf2DFJIGGQ", "name": "ChevronDownsvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_optimize/icons/PlasmicIcon__ChevronDownsvg.tsx"}, {"id": "HgcWQamEyGiJ", "name": "ChevronUpsvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_optimize/icons/PlasmicIcon__ChevronUpsvg.tsx"}, {"id": "XoVue8d2QUwz", "name": "Icon3Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_optimize/icons/PlasmicIcon__Icon3.tsx"}, {"id": "AKxTHEA6Rx", "name": "Icon4Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_optimize/icons/PlasmicIcon__Icon4.tsx"}], "images": [{"id": "Q9FZkjIRMlhO_", "name": "image.png", "filePath": "wab/client/plasmic/plasmic_kit_optimize/images/imagepng.jpeg"}], "indirect": false, "globalContextsFilePath": "", "codeComponents": [], "splitsProviderFilePath": "", "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "w2GXN278dkQ2gQTVQnPehW", "projectApiToken": "XlpYxXeJBssbtNoLCawek4y1zywRmLqnhwf9ryxOaiQ3L99h4nH3LpZISJwqHOCvZIxcvX1CWe2QVnUrfg", "projectName": "[PlasmicKit] Data Binding", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/plasmic_kit_data_binding/plasmic_plasmic_kit_data_binding.module.css", "components": [{"id": "cbEBf9RLgx", "name": "DataPicker", "type": "managed", "projectId": "w2GXN278dkQ2gQTVQnPehW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicDataPicker.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/DataBinding/DataPicker.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicDataPicker.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "xmF37LmWYE", "name": "DataPickerColumn", "type": "managed", "projectId": "w2GXN278dkQ2gQTVQnPehW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicDataPickerColumn.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/DataBinding/DataPickerColumn.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicDataPickerColumn.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "fa3uzsyXr0", "name": "DataPickerColumnItem", "type": "managed", "projectId": "w2GXN278dkQ2gQTVQnPehW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicDataPickerColumnItem.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/DataBinding/DataPickerColumnItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicDataPickerColumnItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "PZbWryjVVD", "name": "DataPickerSelectedItem", "type": "managed", "projectId": "w2GXN278dkQ2gQTVQnPehW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicDataPickerSelectedItem.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/DataBinding/DataPickerSelectedItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicDataPickerSelectedItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "EzMpPqn_cI", "name": "DataPickerGlobalSearchField", "type": "managed", "projectId": "w2GXN278dkQ2gQTVQnPehW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicDataPickerGlobalSearchField.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/DataBinding/DataPickerGlobalSearchField.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicDataPickerGlobalSearchField.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "nD2Ql_rEk6", "name": "DataPickerGlobalSearchResultsItem", "type": "managed", "projectId": "w2GXN278dkQ2gQTVQnPehW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicDataPickerGlobalSearchResultsItem.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/DataBinding/DataPickerGlobalSearchResultsItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicDataPickerGlobalSearchResultsItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "gWylXtol8Lf", "name": "DataPickerValueTypeIcon", "type": "managed", "projectId": "w2GXN278dkQ2gQTVQnPehW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicDataPickerValueTypeIcon.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/DataBinding/DataPickerValueTypeIcon.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicDataPickerValueTypeIcon.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "GDvL7J9P5V4", "name": "DataPickerGlobalSearchResults", "type": "managed", "projectId": "w2GXN278dkQ2gQTVQnPehW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicDataPickerGlobalSearchResults.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/DataBinding/DataPickerGlobalSearchResults.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicDataPickerGlobalSearchResults.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "yN9xaawDlts", "name": "DataPickerCodeEditorLayout", "type": "managed", "projectId": "w2GXN278dkQ2gQTVQnPehW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicDataPickerCodeEditorLayout.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/DataBinding/DataPickerCodeEditorLayout.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicDataPickerCodeEditorLayout.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "VDe4OfA0wv", "name": "WrapRepeatedElementModal", "type": "managed", "projectId": "w2GXN278dkQ2gQTVQnPehW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicWrapRepeatedElementModal.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/DataBinding/WrapRepeatedElementModal.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicWrapRepeatedElementModal.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "QcDtYmEqee", "name": "WrapRepeatedElementOption", "type": "managed", "projectId": "w2GXN278dkQ2gQTVQnPehW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicWrapRepeatedElementOption.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/DataBinding/WrapRepeatedElementOption.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicWrapRepeatedElementOption.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "SdMPiPjcB9G", "name": "CopilotCodePrompt", "type": "managed", "projectId": "w2GXN278dkQ2gQTVQnPehW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicCopilotCodePrompt.tsx", "importSpec": {"modulePath": "wab/client/components/copilot/CopilotCodePrompt.tsx", "exportName": "CopilotCodePrompt"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicCopilotCodePrompt.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "CdMYaSGMjG", "name": "CopilotMsg", "type": "managed", "projectId": "w2GXN278dkQ2gQTVQnPehW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicCopilotMsg.tsx", "importSpec": {"modulePath": "wab/client/components/CopilotMsg.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicCopilotMsg.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "-LDNJojbDZD", "name": "CopilotLikeDislike", "type": "managed", "projectId": "w2GXN278dkQ2gQTVQnPehW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicCopilotLikeDislike.tsx", "importSpec": {"modulePath": "wab/client/components/CopilotLikeDislike.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicCopilotLikeDislike.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "-zGA-erYhCmv", "name": "CopilotPromptDialog", "type": "managed", "projectId": "w2GXN278dkQ2gQTVQnPehW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicCopilotPromptDialog.tsx", "importSpec": {"modulePath": "wab/client/components/copilot/CopilotPromptDialog.tsx", "exportName": "CopilotPromptDialog"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicCopilotPromptDialog.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "MmbJYtYh-0Eh", "name": "CopilotPromptImage", "type": "managed", "projectId": "w2GXN278dkQ2gQTVQnPehW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicCopilotPromptImage.tsx", "importSpec": {"modulePath": "wab/client/components/copilot/CopilotPromptImage.tsx", "exportName": "CopilotPromptImage"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicCopilotPromptImage.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "pnV7KLVDUyoz", "name": "CopilotPromptInput", "type": "managed", "projectId": "w2GXN278dkQ2gQTVQnPehW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicCopilotPromptInput.tsx", "importSpec": {"modulePath": "wab/client/components/copilot/CopilotPromptInput.tsx", "exportName": "CopilotPromptInput"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_data_binding/PlasmicCopilotPromptInput.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [{"id": "aeyQLybWj1P", "name": "CheckSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/icons/PlasmicIcon__CheckSvg.tsx"}, {"id": "T7O74SQvscm", "name": "IconIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/icons/PlasmicIcon__Icon.tsx"}, {"id": "udef47udLQ", "name": "Icon2Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/icons/PlasmicIcon__Icon2.tsx"}, {"id": "EfDOV4MDLj", "name": "Icon3Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/icons/PlasmicIcon__Icon3.tsx"}, {"id": "ZTW8iKylgI", "name": "Icon4Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/icons/PlasmicIcon__Icon4.tsx"}, {"id": "mPucsZbX6V", "name": "Icon5Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_data_binding/icons/PlasmicIcon__Icon5.tsx"}], "images": [{"id": "UYmVmRYjy", "name": "image", "filePath": "wab/client/plasmic/plasmic_kit_data_binding/images/image.png"}], "indirect": false, "globalContextsFilePath": "", "codeComponents": [{"id": "IKos7NjpjU", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "T4W9danrZ6", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "customFunctions": [], "splitsProviderFilePath": "", "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "28Ba3BDvRe7EiWdfFNnLB6", "projectApiToken": "DWg4EVpu2l6n6fAdMDCvE22FFoWH1NK3ZmqmXnzezysh5ZWic03cixuobavnSG7W2O29VnLlgnyQp1TAijQ", "projectName": "antd", "version": "2.0.1", "cssFilePath": "wab/client/plasmic/antd/plasmic_antd.module.css", "components": [], "icons": [], "images": [], "indirect": true, "globalContextsFilePath": "", "codeComponents": [{"id": "kv6Mk_JNYw", "name": "<PERSON><PERSON>", "componentImportPath": "antd"}, {"id": "iBC45mjTXv", "name": "Slide<PERSON>", "componentImportPath": "@plasmicpkgs/antd"}, {"id": "tRueht1jub", "name": "Switch", "componentImportPath": "antd"}, {"id": "YRr0RPAdEj", "name": "Option", "componentImportPath": "rc-select"}, {"id": "Ay1H491feg", "name": "OptGroup", "componentImportPath": "rc-select"}, {"id": "2YpGIhkzkpc", "name": "Select", "componentImportPath": "antd"}, {"id": "yegZ392NLoV", "name": "CollapsePanel", "componentImportPath": "antd/lib/collapse/CollapsePanel"}, {"id": "F10gpQ1ZhXY", "name": "Collapse", "componentImportPath": "antd/lib/collapse/Collapse"}, {"id": "SAEWraOT2YV", "name": "Checkbox", "componentImportPath": "antd/lib/checkbox/Checkbox"}, {"id": "m5zFS-kMOL-", "name": "CheckboxGroup", "componentImportPath": "antd/lib/checkbox/Group"}, {"id": "6DZuSUEKjFh", "name": "MenuDivider", "componentImportPath": "antd/lib/menu/MenuDivider"}, {"id": "HZ6EmQgIQJr", "name": "MenuItem", "componentImportPath": "antd/lib/menu/MenuItem"}, {"id": "LGE6ExNG8fZ", "name": "ItemGroup", "componentImportPath": "rc-menu"}, {"id": "DSwQm8gUYob", "name": "SubMenu", "componentImportPath": "antd/lib/menu/SubMenu"}, {"id": "Iz_hLKpHZDg", "name": "<PERSON><PERSON>", "componentImportPath": "antd/lib/menu/index"}, {"id": "_C8wcdH3BDp", "name": "Carousel", "componentImportPath": "antd"}, {"id": "tO9wrxr8Nbb", "name": "Input", "componentImportPath": "antd"}, {"id": "ZJIPHLoTjkN", "name": "TextArea", "componentImportPath": "antd/lib/input/TextArea"}, {"id": "zDcn8vsMldu", "name": "Search", "componentImportPath": "antd/lib/input/Search"}, {"id": "fTDg0FM1tnD", "name": "Password", "componentImportPath": "antd/lib/input/Password"}, {"id": "uZueUXuqePq", "name": "InputGroup", "componentImportPath": "antd/lib/input/Group"}, {"id": "cv59X4q0WK", "name": "Dropdown", "componentImportPath": "@plasmicpkgs/antd"}, {"id": "cHUYQj2aKK", "name": "DropdownButton", "componentImportPath": "antd/lib/dropdown/dropdown-button"}, {"id": "p3HNI5RglT", "name": "TabPane", "componentImportPath": "rc-tabs"}, {"id": "Hb-DVgvuk3", "name": "Tabs", "componentImportPath": "@plasmicpkgs/antd"}], "splitsProviderFilePath": "", "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "783YKJdyRRPxZbx3qiNi5Q", "projectApiToken": "Xkd3WgqVLuz2SfYds7zWHUDVp2uZe3iqyOzL4mxiPd6ybGkT0kmqRfmPtrcGRwA4yRp3NhCOJR8dlTRA", "projectName": "[PlasmicKit] Component Props Section", "version": "latest", "cssFilePath": "wab/client/plasmic/plasmic_kit_component_props_section/plasmic_plasmic_kit_component_props_section.module.css", "components": [{"id": "-ZWJykIq5V-3F", "name": "CardPickerItem", "type": "managed", "projectId": "783YKJdyRRPxZbx3qiNi5Q", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_component_props_section/PlasmicCardPickerItem.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/ComponentProps/CardPickerItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_component_props_section/PlasmicCardPickerItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "6ODOBecfUs5", "name": "CardPickerModal", "type": "managed", "projectId": "783YKJdyRRPxZbx3qiNi5Q", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_component_props_section/PlasmicCardPickerModal.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/ComponentProps/CardPickerModal.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_component_props_section/PlasmicCardPickerModal.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [], "images": [], "indirect": false, "globalContextsFilePath": "", "codeComponents": [], "splitsProviderFilePath": "", "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "cQnF1HuwK97HkvkrC6uRk2", "projectApiToken": "UXjed1SyPqmEOIeSMX8ea14kmySD7SS6Tr1lTiNAEc6tLegdumUOB3Sb2HeWW8UCckLqsuVOcGhBMqnKzw", "projectName": "[PlasmicKit] Analytics", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/plasmic_kit_analytics/plasmic_plasmic_kit_analytics.module.css", "components": [{"id": "RrG72JEyZOXn", "name": "TeamAnalytics", "type": "managed", "projectId": "cQnF1HuwK97HkvkrC6uRk2", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_analytics/PlasmicTeamAnalytics.tsx", "importSpec": {"modulePath": "wab/client/components/analytics/TeamAnalytics.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_analytics/PlasmicTeamAnalytics.module.css", "scheme": "blackbox", "componentType": "page"}, {"id": "U5oM6fe0OlY", "name": "TeamFilters", "type": "managed", "projectId": "cQnF1HuwK97HkvkrC6uRk2", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_analytics/PlasmicTeamFilters.tsx", "importSpec": {"modulePath": "wab/client/components/analytics/TeamFilters.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_analytics/PlasmicTeamFilters.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "Dza4MqGNx4p", "name": "DataFilters", "type": "managed", "projectId": "cQnF1HuwK97HkvkrC6uRk2", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_analytics/PlasmicDataFilters.tsx", "importSpec": {"modulePath": "wab/client/components/analytics/DataFilters.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_analytics/PlasmicDataFilters.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "vSQc3cNg5Q", "name": "ChartView", "type": "managed", "projectId": "cQnF1HuwK97HkvkrC6uRk2", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_analytics/PlasmicChartView.tsx", "importSpec": {"modulePath": "wab/client/components/analytics/ChartView.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_analytics/PlasmicChartView.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "lpGYGncEBV", "name": "AnalyticsHeader", "type": "managed", "projectId": "cQnF1HuwK97HkvkrC6uRk2", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_analytics/PlasmicAnalyticsHeader.tsx", "importSpec": {"modulePath": "wab/client/components/analytics/AnalyticsHeader.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_analytics/PlasmicAnalyticsHeader.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "bQ74QBVIbHI", "name": "LabeledSelect", "type": "managed", "projectId": "cQnF1HuwK97HkvkrC6uRk2", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_analytics/PlasmicLabeledSelect.tsx", "importSpec": {"modulePath": "wab/client/components/analytics/LabeledSelect.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_analytics/PlasmicLabeledSelect.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "0bODOMCtGi", "name": "OptimizationsSelect", "type": "managed", "projectId": "cQnF1HuwK97HkvkrC6uRk2", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_analytics/PlasmicOptimizationsSelect.tsx", "importSpec": {"modulePath": "wab/client/components/analytics/OptimizationsSelect.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_analytics/PlasmicOptimizationsSelect.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "yvny0cDy_e", "name": "OptimizationOption", "type": "managed", "projectId": "cQnF1HuwK97HkvkrC6uRk2", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_analytics/PlasmicOptimizationOption.tsx", "importSpec": {"modulePath": "wab/client/components/analytics/OptimizationOption.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_analytics/PlasmicOptimizationOption.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "Jt0CZzY1xy", "name": "PeriodPicker", "type": "managed", "projectId": "cQnF1HuwK97HkvkrC6uRk2", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_analytics/PlasmicPeriodPicker.tsx", "importSpec": {"modulePath": "wab/client/components/analytics/PeriodPicker.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_analytics/PlasmicPeriodPicker.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "wQH36LoqQL", "name": "SharePageModal", "type": "managed", "projectId": "cQnF1HuwK97HkvkrC6uRk2", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_analytics/PlasmicSharePageModal.tsx", "importSpec": {"modulePath": "wab/client/components/analytics/SharePageModal.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_analytics/PlasmicSharePageModal.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [], "images": [], "indirect": false, "globalContextsFilePath": "", "codeComponents": [{"id": "p-n5zeFGwk", "name": "PlasmicHead", "componentImportPath": "@plasmicapp/react-web"}], "splitsProviderFilePath": "", "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "p8FkKgCnyuat1kHSEYAKfW", "projectApiToken": "9DoCnq5cNw0FQjw61dZTcP1Jo20kdDCGkpGGTqjVxjl2yyS7ZrWDHrnED1ajDZZUDtRjLaCbuWJ5DXN2Xw", "projectName": "[PlasmicKit] Merge Flow", "version": "latest", "cssFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/plasmic_plasmic_kit_merge_flow.module.css", "components": [{"id": "A4VINgKjc8", "name": "MergeFlow", "type": "managed", "projectId": "p8FkKgCnyuat1kHSEYAKfW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/PlasmicMergeFlow.tsx", "importSpec": {"modulePath": "wab/client/components/merge/MergeFlow.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/PlasmicMergeFlow.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "LCAZOUPfDDB", "name": "ToggleButtonSwitch", "type": "managed", "projectId": "p8FkKgCnyuat1kHSEYAKfW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/PlasmicToggleButtonSwitch.tsx", "importSpec": {"modulePath": "wab/client/components/merge/ToggleButtonSwitch.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/PlasmicToggleButtonSwitch.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "VgvN9iOqwZ", "name": "LineItem", "type": "managed", "projectId": "p8FkKgCnyuat1kHSEYAKfW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/PlasmicLineItem.tsx", "importSpec": {"modulePath": "wab/client/components/merge/LineItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/PlasmicLineItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "RM-Ya_c-mv", "name": "Conflict", "type": "managed", "projectId": "p8FkKgCnyuat1kHSEYAKfW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/PlasmicConflict.tsx", "importSpec": {"modulePath": "wab/client/components/merge/Conflict.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/PlasmicConflict.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "AJepyKzS-T-", "name": "Switch", "type": "managed", "projectId": "p8FkKgCnyuat1kHSEYAKfW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/PlasmicSwitch.tsx", "importSpec": {"modulePath": "wab/client/components/merge/Switch.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/PlasmicSwitch.module.css", "scheme": "blackbox", "componentType": "component", "plumeType": "switch"}, {"id": "o4Oidp6CzFL", "name": "Diffs", "type": "managed", "projectId": "p8FkKgCnyuat1kHSEYAKfW", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/PlasmicDiffs.tsx", "importSpec": {"modulePath": "wab/client/components/merge/Diffs.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/PlasmicDiffs.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [{"id": "9_qXsh2_0", "name": "FramePlusIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/icons/PlasmicIcon__FramePlus.tsx"}, {"id": "eV4_yyuiy3", "name": "ChevronDownIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/icons/PlasmicIcon__ChevronDown.tsx"}, {"id": "YCOFZmA9Gr", "name": "CloseIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/icons/PlasmicIcon__Close.tsx"}, {"id": "b32FQsRIZF", "name": "ArrowRightIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/icons/PlasmicIcon__ArrowRight.tsx"}, {"id": "eHz6SkjEXN", "name": "HomeIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/icons/PlasmicIcon__Home.tsx"}, {"id": "TopFn49DCw", "name": "SquareCheckFilledIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/icons/PlasmicIcon__SquareCheckFilled.tsx"}, {"id": "e1jr2JBmRV", "name": "SquareIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/icons/PlasmicIcon__Square.tsx"}, {"id": "nO4zRkdymv", "name": "DiamondsIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/icons/PlasmicIcon__Diamonds.tsx"}, {"id": "GwYW58XSMS", "name": "ComponentIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/icons/PlasmicIcon__Component.tsx"}, {"id": "gTDWgpkqvL", "name": "ChevronDown2Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/icons/PlasmicIcon__ChevronDown2.tsx"}], "images": [{"id": "v2eVPM-oDO", "name": "tree", "filePath": "wab/client/plasmic/plasmic_kit_merge_flow/images/tree.svg"}, {"id": "CnRm3QPPqQ", "name": "plus", "filePath": "wab/client/plasmic/plasmic_kit_merge_flow/images/plus.svg"}, {"id": "RBjf9GCAF1", "name": "Frame 1849", "filePath": "wab/client/plasmic/plasmic_kit_merge_flow/images/frame1849.svg"}], "indirect": false, "globalContextsFilePath": "", "codeComponents": [{"id": "JWl-VNhsLT", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "vvEVYvjxwz", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "caTPwKxj5ZrD9LQ7DMdK4Z", "projectApiToken": "vXGGQpdi9fhcncabBdg8DcMRUbkz0iRW6HS1jzQufQR6QH0MasPwr9SX7ap4SsK3xXwwXmzZtglWGe0Rm2nw", "projectName": "plasmic-basic-components", "version": "3.36.0", "cssFilePath": "wab/client/plasmic/plasmic_basic_components/plasmic_plasmic_basic_components.module.css", "components": [], "icons": [], "images": [], "indirect": true, "globalContextsFilePath": "", "codeComponents": [{"id": "CMDBvOhaI4s", "name": "<PERSON><PERSON><PERSON>", "displayName": "hostless-iframe", "componentImportPath": "@plasmicpkgs/plasmic-basic-components"}, {"id": "RhitNJW5Zu-", "name": "Video", "displayName": "hostless-html-video", "componentImportPath": "@plasmicpkgs/plasmic-basic-components"}, {"id": "PKldDYkH42", "name": "Embed", "displayName": "hostless-embed", "componentImportPath": "@plasmicpkgs/plasmic-basic-components"}, {"id": "D4RbnlpRXg3", "name": "DataProvider", "displayName": "hostless-data-provider", "componentImportPath": "@plasmicpkgs/plasmic-basic-components"}, {"id": "zlBHsvkFlN", "name": "Condition<PERSON><PERSON>", "displayName": "hostless-condition-guard", "componentImportPath": "@plasmicpkgs/plasmic-basic-components"}, {"id": "K-mWGqrHefEp", "name": "SideEffect", "displayName": "hostless-side-effect", "componentImportPath": "@plasmicpkgs/plasmic-basic-components"}, {"id": "SpkzmXJqg27p", "name": "Timer", "displayName": "hostless-timer", "componentImportPath": "@plasmicpkgs/plasmic-basic-components"}], "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "eyjDfHaWPk4awNJAqhg4Cb", "projectApiToken": "TzIvXznTGt1M9jjItewokGRoDwVGczIsMTq7lnEHHAg99peoVxQlaLAaddyt6VZdF6TpmZmi6jKDRTo4g", "projectName": "[PlasmicKit] Multiplayer UI", "version": "latest", "cssFilePath": "wab/client/plasmic/plasmic_kit_multiplayer_ui/plasmic_plasmic_kit_multiplayer_ui.module.css", "components": [{"id": "MgtZV0FX0Q", "name": "MultiplayerFollowingBorder", "type": "managed", "projectId": "eyjDfHaWPk4awNJAqhg4Cb", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_multiplayer_ui/PlasmicMultiplayerFollowingBorder.tsx", "importSpec": {"modulePath": "wab/client/components/canvas/Multiplayer/MultiplayerFollowingBorder.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_multiplayer_ui/PlasmicMultiplayerFollowingBorder.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "6bdpsCyN1H", "name": "MultiplayerCursor", "type": "managed", "projectId": "eyjDfHaWPk4awNJAqhg4Cb", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_multiplayer_ui/PlasmicMultiplayerCursor.tsx", "importSpec": {"modulePath": "wab/client/components/canvas/Multiplayer/MultiplayerCursor.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_multiplayer_ui/PlasmicMultiplayerCursor.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "JH5l4wUr73", "name": "MultiplayerLightDarkColorProvider", "type": "managed", "projectId": "eyjDfHaWPk4awNJAqhg4Cb", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_multiplayer_ui/PlasmicMultiplayerLightDarkColorProvider.tsx", "importSpec": {"modulePath": "wab/client/components/canvas/Multiplayer/MultiplayerLightDarkColorProvider.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_multiplayer_ui/PlasmicMultiplayerLightDarkColorProvider.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [{"id": "ja_S8_o1BB", "name": "MouseCursorSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_multiplayer_ui/icons/PlasmicIcon__MouseCursorSvg.tsx"}], "images": [], "indirect": false, "globalContextsFilePath": "", "codeComponents": [{"id": "-0WBL1dM40", "name": "PlasmicHead", "componentImportPath": "@plasmicapp/react-web"}, {"id": "-FL3eYTbC6", "name": "<PERSON><PERSON><PERSON>", "componentImportPath": "@plasmicapp/data-sources"}], "splitsProviderFilePath": "", "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "4B48dRthR8uGgyaBYpWthR", "projectApiToken": "KM0loxnuMI1wY6H9OTwyoQeX1t120gHvWZG3QSQV633UpGZoaovEhKRdL0DTgyQFzQSuuIgvPm8cGAGxnFs2dQ", "projectName": "[PlasmicKit] Insert Panel", "version": "latest", "cssFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/plasmic_plasmic_kit_insert_panel.module.css", "components": [{"id": "OwugJe7uVc", "name": "InsertPanel", "type": "managed", "projectId": "4B48dRthR8uGgyaBYpWthR", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/PlasmicInsertPanel.tsx", "importSpec": {"modulePath": "wab/client/components/insert-panel/InsertPanel.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/PlasmicInsertPanel.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "iSztBTxncH", "name": "InsertPanelTabItem", "type": "managed", "projectId": "4B48dRthR8uGgyaBYpWthR", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/PlasmicInsertPanelTabItem.tsx", "importSpec": {"modulePath": "wab/client/components/insert-panel/InsertPanelTabItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/PlasmicInsertPanelTabItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "GU-Aj02J2m", "name": "Separator", "type": "managed", "projectId": "4B48dRthR8uGgyaBYpWthR", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/PlasmicSeparator.tsx", "importSpec": {"modulePath": "wab/client/components/insert-panel/Separator.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/PlasmicSeparator.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "Uq14j_W-86", "name": "Section", "type": "managed", "projectId": "4B48dRthR8uGgyaBYpWthR", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/PlasmicSection.tsx", "importSpec": {"modulePath": "wab/client/components/insert-panel/Section.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/PlasmicSection.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "qqXViGcFWb", "name": "Card", "type": "managed", "projectId": "4B48dRthR8uGgyaBYpWthR", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/PlasmicCard.tsx", "importSpec": {"modulePath": "wab/client/components/insert-panel/Card.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/PlasmicCard.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "3Ao1xbz5MD", "name": "QuickInsertItemOld", "type": "managed", "projectId": "4B48dRthR8uGgyaBYpWthR", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/PlasmicQuickInsertItemOld.tsx", "importSpec": {"modulePath": "wab/client/components/insert-panel/QuickInsertItemOld.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/PlasmicQuickInsertItemOld.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "9mduXtTTjQe", "name": "QuickInsertItem", "type": "managed", "projectId": "4B48dRthR8uGgyaBYpWthR", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/PlasmicQuickInsertItem.tsx", "importSpec": {"modulePath": "wab/client/components/insert-panel/QuickInsertItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/PlasmicQuickInsertItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "BjPuqtCg-H", "name": "ListBottomFade", "type": "managed", "projectId": "4B48dRthR8uGgyaBYpWthR", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/PlasmicListBottomFade.tsx", "importSpec": {"modulePath": "wab/client/components/insert-panel/ListBottomFade.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/PlasmicListBottomFade.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "ae4tx4K7hb", "name": "Spacer", "type": "managed", "projectId": "4B48dRthR8uGgyaBYpWthR", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/PlasmicSpacer.tsx", "importSpec": {"modulePath": "wab/client/components/insert-panel/Spacer.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/PlasmicSpacer.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "bulTqUMaa2", "name": "InsertPanelTabGroup", "type": "managed", "projectId": "4B48dRthR8uGgyaBYpWthR", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/PlasmicInsertPanelTabGroup.tsx", "importSpec": {"modulePath": "wab/client/components/insert-panel/InsertPanelTabGroup.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/PlasmicInsertPanelTabGroup.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [{"id": "wbxAqhnU_B", "name": "StoreIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/icons/PlasmicIcon__Store.tsx"}, {"id": "aOGHT_bTEBZ", "name": "ChevronDownsvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/icons/PlasmicIcon__ChevronDownsvg.tsx"}, {"id": "JoQXtyo-2FA", "name": "ChevronUpsvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_insert_panel/icons/PlasmicIcon__ChevronUpsvg.tsx"}], "images": [], "indirect": false, "globalContextsFilePath": "", "codeComponents": [{"id": "DYgggqaBkt", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "us9Q589t4B", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "BP7V3EkXPURJVwwMyWoHn", "projectApiToken": "LUtZ9rRneje3IFJxqcQFueHTd03P2atbyXaYt9YZYt5CaPRlJh3dJpl7zBSHks02RuAlYeTwcK2KNhRdw", "projectName": "[PlasmicKit] Comments", "version": "latest", "cssFilePath": "wab/client/plasmic/plasmic_kit_comments/plasmic_plasmic_kit_comments.module.css", "components": [{"id": "bV6LLO0B3Y", "name": "CommentsTab", "type": "managed", "projectId": "BP7V3EkXPURJVwwMyWoHn", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicCommentsTab.tsx", "importSpec": {"modulePath": "wab/client/components/comments/CommentsTab.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicCommentsTab.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "l_AKXl2AAu", "name": "CommentPost", "type": "managed", "projectId": "BP7V3EkXPURJVwwMyWoHn", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicCommentPost.tsx", "importSpec": {"modulePath": "wab/client/components/comments/CommentPost.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicCommentPost.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "qi3Y1X2qZ7", "name": "CommentPostForm", "type": "managed", "projectId": "BP7V3EkXPURJVwwMyWoHn", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicCommentPostForm.tsx", "importSpec": {"modulePath": "wab/client/components/comments/CommentPostForm.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicCommentPostForm.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "QY53tkpvLv", "name": "ThreadComments", "type": "managed", "projectId": "BP7V3EkXPURJVwwMyWoHn", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicThreadComments.tsx", "importSpec": {"modulePath": "wab/client/components/comments/ThreadComments.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicThreadComments.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "FOzDmFDbWm", "name": "ReactionButton", "type": "managed", "projectId": "BP7V3EkXPURJVwwMyWoHn", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicReactionButton.tsx", "importSpec": {"modulePath": "wab/client/components/comments/ReactionButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicReactionButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "pTr2lSrGWq8O", "name": "MarkdownHintsPopoverContent", "type": "managed", "projectId": "BP7V3EkXPURJVwwMyWoHn", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicMarkdownHintsPopoverContent.tsx", "importSpec": {"modulePath": "wab/client/components/comments/MarkdownHintsPopoverContent.tsx", "exportName": "MarkdownHintsPopoverContent"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicMarkdownHintsPopoverContent.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "T7mVQBFEWA-V", "name": "MarkdownHintRow", "type": "managed", "projectId": "BP7V3EkXPURJVwwMyWoHn", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicMarkdownHintRow.tsx", "importSpec": {"modulePath": "wab/client/components/comments/MarkdownHintRow.tsx", "exportName": "MarkdownHintRow"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicMarkdownHintRow.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "b0TlBn4m87ta", "name": "AddCommentMarker", "type": "managed", "projectId": "BP7V3EkXPURJVwwMyWoHn", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicAddCommentMarker.tsx", "importSpec": {"modulePath": "wab/client/components/comments/AddCommentMarker.tsx", "exportName": "AddCommentMarker"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicAddCommentMarker.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "hxUVoCIPsT_h", "name": "CommentMarker", "type": "managed", "projectId": "BP7V3EkXPURJVwwMyWoHn", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicCommentMarker.tsx", "importSpec": {"modulePath": "wab/client/components/comments/CommentMarker.tsx", "exportName": "CommentMarker"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicCommentMarker.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "tccr1SFVw_AY", "name": "CommentsDialogHead", "type": "managed", "projectId": "BP7V3EkXPURJVwwMyWoHn", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicCommentsDialogHead.tsx", "importSpec": {"modulePath": "wab/client/components/comments/CommentsDialogHead.tsx", "exportName": "CommentsDialogHead"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicCommentsDialogHead.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "UhTNVxujj1gR", "name": "ThreadCommentsDialog", "type": "managed", "projectId": "BP7V3EkXPURJVwwMyWoHn", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicThreadCommentsDialog.tsx", "importSpec": {"modulePath": "wab/client/components/comments/ThreadCommentsDialog.tsx", "exportName": "ThreadCommentsDialog"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicThreadCommentsDialog.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "bGnXEIS7pS-Y", "name": "CommentPostFormDialog", "type": "managed", "projectId": "BP7V3EkXPURJVwwMyWoHn", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicCommentPostFormDialog.tsx", "importSpec": {"modulePath": "wab/client/components/comments/CommentPostFormDialog.tsx", "exportName": "CommentPostFormDialog"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicCommentPostFormDialog.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "qiIt-rIFSO0f", "name": "ThreadHistory", "type": "managed", "projectId": "BP7V3EkXPURJVwwMyWoHn", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicThreadHistory.tsx", "importSpec": {"modulePath": "wab/client/components/comments/ThreadHistory.tsx", "exportName": "ThreadHistory"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicThreadHistory.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "E0P_lFzVr70L", "name": "ThreadHistoryStatus", "type": "managed", "projectId": "BP7V3EkXPURJVwwMyWoHn", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicThreadHistoryStatus.tsx", "importSpec": {"modulePath": "wab/client/components/comments/ThreadHistoryStatus.tsx", "exportName": "ThreadHistoryStatus"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicThreadHistoryStatus.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "PTsdlYdahZ76", "name": "CommentsHeader", "type": "managed", "projectId": "BP7V3EkXPURJVwwMyWoHn", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicCommentsHeader.tsx", "importSpec": {"modulePath": "wab/client/components/comments/CommentsHeader.tsx", "exportName": "CommentsHeader"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicCommentsHeader.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "nObxvgrqmfvo", "name": "ThreadList", "type": "managed", "projectId": "BP7V3EkXPURJVwwMyWoHn", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicThreadList.tsx", "importSpec": {"modulePath": "wab/client/components/comments/ThreadList.tsx", "exportName": "ThreadList"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicThreadList.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [{"id": "_-_PqTBs1dWd", "name": "IconIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_comments/icons/PlasmicIcon__Icon.tsx"}, {"id": "NiBzf-jmX1er", "name": "Icon2Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_comments/icons/PlasmicIcon__Icon2.tsx"}, {"id": "4ZDQfZwOzIuI", "name": "Icon3Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_comments/icons/PlasmicIcon__Icon3.tsx"}, {"id": "JcPEriMfIfa3", "name": "ChevronDownSvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_comments/icons/PlasmicIcon__ChevronDownSvg.tsx"}], "images": [{"id": "2aijDEIx4x", "name": "69b43a437055b398eff90a515ed4f551.svg", "filePath": "wab/client/plasmic/plasmic_kit_comments/images/_69B43A437055B398Eff90A515Ed4F551Svg.svg"}], "indirect": false, "globalContextsFilePath": "", "codeComponents": [{"id": "ArAsPoU2br", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "BR7hVj2lJ3", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "frhoorZk3bxNXU73uUyvHm", "projectApiToken": "eRgeXIbtCo7BNSWoDvowag4y6VPbyWVnphYkSfpjZr8XV7Fi4gSqObsT2O9QB9DmD1X1z0jp1NdPOgnq4uI2g", "projectName": "[PlasmicKit] State Management", "version": "latest", "cssFilePath": "wab/client/plasmic/plasmic_kit_state_management/plasmic_plasmic_kit_state_management.module.css", "components": [{"id": "S0KtszELh-", "name": "InteractionsSection", "type": "managed", "projectId": "frhoorZk3bxNXU73uUyvHm", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicInteractionsSection.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/StateManagement/InteractionsSection.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicInteractionsSection.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "_uEitkIFZr", "name": "ActionChip", "type": "managed", "projectId": "frhoorZk3bxNXU73uUyvHm", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicActionChip.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/StateManagement/ActionChip.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicActionChip.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "VYbagtLCKV", "name": "HandlerRow", "type": "managed", "projectId": "frhoorZk3bxNXU73uUyvHm", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicHandlerRow.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/StateManagement/HandlerRow.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicHandlerRow.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "YP664uas0Q", "name": "ActionBuilder", "type": "managed", "projectId": "frhoorZk3bxNXU73uUyvHm", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicActionBuilder.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/StateManagement/ActionBuilder.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicActionBuilder.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "Coj9xtPv-Oc", "name": "NewVariable", "type": "managed", "projectId": "frhoorZk3bxNXU73uUyvHm", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicNewVariable.tsx", "importSpec": {"modulePath": "wab/client/components/NewVariable.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicNewVariable.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "2_3UTUe0CF", "name": "VariablesSection", "type": "managed", "projectId": "frhoorZk3bxNXU73uUyvHm", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicVariablesSection.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/StateManagement/VariablesSection.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicVariablesSection.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "sHz-uchOcJ", "name": "VariableType", "type": "managed", "projectId": "frhoorZk3bxNXU73uUyvHm", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicVariableType.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/StateManagement/VariableType.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicVariableType.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "jiD9NQWVHe", "name": "VariableRow", "type": "managed", "projectId": "frhoorZk3bxNXU73uUyvHm", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicVariableRow.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/StateManagement/VariableRow.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicVariableRow.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "s6ZC9dnvK9A", "name": "HandlerSection", "type": "managed", "projectId": "frhoorZk3bxNXU73uUyvHm", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicHandlerSection.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/StateManagement/HandlerSection.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicHandlerSection.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "3OCMg2P28Q", "name": "ImplicitVariablesSection", "type": "managed", "projectId": "frhoorZk3bxNXU73uUyvHm", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicImplicitVariablesSection.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/StateManagement/ImplicitVariablesSection.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicImplicitVariablesSection.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "RzN6mQN5_D", "name": "ParamSection", "type": "managed", "projectId": "frhoorZk3bxNXU73uUyvHm", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicParamSection.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/StateManagement/ParamSection.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicParamSection.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "EmZVqVuGE1", "name": "LabeledItem", "type": "managed", "projectId": "frhoorZk3bxNXU73uUyvHm", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicLabeledItem.tsx", "importSpec": {"modulePath": "wab/client/components/sidebar-tabs/StateManagement/LabeledItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicLabeledItem.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [], "images": [], "indirect": false, "globalContextsFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicGlobalContextsProvider.tsx", "codeComponents": [{"id": "mzj6PyTgPT9u", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "11bnIiSxJUyPwS", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "fuzE93KTc4ZKNBYf3LAfy", "projectApiToken": "mv6evZZOh81is2ZUqBWk3pkfIyFo2IlKLFJiW1Fi6RmdaU2pTScKi5ABPTxye1Clj81w1KzUj9QrlBK26J3w", "projectName": "[PlasmicKit] Context Menu Indicator", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/plasmic_kit_context_menu_indicator/plasmic_plasmic_kit_context_menu_indicator.module.css", "components": [{"id": "iKmOjRERju", "name": "BoundingBoxHighlighter", "type": "managed", "projectId": "fuzE93KTc4ZKNBYf3LAfy", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_context_menu_indicator/PlasmicBoundingBoxHighlighter.tsx", "importSpec": {"modulePath": "wab/client/components/ContextMenuIndicator/BoundingBoxHighlighter.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_context_menu_indicator/PlasmicBoundingBoxHighlighter.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "AITkGvBysG", "name": "ContextMenuIndicator", "type": "managed", "projectId": "fuzE93KTc4ZKNBYf3LAfy", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_context_menu_indicator/PlasmicContextMenuIndicator.tsx", "importSpec": {"modulePath": "wab/client/components/ContextMenuIndicator/ContextMenuIndicator.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_context_menu_indicator/PlasmicContextMenuIndicator.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "juosawBbMz", "name": "ContextMenuIndicatorInner", "type": "managed", "projectId": "fuzE93KTc4ZKNBYf3LAfy", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_context_menu_indicator/PlasmicContextMenuIndicatorInner.tsx", "importSpec": {"modulePath": "wab/client/components/ContextMenuIndicator/ContextMenuIndicatorInner.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_context_menu_indicator/PlasmicContextMenuIndicatorInner.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "5RLoIE7-j5", "name": "MenuIndicator", "type": "managed", "projectId": "fuzE93KTc4ZKNBYf3LAfy", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_context_menu_indicator/PlasmicMenuIndicator.tsx", "importSpec": {"modulePath": "wab/client/components/ContextMenuIndicator/MenuIndicator.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_context_menu_indicator/PlasmicMenuIndicator.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [{"id": "OH84QQlMGV", "name": "Settings2Icon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_context_menu_indicator/icons/PlasmicIcon__Settings2.tsx"}, {"id": "s7v30LEVvl", "name": "DownloadsvgIcon", "moduleFilePath": "wab/client/plasmic/plasmic_kit_context_menu_indicator/icons/PlasmicIcon__Downloadsvg.tsx"}], "images": [], "indirect": false, "globalContextsFilePath": "", "codeComponents": [{"id": "8g_coNTmPn", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "Byt_c1myWd", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "customFunctions": [], "splitsProviderFilePath": "", "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "2dMe7XWUq916KsPnra5vYj", "projectApiToken": "knh1Ivc4eeZBdlcmVAgaio1KEEZnETjCcriPbEZo4MNsfVOmw5fYiFWb4LHYqsUuBg7GdxqLTtwZNEzOiUtsA", "projectName": "[PlasmicKit] End user management", "version": "latest", "cssFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/plasmic_plasmic_kit_end_user_management.module.css", "components": [{"id": "ratDJT6SAx", "name": "AuthConfig", "type": "managed", "projectId": "2dMe7XWUq916KsPnra5vYj", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicAuthConfig.tsx", "importSpec": {"modulePath": "wab/client/components/app-auth/AuthConfig.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicAuthConfig.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "OKf_hhc2Skl", "name": "PermissionRule", "type": "managed", "projectId": "2dMe7XWUq916KsPnra5vYj", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicPermissionRule.tsx", "importSpec": {"modulePath": "wab/client/components/app-auth/PermissionRule.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicPermissionRule.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "7jCYJVNv9q", "name": "PermissionRuleGroup", "type": "managed", "projectId": "2dMe7XWUq916KsPnra5vYj", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicPermissionRuleGroup.tsx", "importSpec": {"modulePath": "wab/client/components/app-auth/PermissionRuleGroup.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicPermissionRuleGroup.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "W_Uez__b5a-", "name": "PermissionsTab", "type": "managed", "projectId": "2dMe7XWUq916KsPnra5vYj", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicPermissionsTab.tsx", "importSpec": {"modulePath": "wab/client/components/app-auth/PermissionsTab.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicPermissionsTab.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "1lRaedKrFR", "name": "AuthTabNavigation", "type": "managed", "projectId": "2dMe7XWUq916KsPnra5vYj", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicAuthTabNavigation.tsx", "importSpec": {"modulePath": "wab/client/components/app-auth/AuthTabNavigation.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicAuthTabNavigation.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "jfBLn3a3U6", "name": "ActivityRow", "type": "managed", "projectId": "2dMe7XWUq916KsPnra5vYj", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicActivityRow.tsx", "importSpec": {"modulePath": "wab/client/components/app-auth/ActivityRow.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicActivityRow.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "4_ozDtECN_", "name": "ActivityTab", "type": "managed", "projectId": "2dMe7XWUq916KsPnra5vYj", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicActivityTab.tsx", "importSpec": {"modulePath": "wab/client/components/app-auth/ActivityTab.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicActivityTab.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "bRXkugOm8Ra", "name": "SettingsTab", "type": "managed", "projectId": "2dMe7XWUq916KsPnra5vYj", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicSettingsTab.tsx", "importSpec": {"modulePath": "wab/client/components/app-auth/SettingsTab.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicSettingsTab.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "wx3bEfvj7g", "name": "DirectoryConfig", "type": "managed", "projectId": "2dMe7XWUq916KsPnra5vYj", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicDirectoryConfig.tsx", "importSpec": {"modulePath": "wab/client/components/app-auth/DirectoryConfig.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicDirectoryConfig.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "_c0HP8vrOTq", "name": "DirectoryUserRow", "type": "managed", "projectId": "2dMe7XWUq916KsPnra5vYj", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicDirectoryUserRow.tsx", "importSpec": {"modulePath": "wab/client/components/app-auth/DirectoryUserRow.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicDirectoryUserRow.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "rF43GtStPO", "name": "GroupItem", "type": "managed", "projectId": "2dMe7XWUq916KsPnra5vYj", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicGroupItem.tsx", "importSpec": {"modulePath": "wab/client/components/app-auth/GroupItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicGroupItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "FvbTyDpXOYV", "name": "RedirectUrIsControl", "type": "managed", "projectId": "2dMe7XWUq916KsPnra5vYj", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicRedirectUrIsControl.tsx", "importSpec": {"modulePath": "wab/client/components/app-auth/RedirectUrIsControl.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicRedirectUrIsControl.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "Wx1WB4BUap", "name": "RedirectUriRow", "type": "managed", "projectId": "2dMe7XWUq916KsPnra5vYj", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicRedirectUriRow.tsx", "importSpec": {"modulePath": "wab/client/components/app-auth/RedirectUriRow.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicRedirectUriRow.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "HQf5xQMdD4", "name": "AuthConfigToken", "type": "managed", "projectId": "2dMe7XWUq916KsPnra5vYj", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicAuthConfigToken.tsx", "importSpec": {"modulePath": "wab/client/components/app-auth/AuthConfigToken.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_end_user_management/PlasmicAuthConfigToken.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [], "images": [], "indirect": false, "globalContextsFilePath": "", "codeComponents": [{"id": "9Q2msuZoGY", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "huWYRKcTXe", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "splitsProviderFilePath": "", "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "8PtdGodUbexNYgkuyBUcWu", "projectApiToken": "34o5R5KswluuZ4sD83F88oQrcLWsNNeUpCptQ8Okf4hYqgjnHnX2idGAlhtB7WWQQelNAInf5XFazR1cGw", "projectName": "plasmic-embed-css", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/plasmic_embed_css/plasmic_plasmic_embed_css.module.css", "components": [], "icons": [], "images": [], "indirect": true, "globalContextsFilePath": "wab/client/plasmic/plasmic_embed_css/PlasmicGlobalContextsProvider.tsx", "codeComponents": [{"id": "qF0uJxFztB", "name": "EmbedCss", "displayName": "global-embed-css", "componentImportPath": "@plasmicpkgs/plasmic-embed-css"}], "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "ehckhYnyDHgCBbV47m9bkf", "projectApiToken": "SDcTxkQS1NI2sO7JrvdkphHLscQPWgw6vF1SQZcQuwHFZuCve20JpfONSzb0FDnwQjwp8z2VqhC8bA2aVTA", "projectName": "[PlasmicKit] Pricing", "version": "latest", "cssFilePath": "wab/client/plasmic/plasmic_kit_pricing/plasmic_plasmic_kit_pricing.module.css", "components": [{"id": "Xx_WsdQKli-S", "name": "PriceTierPicker", "type": "managed", "projectId": "ehckhYnyDHgCBbV47m9bkf", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicPriceTierPicker.tsx", "importSpec": {"modulePath": "wab/client/components/pricing/PriceTierPicker.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicPriceTierPicker.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "P7E8qtNzKrbM", "name": "PriceTier", "type": "managed", "projectId": "ehckhYnyDHgCBbV47m9bkf", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicPriceTier.tsx", "importSpec": {"modulePath": "wab/client/components/pricing/PriceTier.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicPriceTier.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "Z40kBWC-Knbn", "name": "PriceTierFeatureItem", "type": "managed", "projectId": "ehckhYnyDHgCBbV47m9bkf", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicPriceTierFeatureItem.tsx", "importSpec": {"modulePath": "wab/client/components/pricing/PriceTierFeatureItem.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicPriceTierFeatureItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "UwHbCO-1rFrq", "name": "PriceTierChip", "type": "managed", "projectId": "ehckhYnyDHgCBbV47m9bkf", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicPriceTierChip.tsx", "importSpec": {"modulePath": "wab/client/components/pricing/PriceTierChip.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicPriceTierChip.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "aVJYhoS8iDMR", "name": "HoverableText", "type": "managed", "projectId": "ehckhYnyDHgCBbV47m9bkf", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicHoverableText.tsx", "importSpec": {"modulePath": "wab/client/components/pricing/HoverableText.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicHoverableText.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "XvpbI4g-IJWK", "name": "Popout", "type": "managed", "projectId": "ehckhYnyDHgCBbV47m9bkf", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicPopout.tsx", "importSpec": {"modulePath": "wab/client/components/pricing/Popout.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicPopout.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "OOKbAz_EJ7Rm", "name": "ElevatedCard", "type": "managed", "projectId": "ehckhYnyDHgCBbV47m9bkf", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicElevatedCard.tsx", "importSpec": {"modulePath": "wab/client/components/pricing/ElevatedCard.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicElevatedCard.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "IzGvUfmCzHyO", "name": "ExpandableSection", "type": "managed", "projectId": "ehckhYnyDHgCBbV47m9bkf", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicExpandableSection.tsx", "importSpec": {"modulePath": "wab/client/components/pricing/ExpandableSection.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicExpandableSection.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "NqVzp6p_r1Wa", "name": "PricingButton", "type": "managed", "projectId": "ehckhYnyDHgCBbV47m9bkf", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicPricingButton.tsx", "importSpec": {"modulePath": "wab/client/components/pricing/PricingButton.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicPricingButton.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "1T4UNMYLSC7u", "name": "HoverableIcon", "type": "managed", "projectId": "ehckhYnyDHgCBbV47m9bkf", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicHoverableIcon.tsx", "importSpec": {"modulePath": "wab/client/components/pricing/HoverableIcon.tsx"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicHoverableIcon.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [], "images": [], "indirect": false, "globalContextsFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicGlobalContextsProvider.tsx", "codeComponents": [{"id": "LeMCYgOK43uT", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "Fk1cd_tMCjYO", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}, {"id": "eAE4YEj_YxMC", "name": "PricingTooltip", "displayName": "<PERSON><PERSON><PERSON>", "componentImportPath": "./src/wab/client/components/pricing/Tooltip"}], "splitsProviderFilePath": "", "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "28e27syQUKgfkErJT9mxWA", "projectApiToken": "bSBdSV2gtOVUoR3tmecOoxqnBarm1FQvTEvu0e4y8kD1zMySRzQ0ZDlUgARwK31fOv6HVoxR6P7mKZJ5A", "projectName": "[PlasmicKit] Responsive Breakpoints", "version": "0.0.1", "cssFilePath": "wab/client/plasmic/plasmic_kit_responsive_breakpoints/plasmic_plasmic_kit_responsive_breakpoints.module.css", "components": [], "icons": [], "images": [], "indirect": true, "globalContextsFilePath": "", "codeComponents": [{"id": "Pp89ciaZo-Kt", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "RG2TGyj2bcgG", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "splitsProviderFilePath": "", "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "gmeH6XgPaBtkt51HunAo4g", "projectApiToken": "LBbn8U2dmbpX9oeuRsYNyEKki39EXydGQgs9vBvR5sLHfGEZsK4owvOj7Q7SnflW4ASIEdLFAeeoxGxXZ8dJtA", "projectName": "react-aria", "version": ">0.0.0", "cssFilePath": "wab/client/plasmic/react_aria/plasmic.module.css", "components": [], "icons": [], "images": [], "indirect": true, "globalContextsFilePath": "", "splitsProviderFilePath": "", "codeComponents": [{"id": "QswWn7k1U7ET", "name": "BaseSelect", "displayName": "plasmic-react-aria-select", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerSelect"}, {"id": "XEco5EOfFG7-", "name": "BaseSelectValue", "displayName": "plasmic-react-aria-select-value", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerSelect"}, {"id": "YsCvUUXRgPKy", "name": "BaseComboBox", "displayName": "plasmic-react-aria-combobox", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerComboBox"}, {"id": "HvUCgqiPzPEX", "name": "BaseButton", "displayName": "plasmic-react-aria-button", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerButton"}, {"id": "me7KTdO_KIkf", "name": "BaseLabel", "displayName": "plasmic-react-aria-label", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerLabel"}, {"id": "4sLOTHHFrzj2", "name": "BaseListBox", "displayName": "plasmic-react-aria-listbox", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerListBox", "helper": {"name": "listboxHelpers", "importPath": "@plasmicpkgs/react-aria/skinny/registerListBox"}}, {"id": "PoAYkttnnKt8", "name": "BaseListBoxItem", "displayName": "plasmic-react-aria-listbox-item", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerListBoxItem"}, {"id": "zIp51FL8KHRa", "name": "BasePopover", "displayName": "plasmic-react-aria-popover", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerPopover"}, {"id": "P1zT4UvhSciD", "name": "BaseInput", "displayName": "plasmic-react-aria-input", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerInput", "helper": {"name": "inputHelpers", "importPath": "@plasmicpkgs/react-aria/skinny/registerInput"}}, {"id": "IRebgwZ5KOxr", "name": "BaseSwitch", "displayName": "plasmic-react-aria-switch", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerSwitch"}, {"id": "t7JSdrAmVPUA", "name": "BaseCheckbox", "displayName": "plasmic-react-aria-checkbox", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerCheckbox"}, {"id": "J6dFzk9sLhiL", "name": "BaseCheckboxGroup", "displayName": "plasmic-react-aria-checkboxGroup", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerCheckboxGroup"}, {"id": "YiLyhyFTHV9y", "name": "BaseRadioGroup", "displayName": "plasmic-react-aria-radioGroup", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerRadioGroup"}, {"id": "L01PBsCNswpe", "name": "BaseRadio", "displayName": "plasmic-react-aria-radioGroup-radio", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerRadio"}, {"id": "rMswhyNZvi7B", "name": "BaseTextField", "displayName": "plasmic-react-aria-textField", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerTextField"}, {"id": "sHCwEg6H_Y31", "name": "BaseModal", "displayName": "plasmic-react-aria-modal", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerModal"}, {"id": "_Qjx-IzfLF6l", "name": "BaseTooltip", "displayName": "plasmic-react-aria-tooltip", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerTooltip"}, {"id": "kp28MgccfwlQ", "name": "BaseDialogTrigger", "displayName": "plasmic-react-aria-dialogTrigger", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerDialogTrigger"}, {"id": "51hRux-iu0RF", "name": "BaseText", "displayName": "plasmic-react-aria-text", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerText"}, {"id": "-_rmqfmyqbxd", "name": "BaseText", "displayName": "plasmic-react-aria-description", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerText"}, {"id": "iIIdXu16I-k-", "name": "BaseSection", "displayName": "plasmic-react-aria-listbox-section", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerSection"}, {"id": "kv1js5i5hCxL", "name": "BaseSlider", "displayName": "plasmic-react-aria-slider", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerSlider"}, {"id": "ZoPZHoboZWhP", "name": "BaseSliderOutput", "displayName": "plasmic-react-aria-slider-sliderOutput", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerSliderOutput"}, {"id": "kiBpPWTzQmrA", "name": "BaseSliderThumb", "displayName": "plasmic-react-aria-slider-sliderThumb", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerSliderThumb"}, {"id": "flW7Lvcc_jyV", "name": "BaseSliderTrack", "displayName": "plasmic-react-aria-slider-sliderTrack", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerSliderTrack"}, {"id": "kbh41PnPdelo", "name": "BaseSlider", "displayName": "plasmic-react-aria-slider-range-slider", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerSlider"}, {"id": "FCiuD5hxcsIL", "name": "BaseHeading", "displayName": "plasmic-react-aria-heading", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerHeading"}, {"id": "z-WtOn_lG8-p", "name": "BaseTextArea", "displayName": "plasmic-react-aria-textarea", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerTextArea", "helper": {"name": "inputHelpers", "importPath": "@plasmicpkgs/react-aria/skinny/registerTextArea"}}, {"id": "ve6CbDRk1Jw-", "name": "BaseOverlayArrow", "displayName": "plasmic-react-aria-overlayArrow", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerOverlayArrow"}, {"id": "xPmAZLS8Wf-7", "name": "BaseDialog", "displayName": "plasmic-react-aria-dialog", "componentImportPath": "@plasmicpkgs/react-aria/skinny/registerDialog"}], "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}, {"projectId": "kTSMroKPFv65RRTb44SCtk", "projectApiToken": "gsvvkXd1dok0xQQ89nOSOCDgGMkbupL0DtJ11cm2ubLbQZKMGaiBmo0HrRr7BmkC8bZgfVQCeEdOth3hLNZw", "projectName": "[PlasmicKit] User Mentions", "version": ">=0.0.0", "cssFilePath": "wab/client/plasmic/user_mentions/plasmic.module.css", "components": [{"id": "l-sEnd6egOHM", "name": "UserList", "type": "managed", "projectId": "kTSMroKPFv65RRTb44SCtk", "renderModuleFilePath": "wab/client/plasmic/user_mentions/PlasmicUserList.tsx", "importSpec": {"modulePath": "wab/client/components/user-mentions/UserList.tsx"}, "cssFilePath": "wab/client/plasmic/user_mentions/PlasmicUserList.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "w9CXWQAYB8kA", "name": "UserListItem", "type": "managed", "projectId": "kTSMroKPFv65RRTb44SCtk", "renderModuleFilePath": "wab/client/plasmic/user_mentions/PlasmicUserListItem.tsx", "importSpec": {"modulePath": "wab/client/components/user-mentions/UserListItem.tsx"}, "cssFilePath": "wab/client/plasmic/user_mentions/PlasmicUserListItem.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "Gc2UoCN4xKJL", "name": "UserMentionsPopoverContent", "type": "managed", "projectId": "kTSMroKPFv65RRTb44SCtk", "renderModuleFilePath": "wab/client/plasmic/user_mentions/PlasmicUserMentionsPopoverContent.tsx", "importSpec": {"modulePath": "wab/client/components/user-mentions/UserMentionsPopoverContent.tsx", "exportName": "UserMentionsPopoverContent"}, "cssFilePath": "wab/client/plasmic/user_mentions/PlasmicUserMentionsPopoverContent.module.css", "scheme": "blackbox", "componentType": "component"}, {"id": "HSPuw3LccxMD", "name": "UserMentionDisplay", "type": "managed", "projectId": "kTSMroKPFv65RRTb44SCtk", "renderModuleFilePath": "wab/client/plasmic/plasmic_kit_user_mentions/PlasmicUserMentionDisplay.tsx", "importSpec": {"modulePath": "wab/client/components/user-mentions/UserMentionDisplay.tsx", "exportName": "UserMentionDisplay"}, "cssFilePath": "wab/client/plasmic/plasmic_kit_user_mentions/PlasmicUserMentionDisplay.module.css", "scheme": "blackbox", "componentType": "component"}], "icons": [{"id": "vpjhh_trEdY0", "name": "CircleIcon", "moduleFilePath": "wab/client/plasmic/user_mentions/icons/PlasmicIcon__Circle.tsx"}, {"id": "k02Fwku7Tl9M", "name": "ChevronDownIcon", "moduleFilePath": "wab/client/plasmic/user_mentions/icons/PlasmicIcon__ChevronDown.tsx"}, {"id": "sUed0_FBnG4j", "name": "TriangleFilledIcon", "moduleFilePath": "wab/client/plasmic/user_mentions/icons/PlasmicIcon__TriangleFilled.tsx"}, {"id": "9tHnrDLYFnPe", "name": "IconIcon", "moduleFilePath": "wab/client/plasmic/user_mentions/icons/PlasmicIcon__Icon.tsx"}], "images": [{"id": "Vw4yl7Sbw35N", "name": "image", "filePath": "wab/client/plasmic/user_mentions/images/image.svg"}], "indirect": false, "globalContextsFilePath": "", "splitsProviderFilePath": "", "codeComponents": [{"id": "hwcHb56kQ4jQ", "name": "PlasmicHead", "displayName": "hostless-plasmic-head", "componentImportPath": "@plasmicapp/react-web"}, {"id": "DqXRN909yh_2", "name": "<PERSON><PERSON><PERSON>", "displayName": "plasmic-data-source-fetcher", "componentImportPath": "@plasmicapp/react-web/lib/data-sources"}], "customFunctions": [], "styleTokensProviderFilePath": "", "projectModuleFilePath": ""}], "globalVariants": {"variantGroups": [{"id": "7e77cc6f-da4a-4720-a461-6e1c4584e418", "name": "UnnamedGlobalVariantGroup", "projectId": "4dd7e277-5c92-4d86-86bc-a1bd1cd743f0", "contextFilePath": "wab/client/plasmic/PP__GlobalVariant__UnnamedGlobalVariantGroup.tsx"}, {"id": "2dzYbdw5Xtx", "name": "Screen", "projectId": "ooL7EhXDmFQWnW9sxtchhE", "contextFilePath": "wab/client/plasmic/PlasmicGlobalVariant__Screen.tsx"}, {"id": "IFgLgWglLv", "name": "CodegenType", "projectId": "dyzP6dbCdycwJpqiR2zkwe", "contextFilePath": "wab/client/plasmic/plasmic_kit_docs_portal/PlasmicGlobalVariant__CodegenType.tsx"}, {"id": "0tMEAYfWN538", "name": "Screen", "projectId": "fQPf2UiMEMhB52C8QQXwWe", "contextFilePath": "wab/client/plasmic/plasmic_kit_omnibar/PlasmicGlobalVariant__Screen.tsx"}, {"id": "n2dxsui6xhqx", "name": "Screen", "projectId": "6BCq4vMow1yqGKFdcP68Rz", "contextFilePath": "wab/client/plasmic/plasmic_kit_page_settings/PlasmicGlobalVariant__Screen.tsx"}, {"id": "2SnfbihspmoJ", "name": "Screen", "projectId": "9csusiyEETC5n9fFKLeYNK", "contextFilePath": "wab/client/plasmic/plasmic_kit_data_queries/PlasmicGlobalVariant__Screen.tsx"}, {"id": "v16B7zZEJF0m", "name": "Screen", "projectId": "4nEqjj19Sbp3EVnBkgQMP1", "contextFilePath": "wab/client/plasmic/plasmic_kit_data_expressions/PlasmicGlobalVariant__Screen.tsx"}, {"id": "BW9kEyJ7_p", "name": "Screen", "projectId": "sZNLVjiaeNGLXmdsrbRQAX", "contextFilePath": "wab/client/plasmic/q_4_color_tokens/PlasmicGlobalVariant__Screen.tsx"}, {"id": "NXbQfeebYy0", "name": "Screen", "projectId": "sDniSX4oPUZFyk2sXXb3nh", "contextFilePath": "wab/client/plasmic/q_4_text_mixins_product/PlasmicGlobalVariant__Screen.tsx"}, {"id": "ydp0rlrGOfOvo", "name": "Screen", "projectId": "ieacQ3Z46z4gwo1FnaB5vY", "contextFilePath": "wab/client/plasmic/plasmic_kit_cms/PlasmicGlobalVariant__Screen.tsx"}, {"id": "bOVbNSh4sGqoa", "name": "Screen", "projectId": "gtUDvxG6cmBbSzqLikNzoP", "contextFilePath": "wab/client/plasmic/plasmic_kit_optimize/PlasmicGlobalVariant__Screen.tsx"}, {"id": "FEwtgyZeZWHon", "name": "Screen", "projectId": "cQnF1HuwK97HkvkrC6uRk2", "contextFilePath": "wab/client/plasmic/plasmic_kit_analytics/PlasmicGlobalVariant__Screen.tsx"}, {"id": "33bo6t6aaBqtV", "name": "Screen", "projectId": "p8FkKgCnyuat1kHSEYAKfW", "contextFilePath": "wab/client/plasmic/plasmic_kit_merge_flow/PlasmicGlobalVariant__Screen.tsx"}, {"id": "36nw8KCcgswV1", "name": "Screen", "projectId": "BP7V3EkXPURJVwwMyWoHn", "contextFilePath": "wab/client/plasmic/plasmic_kit_comments/PlasmicGlobalVariant__Screen.tsx"}, {"id": "DJ0PzuYE_I5xyS", "name": "Screen", "projectId": "frhoorZk3bxNXU73uUyvHm", "contextFilePath": "wab/client/plasmic/plasmic_kit_state_management/PlasmicGlobalVariant__Screen.tsx"}, {"id": "IyzVSfo1whzCb", "name": "Screen", "projectId": "fuzE93KTc4ZKNBYf3LAfy", "contextFilePath": "wab/client/plasmic/plasmic_kit_context_menu_indicator/PlasmicGlobalVariant__Screen.tsx"}, {"id": "hIjF9NLAUKG-", "name": "Environment", "projectId": "ehckhYnyDHgCBbV47m9bkf", "contextFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicGlobalVariant__Environment.tsx"}, {"id": "PbV7vw3AiD6M", "name": "Screen", "projectId": "28e27syQUKgfkErJT9mxWA", "contextFilePath": "wab/client/plasmic/plasmic_kit_responsive_breakpoints/PlasmicGlobalVariant__Screen.tsx"}, {"id": "B61LAyP8VHu7", "name": "Screen", "projectId": "ehckhYnyDHgCBbV47m9bkf", "contextFilePath": "wab/client/plasmic/plasmic_kit_pricing/PlasmicGlobalVariant__Screen.tsx"}, {"id": "4QWQO5iAb22t", "name": "Screen", "projectId": "kTSMroKPFv65RRTb44SCtk", "contextFilePath": "wab/client/plasmic/user_mentions/PlasmicGlobalVariant__Screen.tsx"}]}, "wrapPagesWithGlobalContexts": true, "postSyncCommands": ["git ls-files -m | xargs pre-commit run prettier --files"], "cliVersion": "0.1.342", "$schema": "https://unpkg.com/@plasmicapp/cli@0.1.342/dist/plasmic.schema.json"}