{"name": "@plasmicapp/cli", "version": "0.1.342", "description": "plasmic cli for syncing local code with Plasmic designs", "engines": {"node": ">=12"}, "main": "./dist/lib.js", "types": "./dist/lib.d.ts", "bin": {"plasmic": "./dist/index.js"}, "scripts": {"wtest": "jest --colors --watchAll", "test": "jest --colors", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand --watch", "build": "bash build.sh", "plasmic": "ts-node src/index.ts", "prepublishOnly": "npm run build"}, "devDependencies": {"@babel/core": "^7.12.3", "@babel/generator": "^7.12.1", "@babel/parser": "^7.12.3", "@babel/preset-typescript": "^7.12.1", "@babel/traverse": "^7.12.1", "@babel/types": "^7.23.0", "@sentry/node": "^5.19.2", "@supercharge/promise-pool": "^2.4.0", "@types/babel__core": "^7.20.3", "@types/babel__generator": "^7.6.6", "@types/babel__traverse": "^7.20.3", "@types/cli-progress": "^3.11.0", "@types/findup-sync": "^2.0.2", "@types/glob": "^7.1.3", "@types/inquirer": "^6.5.0", "@types/jest": "^29.5.11", "@types/latest-version": "^4.0.1", "@types/lodash": "^4.14.157", "@types/node": "^14.0.23", "@types/node-fetch": "^2.6.7", "@types/pako": "^1.0.1", "@types/prettier": "^2.0.2", "@types/semver": "^7.3.1", "@types/tmp": "^0.2.0", "@types/update-notifier": "^4.1.0", "@types/uuid": "^8.3.0", "@types/wrap-ansi": "^3.0.0", "@types/yargs": "^15.0.5", "@typescript-eslint/eslint-plugin": "^5.61.0", "@typescript-eslint/parser": "^5.61.0", "axios": "^0.21.1", "chalk": "^4.1.0", "cli-progress": "^3.12.0", "esbuild": "0.17.18", "eslint": "^7.7.0", "fast-glob": "^3.2.4", "findup-sync": "^4.0.0", "fs": "^0.0.1-security", "glob": "^7.1.6", "inquirer": "^7.3.2", "jest": "^29.7.0", "jest-circus": "^29.7.0", "latest-version": "^5.1.0", "lodash": "^4.17.19", "moment": "^2.27.0", "node-fetch": "^2", "open": "^8.0.9", "pako": "^1.0.11", "path": "^0.12.7", "prettier": "^2.0.5", "semver": "^7.3.2", "socket.io-client": "^4.1.2", "tmp": "^0.2.1", "ts-jest": "^29.1.1", "ts-node": "^8.10.2", "typescript": "^4.9.5", "typescript-json-schema": "^0.45.0", "upath": "^1.2.0", "utility-types": "^3.10.0", "uuid": "^8.3.1", "winston": "^3.3.3", "wrap-ansi": "^7.0.0", "yargs": "^15.4.1"}}