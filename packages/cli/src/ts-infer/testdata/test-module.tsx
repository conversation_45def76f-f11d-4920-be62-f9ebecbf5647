// Test module for ts-infer tests
import * as React from "react";

export function simpleFunction(arg1: string, arg2: number) {
  return arg1 + arg2;
}

export const arrowFunction = (name: string, age: number, active: boolean) => {
  return `${name} is ${age} years old and ${active ? "active" : "inactive"}`;
};

export function complexFunction(
  config: {
    name: string;
    options?: {
      enabled: boolean;
      count: number;
    };
  },
  callback: (value: string) => void,
  items: string[]
) {
  callback(config.name);
}

// React component with simple props
export type SimpleProps = {
  title: string;
  count: number;
};

export function SimpleComponent(props: SimpleProps) {
  return null;
}

// React component with complex props
export interface ComplexProps {
  children: React.ReactNode;
  onClick?: (event: { x: number; y: number }) => void;
  items: Array<{
    id: string;
    label: string;
  }>;
  status: "loading" | "success" | "error";
  metadata?: Record<string, any>;
}

export const ComplexComponent: React.FC<ComplexProps> = (props) => {
  return null;
};

// Component with inline props type
export function InlinePropsComponent(props: {
  name: string;
  age?: number;
  tags: string[];
}) {
  return null;
}

// Default export function
export default function defaultFunction(value: string, multiplier: number) {
  return value.repeat(multiplier);
}

// Type aliases
export type UserId = string;
export type UserStatus = "active" | "inactive" | "suspended";

export interface User {
  id: UserId;
  name: string;
  email: string;
  status: UserStatus;
  profile?: {
    bio: string;
    avatar?: string;
  };
}

export function processUser(user: User, options?: { validate: boolean }) {
  return user;
}
