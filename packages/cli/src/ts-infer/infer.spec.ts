import { inferFunctionParams, inferReactComponentProps } from "./infer";
import * as path from "path";

const testModulePath = path.join(__dirname, "testdata/test-module.tsx");

describe("inferFunctionParams", () => {
  it("should infer parameters of a simple function", () => {
    expect(
      inferFunctionParams({ modulePath: testModulePath, namedExport: "simpleFunction" })
    ).toEqual(
      [{ name: "arg1", type: { type: "string" } }, { name: "arg2", type: { type: "number" } }]);
  });

  it("should infer parameters of an arrow function", () => {
    expect(
      inferFunctionParams({
        modulePath: testModulePath,
        namedExport: "arrowFunction",
      })
    ).toEqual([
      { name: "name", type: { type: "string" } },
      { name: "age", type: { type: "number" } },
      { name: "active", type: { type: "boolean" } },
    ]);
  });

  it("should infer complex function parameters", () => {
    expect(inferFunctionParams({
      modulePath: testModulePath,
      namedExport: "complexFunction",
    }))
      .toEqual([
      {
        name: "config",
        type: {
          type: "object",
          fields: {
            name: {
              optional: false,
              type: { type: "string" }
            },
            options: {
              optional: true,
              type: {
                type: "object",
                fields: {
                  enabled: {
                    optional: false,
                    type: { type: "boolean" }
                  },
                  count: {
                    optional: false,
                    type: { type: "number" }
                  }
                }
              },
            },
          },
        },
      },
      {
        name: "callback",
        type: {
          type: "function",
          params: [{ name: "value", type: { type: "string" } }],
          returnType: { type: "void" },
        },
      },
      {
        name: "items",
        type: {
          type: "array",
          elementType: { type: "string" },
        },
      },
    ]);
  });

  it("should infer default export function parameters", () => {
    expect(
      inferFunctionParams({
        modulePath: testModulePath,
        defaultExport: true,
      })
    ).toEqual([
      { name: "value", type: { type: "string" } },
      { name: "multiplier", type: { type: "number" } },
    ]);
  });

  it("should handle function with type aliases", () => {
    expect(inferFunctionParams({
      modulePath: testModulePath,
      namedExport: "processUser",
    })).toEqual([
      {
        name: "user",
        type: {
          type: "type",
          name: "User",
          resolved: {
            type: "object",
            fields: {
              id: { optional: false, type: { type: "type", name: "UserId", resolved: { type: "string" } } },
              name: { optional: false, type: { type: "string" } },
              email: { optional: false, type: { type: "string" } },
              status: {
                optional: false,
                type: {
                  type: "type",
                  name: "UserStatus",
                  resolved: {
                    type: "union",
                    types: [
                      { type: "literal", value: "active" },
                      { type: "literal", value: "inactive" },
                      { type: "literal", value: "suspended" }
                    ]
                  }
                }
              },
              profile: {
                optional: true,
                type: {
                  type: "object",
                  fields: {
                    bio: { optional: false, type: { type: "string" } },
                    avatar: { optional: true, type: { type: "string" } }
                  }
                }
              },
            },
          },
        },
      },
      {
        name: "options",
        type: {
          type: "object",
          fields: {
            validate: { optional: false, type: { type: "boolean" } }
          }
        },
      },
    ]);
  });

  it("should throw error for non-existent function", () => {
    expect(() => {
      inferFunctionParams({
        modulePath: testModulePath,
        namedExport: "nonExistentFunction",
      });
    }).toThrow("Could not find exported function: nonExistentFunction");
  });
});

describe("inferReactComponentProps", () => {
  it("should infer props of a simple React component", () => {
    expect(
      inferReactComponentProps({
        modulePath: testModulePath,
        namedExport: "SimpleComponent",
      })
    ).toEqual({
      name: "props",
      type: {
        type: "type",
        name: "SimpleProps",
        resolved: {
          type: "object",
          fields: {
            title: { optional: false, type: { type: "string" } },
            count: { optional: false, type: { type: "number" } },
          },
        },
      },
    });
  });

  it("should infer props of a complex React component", () => {
    expect(inferReactComponentProps({
      modulePath: testModulePath,
      namedExport: "ComplexComponent",
    })).toEqual({
      name: "props",
      type: {
        type: "type",
        name: "ComplexProps",
        resolved: {
          type: "object",
          fields: {
            children: {
              optional: false,
              type: {
                type: "type",
                name: "React.ReactNode",
                resolved: null,
              },
            },
            onClick: {
              optional: true,
              type: {
                type: "function",
                params: [
                  {
                    name: "event",
                    type: {
                      type: "object",
                      fields: {
                        x: { optional: false, type: { type: "number" } },
                        y: { optional: false, type: { type: "number" } }
                      }
                    }
                  }
                ],
                returnType: { type: "void" },
              },
            },
            items: {
              optional: false,
              type: {
                type: "type",
                name: "Array",
                resolved: {
                  type: "array",
                  elementType: {
                    type: "object",
                    fields: {
                      id: { optional: false, type: { type: "string" } },
                      label: { optional: false, type: { type: "string" } },
                    },
                  },
                },
              },
            },
            status: {
              optional: false,
              type: {
                type: "union",
                types: [
                  { type: "literal", value: "loading" },
                  { type: "literal", value: "success" },
                  { type: "literal", value: "error" },
                ],
              },
            },
            metadata: {
              optional: true,
              type: {
                type: "type",
                name: "Record",
                resolved: {
                  type: "type",
                  name: "Record",
                  resolved: {
                    type: "object",
                    fields: {}
                  }
                }
              }
            },
          },
        },
      },
    });
  });

  it("should infer inline props type", () => {
    expect(
      inferReactComponentProps({
        modulePath: testModulePath,
        namedExport: "InlinePropsComponent",
      })
    ).toEqual({
      name: "props",
      type: {
        type: "object",
        fields: {
          name: { optional: false, type: { type: "string" } },
          age: {
            optional: true,
            type: { type: "number" },
          },
          tags: {
            optional: false,
            type: {
              type: "array",
              elementType: { type: "string" },
            },
          },
        },
      },
    });
  });

  it("should throw error for non-existent component", () => {
    expect(() => {
      inferReactComponentProps({
        modulePath: testModulePath,
        namedExport: "NonExistentComponent",
      });
    }).toThrow("Could not find exported component: NonExistentComponent");
  });
});
