import * as path from "path";
import * as ts from "typescript";
import { existsBuffered } from "../utils/file-utils";

/** Locates an exported symbol. */
interface SymbolLocator {
  /** Module path that should be resolvable by closest tsconfig.json. */
  modulePath: string;
  defaultExport?: boolean;
  namedExport?: string;
}

type Param = {
  name: string;
  type: Type;
};

type ObjectType = {
  type: "object";
  fields: Record<string, { optional: boolean; type: Type }>;
};
type Type =
  | { type: "string" }
  | { type: "number" }
  | { type: "boolean" }
  | { type: "any" }
  | { type: "unknown" }
  | { type: "void" }
  | { type: "undefined" }
  | { type: "null" }
  | { type: "literal"; value: string | number | boolean }
  | { type: "array"; elementType: Type }
  | { type: "tuple"; elements: Type[] }
  | { type: "union"; types: Type[] }
  | { type: "intersection"; types: Type[] }
  | ObjectType
  | { type: "function"; params: Param[]; returnType: Type }
  | { type: "type"; name: string; resolved: Type | null };

function findTsConfig(startPath: string): string | undefined {
  let currentDir = path.resolve(startPath);
  const root = path.parse(currentDir).root;

  while (currentDir !== root) {
    const tsConfigPath = path.join(currentDir, "tsconfig.json");
    if (existsBuffered(tsConfigPath)) {
      return tsConfigPath;
    }
    currentDir = path.dirname(currentDir);
  }
  return undefined;
}

function resolveModulePath(modulePath: string, basePath?: string): string {
  // If it's already an absolute path, use it
  if (path.isAbsolute(modulePath)) {
    return modulePath;
  }

  // If it's a relative path, resolve it
  if (modulePath.startsWith("./") || modulePath.startsWith("../")) {
    return path.resolve(basePath || process.cwd(), modulePath);
  }

  // For npm packages, we need to resolve them
  const baseDir = basePath || process.cwd();
  try {
    // Try to resolve as a node module
    const resolved = require.resolve(modulePath, {
      paths: [baseDir],
    });
    return resolved;
  } catch {
    // If resolution fails, return as-is and let TypeScript handle it
    return modulePath;
  }
}

function createProgram(modulePath: string): {
  program: ts.Program;
  resolvedPath: string;
} {
  const resolvedPath = resolveModulePath(modulePath);
  const isNodeModule =
    !path.isAbsolute(modulePath) &&
    !modulePath.startsWith("./") &&
    !modulePath.startsWith("../");

  const searchDir = isNodeModule ? process.cwd() : path.dirname(resolvedPath);
  const tsConfigPath = findTsConfig(searchDir);

  let compilerOptions: ts.CompilerOptions = {
    target: ts.ScriptTarget.ES2015,
    module: ts.ModuleKind.CommonJS,
    jsx: ts.JsxEmit.React,
    esModuleInterop: true,
    skipLibCheck: true,
    moduleResolution: ts.ModuleResolutionKind.NodeJs,
    allowJs: true,
    maxNodeModuleJsDepth: 10,
  };

  if (tsConfigPath) {
    const configFile = ts.readConfigFile(tsConfigPath, ts.sys.readFile);
    const parsedConfig = ts.parseJsonConfigFileContent(
      configFile.config,
      ts.sys,
      path.dirname(tsConfigPath)
    );
    compilerOptions = parsedConfig.options;
  }

  // For node modules, we might need to use module resolution
  if (isNodeModule) {
    const host = ts.createCompilerHost(compilerOptions);
    const resolvedModule = ts.resolveModuleName(
      modulePath,
      path.join(searchDir, "dummy.ts"),
      compilerOptions,
      host
    );

    if (resolvedModule.resolvedModule) {
      const actualPath = resolvedModule.resolvedModule.resolvedFileName;
      return {
        program: ts.createProgram([actualPath], compilerOptions),
        resolvedPath: actualPath,
      };
    }
  }

  return {
    program: ts.createProgram([resolvedPath], compilerOptions),
    resolvedPath,
  };
}

function typeNodeToType(
  typeNode: ts.TypeNode | undefined,
  typeChecker: ts.TypeChecker,
  depth: number = 0
): Type {
  if (!typeNode) {
    return { type: "any" };
  }

  // Prevent infinite recursion
  if (depth > 10) {
    return { type: "any" };
  }

  // Check if this is a type reference first
  if (ts.isTypeReferenceNode(typeNode)) {
    const typeName = typeNode.typeName.getText();

    // Don't resolve certain React types
    if (
      typeName === "React.ReactNode" ||
      typeName === "ReactNode" ||
      typeName === "React.ReactElement" ||
      typeName === "ReactElement" ||
      typeName === "JSX.Element" ||
      typeName === "React.FC"
    ) {
      return { type: "type", name: typeName, resolved: null };
    }

    // Get the actual type and resolve it
    const tsType = typeChecker.getTypeFromTypeNode(typeNode);
    const resolved = tsTypeToType(
      tsType,
      typeChecker,
      undefined,
      depth + 1,
      new Set()
    );

    return {
      type: "type",
      name: typeName,
      resolved: resolved.type === "any" ? null : resolved,
    };
  }

  const tsType = typeChecker.getTypeFromTypeNode(typeNode);
  return tsTypeToType(tsType, typeChecker, typeNode, depth, new Set());
}

function tsTypeToType(
  tsType: ts.Type,
  typeChecker: ts.TypeChecker,
  node?: ts.TypeNode,
  depth: number = 0,
  visitedTypes: Set<ts.Type> = new Set()
): Type {
  // Check for infinite recursion
  if (depth > 10 || visitedTypes.has(tsType)) {
    return { type: "any" };
  }

  // Add to visited set
  visitedTypes = new Set(visitedTypes);
  visitedTypes.add(tsType);
  // Handle primitive types
  if (tsType.flags & ts.TypeFlags.String) {
    return { type: "string" };
  }
  if (tsType.flags & ts.TypeFlags.Number) {
    return { type: "number" };
  }
  if (tsType.flags & ts.TypeFlags.Boolean) {
    return { type: "boolean" };
  }
  if (tsType.flags & ts.TypeFlags.Void) {
    return { type: "void" };
  }
  if (tsType.flags & ts.TypeFlags.Undefined) {
    return { type: "undefined" };
  }
  if (tsType.flags & ts.TypeFlags.Null) {
    return { type: "null" };
  }
  if (tsType.flags & ts.TypeFlags.Any) {
    return { type: "any" };
  }
  if (tsType.flags & ts.TypeFlags.Unknown) {
    return { type: "unknown" };
  }

  // Handle literal types
  if (tsType.flags & ts.TypeFlags.StringLiteral) {
    const literal = tsType as ts.StringLiteralType;
    return { type: "literal", value: literal.value };
  }
  if (tsType.flags & ts.TypeFlags.NumberLiteral) {
    const literal = tsType as ts.NumberLiteralType;
    return { type: "literal", value: literal.value };
  }
  if (tsType.flags & ts.TypeFlags.BooleanLiteral) {
    const literal = tsType as any;
    return { type: "literal", value: literal.intrinsicName === "true" };
  }

  // Handle union types - but not optional parameters
  if (tsType.flags & ts.TypeFlags.Union) {
    const unionType = tsType as ts.UnionType;
    const types = unionType.types.map((t) =>
      tsTypeToType(t, typeChecker, undefined, depth + 1, visitedTypes)
    );

    // Don't wrap optional parameter unions
    if (types.length === 2 && types.some((t) => t.type === "undefined")) {
      const nonUndefined = types.find((t) => t.type !== "undefined");
      if (nonUndefined) {
        return nonUndefined;
      }
    }

    return {
      type: "union",
      types,
    };
  }

  // Handle intersection types
  if (tsType.flags & ts.TypeFlags.Intersection) {
    const intersectionType = tsType as ts.IntersectionType;
    return {
      type: "intersection",
      types: intersectionType.types.map((t) =>
        tsTypeToType(t, typeChecker, undefined, depth + 1, visitedTypes)
      ),
    };
  }

  // Handle array types
  const arrayType = typeChecker.getIndexTypeOfType(tsType, ts.IndexKind.Number);
  if (arrayType) {
    return {
      type: "array",
      elementType: tsTypeToType(
        arrayType,
        typeChecker,
        undefined,
        depth + 1,
        visitedTypes
      ),
    };
  }

  // Handle tuple types
  if (
    tsType.symbol &&
    tsType.symbol.name === "__type" &&
    (tsType as any).target
  ) {
    const tupleType = tsType as ts.TupleType;
    if (tupleType.target && tupleType.typeArguments) {
      return {
        type: "tuple",
        elements: tupleType.typeArguments.map((t) =>
          tsTypeToType(t, typeChecker, undefined, depth + 1, visitedTypes)
        ),
      };
    }
  }

  // Handle type references (named types) - check for aliasSymbol first
  if (
    (tsType.aliasSymbol || (node && ts.isTypeReferenceNode(node))) &&
    depth < 5
  ) {
    const typeName = tsType.aliasSymbol
      ? tsType.aliasSymbol.getName()
      : node && ts.isTypeReferenceNode(node)
      ? node.typeName.getText()
      : undefined;

    if (typeName) {
      // Don't resolve certain React types
      if (
        typeName === "React.ReactNode" ||
        typeName === "ReactNode" ||
        typeName === "React.ReactElement" ||
        typeName === "ReactElement" ||
        typeName === "JSX.Element"
      ) {
        return { type: "type", name: typeName, resolved: null };
      }

      // For type aliases, resolve them
      if (tsType.aliasSymbol) {
        const aliasedType = typeChecker.getTypeAtLocation(
          tsType.aliasSymbol.valueDeclaration ||
            tsType.aliasSymbol.declarations?.[0]!
        );
        if (aliasedType && aliasedType !== tsType) {
          return {
            type: "type",
            name: typeName,
            resolved: tsTypeToType(
              aliasedType,
              typeChecker,
              undefined,
              depth + 1,
              visitedTypes
            ),
          };
        }
      }
    }
  }

  // Handle object types
  if (tsType.flags & ts.TypeFlags.Object) {
    const objectType = tsType as ts.ObjectType;

    // Check if it's a function type first
    const callSignatures = objectType.getCallSignatures();
    if (callSignatures.length > 0) {
      const signature = callSignatures[0];
      const params: Param[] = signature.parameters.map((param) => ({
        name: param.getName(),
        type: tsTypeToType(
          typeChecker.getTypeOfSymbolAtLocation(param, param.valueDeclaration!),
          typeChecker,
          undefined,
          depth + 1,
          visitedTypes
        ),
      }));
      const returnType = tsTypeToType(
        signature.getReturnType(),
        typeChecker,
        undefined,
        depth + 1,
        visitedTypes
      );
      return { type: "function", params, returnType };
    }

    const fields: ObjectType["fields"] = {};

    // Get all properties
    const properties = typeChecker.getPropertiesOfType(objectType);
    for (const prop of properties) {
      const propType = typeChecker.getTypeOfSymbolAtLocation(
        prop,
        prop.valueDeclaration!
      );
      const propName = prop.getName();

      // Check if the property has a type alias
      let propertyType: Type;

      // Check if property declaration has a type node
      if (prop.valueDeclaration && "type" in prop.valueDeclaration) {
        const typeNode = (prop.valueDeclaration as any).type;
        if (typeNode && ts.isTypeReferenceNode(typeNode)) {
          // This property uses a type reference
          const typeName = typeNode.typeName.getText();

          // Don't resolve certain React types
          if (
            typeName === "React.ReactNode" ||
            typeName === "ReactNode" ||
            typeName === "React.ReactElement" ||
            typeName === "ReactElement" ||
            typeName === "JSX.Element"
          ) {
            propertyType = { type: "type", name: typeName, resolved: null };
          } else {
            const resolved = tsTypeToType(
              propType,
              typeChecker,
              undefined,
              depth + 1,
              visitedTypes
            );
            propertyType = {
              type: "type",
              name: typeName,
              resolved: resolved.type === "any" ? null : resolved,
            };
          }
        } else if (propType.aliasSymbol) {
          const aliasName = propType.aliasSymbol.getName();
          const aliasedType = typeChecker.getTypeAtLocation(
            propType.aliasSymbol.valueDeclaration ||
              propType.aliasSymbol.declarations?.[0]!
          );
          propertyType = {
            type: "type",
            name: aliasName,
            resolved:
              aliasedType && aliasedType !== propType
                ? tsTypeToType(
                    aliasedType,
                    typeChecker,
                    undefined,
                    depth + 1,
                    visitedTypes
                  )
                : null,
          };
        } else {
          propertyType = tsTypeToType(
            propType,
            typeChecker,
            undefined,
            depth + 1,
            visitedTypes
          );
        }
      } else if (propType.aliasSymbol) {
        const aliasName = propType.aliasSymbol.getName();
        const aliasedType = typeChecker.getTypeAtLocation(
          propType.aliasSymbol.valueDeclaration ||
            propType.aliasSymbol.declarations?.[0]!
        );
        propertyType = {
          type: "type",
          name: aliasName,
          resolved:
            aliasedType && aliasedType !== propType
              ? tsTypeToType(
                  aliasedType,
                  typeChecker,
                  undefined,
                  depth + 1,
                  visitedTypes
                )
              : null,
        };
      } else {
        propertyType = tsTypeToType(
          propType,
          typeChecker,
          undefined,
          depth + 1,
          visitedTypes
        );
      }

      fields[propName] = {
        optional: !!(prop.flags & ts.SymbolFlags.Optional),
        type: propertyType,
      };
    }

    return { type: "object", fields };
  }

  return { type: "any" };
}

export function inferFunctionParams(target: SymbolLocator): Param[] {
  const { program, resolvedPath } = createProgram(target.modulePath);
  const sourceFile = program.getSourceFile(resolvedPath);

  if (!sourceFile) {
    throw new Error(`Could not find source file: ${resolvedPath}`);
  }

  const typeChecker = program.getTypeChecker();
  let targetNode:
    | ts.FunctionDeclaration
    | ts.FunctionExpression
    | ts.ArrowFunction
    | undefined;

  // Find the exported function
  function findExportedNode(node: ts.Node): void {
    if (target.defaultExport) {
      // Handle 'export default function...'
      if (ts.isFunctionDeclaration(node)) {
        const modifiers = ts.getModifiers(node);
        if (modifiers?.some((m) => m.kind === ts.SyntaxKind.DefaultKeyword)) {
          targetNode = node;
          return;
        }
      }
      // Handle 'export default' assignments
      if (ts.isExportAssignment(node) && !node.isExportEquals) {
        const expression = node.expression;
        if (
          ts.isFunctionExpression(expression) ||
          ts.isArrowFunction(expression)
        ) {
          targetNode = expression;
        } else if (ts.isIdentifier(expression)) {
          // Find the actual function declaration
          const symbol = typeChecker.getSymbolAtLocation(expression);
          if (symbol?.valueDeclaration) {
            if (ts.isVariableDeclaration(symbol.valueDeclaration)) {
              const init = symbol.valueDeclaration.initializer;
              if (
                init &&
                (ts.isFunctionExpression(init) || ts.isArrowFunction(init))
              ) {
                targetNode = init;
              }
            } else if (ts.isFunctionDeclaration(symbol.valueDeclaration)) {
              targetNode = symbol.valueDeclaration;
            }
          }
        }
      }
    } else if (target.namedExport) {
      // Handle function declarations
      if (
        ts.isFunctionDeclaration(node) &&
        node.name?.getText() === target.namedExport
      ) {
        const modifiers = ts.getModifiers(node);
        if (modifiers?.some((m) => m.kind === ts.SyntaxKind.ExportKeyword)) {
          targetNode = node;
        }
      }
      // Handle variable statements with function expressions
      if (ts.isVariableStatement(node)) {
        const modifiers = ts.getModifiers(node);
        if (modifiers?.some((m) => m.kind === ts.SyntaxKind.ExportKeyword)) {
          for (const declaration of node.declarationList.declarations) {
            if (
              ts.isIdentifier(declaration.name) &&
              declaration.name.getText() === target.namedExport &&
              declaration.initializer
            ) {
              if (
                ts.isFunctionExpression(declaration.initializer) ||
                ts.isArrowFunction(declaration.initializer)
              ) {
                targetNode = declaration.initializer;
              }
            }
          }
        }
      }
    }
  }

  ts.forEachChild(sourceFile, findExportedNode);

  if (!targetNode) {
    throw new Error(
      `Could not find exported function: ${target.namedExport || "default"}`
    );
  }

  // Extract parameters
  const params: Param[] = [];
  for (const param of targetNode.parameters) {
    const name = param.name.getText();
    const type = typeNodeToType(param.type, typeChecker);
    params.push({ name, type });
  }

  return params;
}

export function inferReactComponentProps(target: SymbolLocator): Param {
  const { program, resolvedPath } = createProgram(target.modulePath);
  const sourceFile = program.getSourceFile(resolvedPath);

  if (!sourceFile) {
    throw new Error(`Could not find source file: ${resolvedPath}`);
  }

  const typeChecker = program.getTypeChecker();
  let propsParam: Param | undefined;

  // Find the exported component
  ts.forEachChild(sourceFile, (node) => {
    if (target.defaultExport) {
      if (ts.isExportAssignment(node) && !node.isExportEquals) {
        const expression = node.expression;
        if (
          ts.isFunctionExpression(expression) ||
          ts.isArrowFunction(expression)
        ) {
          if (expression.parameters.length > 0) {
            const param = expression.parameters[0];
            propsParam = {
              name: param.name.getText(),
              type: typeNodeToType(param.type, typeChecker),
            };
          }
        }
      }
    } else if (target.namedExport) {
      // Handle function declarations
      if (
        ts.isFunctionDeclaration(node) &&
        node.name?.getText() === target.namedExport
      ) {
        const modifiers = ts.getModifiers(node);
        if (modifiers?.some((m) => m.kind === ts.SyntaxKind.ExportKeyword)) {
          if (node.parameters.length > 0) {
            const param = node.parameters[0];
            propsParam = {
              name: param.name.getText(),
              type: typeNodeToType(param.type, typeChecker),
            };
          }
        }
      }
      // Handle variable statements
      if (ts.isVariableStatement(node)) {
        const modifiers = ts.getModifiers(node);
        if (modifiers?.some((m) => m.kind === ts.SyntaxKind.ExportKeyword)) {
          for (const declaration of node.declarationList.declarations) {
            if (
              ts.isIdentifier(declaration.name) &&
              declaration.name.getText() === target.namedExport
            ) {
              // Check if it's typed as React.FC<Props> or similar
              if (
                declaration.type &&
                ts.isTypeReferenceNode(declaration.type)
              ) {
                const typeName = declaration.type.typeName.getText();
                if (
                  typeName === "React.FC" ||
                  typeName === "FC" ||
                  typeName === "React.FunctionComponent" ||
                  typeName === "FunctionComponent"
                ) {
                  // Extract the type argument (the props type)
                  if (
                    declaration.type.typeArguments &&
                    declaration.type.typeArguments.length > 0
                  ) {
                    const propsTypeNode = declaration.type.typeArguments[0];
                    propsParam = {
                      name: "props",
                      type: typeNodeToType(propsTypeNode, typeChecker),
                    };
                  }
                }
              }

              // Otherwise check if it has a function initializer
              if (!propsParam && declaration.initializer) {
                if (
                  ts.isFunctionExpression(declaration.initializer) ||
                  ts.isArrowFunction(declaration.initializer)
                ) {
                  const func = declaration.initializer;
                  if (func.parameters.length > 0) {
                    const param = func.parameters[0];
                    propsParam = {
                      name: param.name.getText(),
                      type: typeNodeToType(param.type, typeChecker),
                    };
                  }
                }
              }
            }
          }
        }
      }
      // Handle interface declarations (for class components)
      if (
        ts.isInterfaceDeclaration(node) &&
        node.name.getText() === target.namedExport
      ) {
        const modifiers = ts.getModifiers(node);
        if (modifiers?.some((m) => m.kind === ts.SyntaxKind.ExportKeyword)) {
          // For interfaces, we'll treat the entire interface as the props type
          const symbol = typeChecker.getSymbolAtLocation(node.name);
          if (symbol) {
            const type = typeChecker.getDeclaredTypeOfSymbol(symbol);
            propsParam = {
              name: "props",
              type: tsTypeToType(type, typeChecker, undefined, 0, new Set()),
            };
          }
        }
      }
    }
  });

  if (!propsParam) {
    throw new Error(
      `Could not find exported component: ${target.namedExport || "default"}`
    );
  }

  return propsParam;
}
