{"name": "@plasmicapp/react-web", "version": "0.2.399", "description": "plasmic library for rendering in the presentational style", "main": "dist/index.cjs.js", "types": "dist/index.d.ts", "module": "dist/react-web.esm.js", "nx": {"targets": {"build": {"inputs": ["{projectRoot}/**/*", "!{projectRoot}/**/dist/**/*", "!{projectRoot}/lib/**/*"], "outputs": ["{projectRoot}/**/dist/**/*", "{projectRoot}/lib/**/*"]}}}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/react-web.esm.js", "require": "./dist/index.cjs.js"}, "./lib/host": {"types": "./lib/host/index.d.ts", "import": "./lib/host/index.js", "require": "./lib/host/index.cjs.js"}, "./lib/data-sources": {"types": "./lib/data-sources/index.d.ts", "import": "./lib/data-sources/index.js", "require": "./lib/data-sources/index.cjs.js"}, "./lib/query": {"types": "./lib/query/index.d.ts", "import": "./lib/query/index.js", "require": "./lib/query/index.cjs.js"}, "./lib/prepass": {"types": "./lib/prepass/index.d.ts", "import": "./lib/prepass/index.js", "require": "./lib/prepass/index.cjs.js"}, "./lib/plasmic.css": "./lib/plasmic.css", "./lib/auth": {"types": "./lib/auth/index.d.ts", "import": "./lib/auth/index.js", "require": "./lib/auth/index.cjs.js"}, "./lib/splits": {"types": "./lib/splits/index.d.ts", "import": "./lib/splits/index.js", "require": "./lib/splits/index.cjs.js"}, "./skinny": {"import": "./skinny/dist/index.js"}, "./skinny/dist/plume/*": {"import": "./skinny/dist/plume/*/index.js"}, "./additional-types-PlasmicImg": {"types": "./dist/render/PlasmicImg/index.d.ts"}, "./lib/nextjs-app-router": {"types": "./lib/nextjs-app-router/index.d.ts", "import": "./lib/nextjs-app-router/index.js", "require": "./lib/nextjs-app-router/index.cjs.js"}, "./lib/nextjs-app-router/react-server": {"types": "./lib/nextjs-app-router/react-server/index.d.ts", "import": "./lib/nextjs-app-router/react-server/index.js", "require": "./lib/nextjs-app-router/react-server/index.cjs.js"}}, "files": ["dist", "lib", "skinny"], "scripts": {"build": "rollup -c && mkdir -p lib && cp src/styles/plasmic.css lib/", "clean": "rm -rf dist/ skinny/dist/ lib/host/*js lib/host/*.ts lib/host/*.map lib/data-sources/*js lib/data-sources/*.ts lib/data-sources/*.map lib/query/*js lib/query/*.ts lib/query/*.map lib/auth/*js lib/auth/*.ts lib/auth/*.map lib/splits/*js lib/splits/*.ts lib/splits/*.map", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test", "lint": "eslint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why", "storybook": "storybook dev -p 6006 --no-open", "build-storybook": "storybook build", "test-storybook": "test-storybook"}, "dependencies": {"@plasmicapp/auth-react": "0.0.23", "@plasmicapp/data-sources": "0.1.188", "@plasmicapp/data-sources-context": "0.1.22", "@plasmicapp/host": "1.0.224", "@plasmicapp/loader-splits": "1.0.64", "@plasmicapp/nextjs-app-router": "1.0.17", "@plasmicapp/prepass": "1.0.20", "@plasmicapp/query": "0.1.80", "@react-aria/checkbox": "^3.15.5", "@react-aria/focus": "^3.20.3", "@react-aria/interactions": "^3.25.1", "@react-aria/listbox": "^3.14.4", "@react-aria/menu": "^3.18.3", "@react-aria/overlays": "^3.27.1", "@react-aria/select": "^3.15.5", "@react-aria/separator": "^3.4.9", "@react-aria/ssr": "^3.9.8", "@react-aria/switch": "^3.7.3", "@react-aria/visually-hidden": "^3.8.23", "@react-stately/collections": "^3.12.4", "@react-stately/list": "^3.12.2", "@react-stately/menu": "^3.9.4", "@react-stately/overlays": "^3.6.16", "@react-stately/select": "^3.6.13", "@react-stately/toggle": "^3.8.4", "@react-stately/tree": "^3.8.10", "classnames": "^2.5.1", "clone": "^2.1.2", "dlv": "^1.1.3", "fast-deep-equal": "^3.1.3", "valtio": "^1.6.3"}, "devDependencies": {"@babel/core": "^7.14.6", "@babel/preset-env": "^7.22.15", "@babel/preset-react": "^7.22.15", "@babel/preset-typescript": "^7.22.15", "@react-types/overlays": "^3.8.15", "@react-types/select": "^3.9.2", "@react-types/shared": "^3.22.1", "@rollup/plugin-commonjs": "^25.0.2", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.1.0", "@size-limit/preset-small-lib": "^4.10.2", "@types/classnames": "^2.3.1", "@types/clone": "^2.1.1", "@types/dlv": "^1.1.2", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "react": "^18.2.0", "react-dom": "^18.2.0", "rollup": "^3.26.1", "rollup-plugin-dts": "^5.3.0", "rollup-plugin-typescript2": "^0.35.0", "size-limit": "^4.10.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}