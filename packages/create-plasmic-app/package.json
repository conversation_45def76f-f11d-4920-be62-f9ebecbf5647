{"name": "create-plasmic-app", "version": "0.0.112", "description": "Create Plasmic-powered React apps", "main": "./dist/lib.js", "types": "./dist/lib.d.ts", "license": "MIT", "engines": {"node": ">=18.0.0"}, "bin": {"create-plasmic-app": "./dist/index.js"}, "scripts": {"test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "coverage": "TEST_CWD=`pwd` yarn --cwd=../.. test --coverage --passWithNoTests", "build": "eslint 'src/**' && tsc", "run-cpa": "yarn build && ts-node run-cpa.ts", "create-plasmic-app": "ts-node src/index.ts", "prepublishOnly": "npm run build"}, "devDependencies": {"@types/findup-sync": "^2.0.2", "@types/glob": "^7.1.3", "@types/inquirer": "^8.2.5", "@types/jest": "^26.0.20", "@types/lodash": "^4.14.168", "@types/node": "^14.14.33", "@types/semver": "^7.3.5", "@types/update-notifier": "^5.0.0", "@types/validate-npm-package-name": "^3.0.2", "@types/yargs": "^16.0.0"}, "dependencies": {"@plasmicapp/cli": "0.1.187", "@sentry/node": "^6.2.2", "chalk": "^4.1.0", "execa": "^5.0.0", "findup-sync": "^4.0.0", "glob": "^7.1.6", "inquirer": "^8.0.0", "lodash": "^4.17.21", "semver": "^7.3.5", "upath": "^2.0.1", "update-notifier": "^5.1.0", "validate-npm-package-name": "^3.0.0", "yargs": "^16.2.0"}}