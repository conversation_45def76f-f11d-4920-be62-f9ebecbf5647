# Create Plasmic App - Development

## Testing

Use `yarn run-cpa` to run create-plasmic-app with presets that gets copied to `cpa-out/` on success.

`yarn run-cpa` will:

1. Let you choose presets to run create-plasmic-app with
2. <PERSON><PERSON> and build create-plasmic-app
3. Sequentially run create-plasmic-app on chosen presets
4. For each preset,
    1. Create a new directory in /tmp/cpa-out/<preset>
    2. Run create-plasmic-app with the preset args
    3. Build the new app
    4. Copy the new app to `packages/create-plasmic-app/cpa-out/<preset>` on success
