## API Report File for "@plasmicapp/loader-react"

> Do not edit this file. It is a report generated by [API Extractor](https://api-extractor.com/).

```ts

/// <reference types="react" />

import { AssetModule } from '@plasmicapp/loader-core';
import type { CodeComponentMeta as CodeComponentMeta_2 } from '@plasmicapp/host';
import { CodeModule } from '@plasmicapp/loader-fetcher';
import type { ComponentHelpers } from '@plasmicapp/host';
import { ComponentMeta } from '@plasmicapp/loader-core';
import { ComponentMeta as ComponentMeta_2 } from '@plasmicapp/loader-fetcher';
import type { CustomFunctionMeta as CustomFunctionMeta_2 } from '@plasmicapp/host';
import { DataCtxReader } from '@plasmicapp/host';
import { DataProvider } from '@plasmicapp/host';
import { FontMeta } from '@plasmicapp/loader-core';
import { getActiveVariation } from '@plasmicapp/loader-splits';
import { getExternalIds } from '@plasmicapp/loader-splits';
import { GlobalActionsContext } from '@plasmicapp/host';
import { GlobalActionsProvider } from '@plasmicapp/host';
import type { GlobalContextMeta as GlobalContextMeta_2 } from '@plasmicapp/host';
import { GlobalGroupMeta } from '@plasmicapp/loader-core';
import { LoaderBundleCache } from '@plasmicapp/loader-core';
import { LoaderBundleOutput } from '@plasmicapp/loader-fetcher';
import { LoaderBundleOutput as LoaderBundleOutput_2 } from '@plasmicapp/loader-core';
import { PageMeta } from '@plasmicapp/loader-core';
import { PageMetadata } from '@plasmicapp/loader-core';
import { PageParamsProvider } from '@plasmicapp/host';
import { plasmicappDataSourcesContext } from '@plasmicapp/data-sources-context';
import { plasmicappHost } from '@plasmicapp/host';
import { plasmicappQuery } from '@plasmicapp/query';
import { PlasmicCanvasContext } from '@plasmicapp/host';
import { PlasmicCanvasHost } from '@plasmicapp/host';
import { PlasmicDataSourceContextValue } from '@plasmicapp/data-sources-context';
import { PlasmicModulesFetcher } from '@plasmicapp/loader-core';
import { PlasmicTracker } from '@plasmicapp/loader-core';
import { PlasmicTranslatorContext } from '@plasmicapp/host';
import { PropType } from '@plasmicapp/host';
import { react } from 'react';
import * as React_2 from 'react';
import { default as React_3 } from 'react';
import { reactDom } from 'react-dom';
import { reactJsxDevRuntime } from 'react/jsx-dev-runtime';
import { reactJsxRuntime } from 'react/jsx-runtime';
import { Registry } from '@plasmicapp/loader-core';
import { repeatedElement } from '@plasmicapp/host';
import { Split } from '@plasmicapp/loader-fetcher';
import type { StateHelpers } from '@plasmicapp/host';
import type { StateSpec } from '@plasmicapp/host';
import { TokenRegistration } from '@plasmicapp/host';
import { TrackRenderOptions } from '@plasmicapp/loader-core';
import { TraitMeta } from '@plasmicapp/host';
import { useDataEnv } from '@plasmicapp/host';
import type { useMutablePlasmicQueryData } from '@plasmicapp/query';
import { usePlasmicCanvasComponentInfo } from '@plasmicapp/host';
import { usePlasmicCanvasContext } from '@plasmicapp/host';
import { usePlasmicQueryData } from '@plasmicapp/query';
import { useSelector } from '@plasmicapp/host';
import { useSelectors } from '@plasmicapp/host';

// @public (undocumented)
export type CodeComponentMeta<P> = Omit<CodeComponentMeta_2<P>, "importPath" | "componentHelpers" | "states"> & {
    importPath?: string;
    states?: Record<string, StateSpec<P> & StateHelpers<P, any>>;
    getServerInfo?: (props: P, ops: ReactServerOps) => ServerInfo;
};

// @public (undocumented)
export type ComponentLookupSpec = string | {
    name: string;
    projectId?: string;
    isCode?: boolean;
};

export { ComponentMeta }

// @public (undocumented)
export interface ComponentRenderData {
    // (undocumented)
    bundle: LoaderBundleOutput;
    // (undocumented)
    entryCompMetas: (ComponentMeta_2 & {
        params?: Record<string, string>;
    })[];
    // (undocumented)
    remoteFontUrls: string[];
}

// @public (undocumented)
export const convertBundlesToComponentRenderData: (bundles: LoaderBundleOutput_2[], compMetas: ComponentMeta[]) => ComponentRenderData | null;

// @public (undocumented)
export type CustomFunctionMeta<F extends (...args: any[]) => any> = Omit<CustomFunctionMeta_2<F>, "importPath"> & {
    importPath?: string;
};

export { DataCtxReader }

export { DataProvider }

// @public
export function extractPlasmicQueryData(element: React.ReactElement): Promise<Record<string, any>>;

// @public (undocumented)
export function extractPlasmicQueryDataFromElement(loader: PlasmicComponentLoader, lookup: ComponentLookupSpec, opts?: {
    prefetchedData?: ComponentRenderData;
    componentProps?: any;
    globalVariants?: GlobalVariantSpec[];
    prefetchedQueryData?: Record<string, any>;
}): Promise<Record<string, any>>;

// @public (undocumented)
export interface FetchComponentDataOpts {
    target?: "server" | "browser";
}

export { GlobalActionsContext }

export { GlobalActionsProvider }

// @public (undocumented)
export type GlobalContextMeta<P> = Omit<GlobalContextMeta_2<P>, "importPath"> & {
    importPath?: string;
};

// @public (undocumented)
export interface GlobalVariantSpec {
    // (undocumented)
    name: string;
    // (undocumented)
    projectId?: string;
    // (undocumented)
    value: any;
}

// @public (undocumented)
export function hydrateFromElement(loader: PlasmicComponentLoader, target: HTMLElement, lookup: ComponentLookupSpec, opts?: {
    prefetchedData?: ComponentRenderData;
    componentProps?: any;
    globalVariants?: GlobalVariantSpec[];
    prefetchedQueryData?: Record<string, any>;
}): Promise<void>;

// @public (undocumented)
export interface InitOptions {
    alwaysFresh?: boolean;
    // (undocumented)
    cache?: LoaderBundleCache;
    // (undocumented)
    host?: string;
    // (undocumented)
    i18n?: {
        keyScheme: "content" | "hash" | "path";
        tagPrefix?: string;
    };
    // @deprecated (undocumented)
    i18nKeyScheme?: "content" | "hash";
    manualRedirect?: boolean;
    nativeFetch?: boolean;
    // (undocumented)
    onClientSideFetch?: "warn" | "error";
    // (undocumented)
    platform?: "react" | "nextjs" | "gatsby";
    // (undocumented)
    platformOptions?: {
        nextjs?: {
            appDir: boolean;
        };
    };
    // (undocumented)
    preview?: boolean;
    // (undocumented)
    projects: {
        id: string;
        token: string;
        version?: string;
    }[];
    skipHead?: boolean;
}

// @public (undocumented)
export function initPlasmicLoader(opts: InitOptions): PlasmicComponentLoader;

// @public (undocumented)
export class InternalPlasmicComponentLoader extends BaseInternalPlasmicComponentLoader {
    constructor(opts: InitOptions);
    // (undocumented)
    refreshRegistry(): void;
    // (undocumented)
    registerComponent<T extends React_3.ComponentType<any>>(component: T, meta: CodeComponentMeta<React_3.ComponentProps<T>>): void;
    // (undocumented)
    registerFunction<F extends (...args: any[]) => any>(fn: F, meta: CustomFunctionMeta<F>): void;
    // (undocumented)
    registerGlobalContext<T extends React_3.ComponentType<any>>(context: T, meta: GlobalContextMeta<React_3.ComponentProps<T>>): void;
    // (undocumented)
    registerToken(token: TokenRegistration): void;
    // (undocumented)
    registerTrait(trait: string, meta: TraitMeta): void;
    // (undocumented)
    subscribePlasmicRoot(watcher: PlasmicRootWatcher): void;
    // (undocumented)
    unsubscribePlasmicRoot(watcher: PlasmicRootWatcher): void;
}

// @public
export function matchesPagePath(pattern: string, path: string): false | {
    params: Record<string, string | string[]>;
};

export { PageMeta }

export { PageMetadata }

export { PageParamsProvider }

export { PlasmicCanvasContext }

export { PlasmicCanvasHost }

// @public (undocumented)
export function PlasmicComponent(props: {
    component: string;
    projectId?: string;
    forceOriginal?: boolean;
    componentProps?: any;
}): React_2.ReactElement | null;

// @public
export class PlasmicComponentLoader {
    constructor(internal: BaseInternalPlasmicComponentLoader);
    // (undocumented)
    clearCache(): void;
    fetchComponentData(...specs: ComponentLookupSpec[]): Promise<ComponentRenderData>;
    // (undocumented)
    fetchComponentData(specs: ComponentLookupSpec[], opts?: FetchComponentDataOpts): Promise<ComponentRenderData>;
    fetchComponents(): Promise<ComponentMeta_2[]>;
    fetchPages(opts?: FetchPagesOpts): Promise<PageMeta[]>;
    // (undocumented)
    getActiveSplits(): Split[];
    // (undocumented)
    getActiveVariation(opts: {
        known?: Record<string, string>;
        traits: Record<string, string | number | boolean>;
    }): Promise<Record<string, string>>;
    // (undocumented)
    protected _getActiveVariation(opts: Parameters<typeof PlasmicComponentLoader.__internal.getActiveVariation>[0]): Promise<Record<string, string>>;
    // (undocumented)
    getChunksUrl(bundle: LoaderBundleOutput, modules: CodeModule[]): string;
    // (undocumented)
    getExternalVariation(variation: Record<string, string>, filters?: Parameters<typeof getExternalIds>[2]): Record<string, string>;
    maybeFetchComponentData(...specs: ComponentLookupSpec[]): Promise<ComponentRenderData | null>;
    // (undocumented)
    maybeFetchComponentData(specs: ComponentLookupSpec[], opts?: FetchComponentDataOpts): Promise<ComponentRenderData | null>;
    registerComponent<T extends React.ComponentType<any>>(component: T, meta: CodeComponentMeta<React.ComponentProps<T>>): void;
    registerComponent<T extends React.ComponentType<any>>(component: T, name: ComponentLookupSpec): void;
    // (undocumented)
    registerFunction<F extends (...args: any[]) => any>(fn: F, meta: CustomFunctionMeta<F>): void;
    // (undocumented)
    registerGlobalContext<T extends React.ComponentType<any>>(context: T, meta: GlobalContextMeta<React.ComponentProps<T>>): void;
    // (undocumented)
    registerModules(modules: Record<string, any>): void;
    // (undocumented)
    registerToken(token: TokenRegistration): void;
    // (undocumented)
    registerTrait(trait: string, meta: TraitMeta): void;
    setGlobalVariants(globalVariants: GlobalVariantSpec[]): void;
    substituteComponent<P>(component: React.ComponentType<P>, name: ComponentLookupSpec): void;
    // (undocumented)
    trackConversion(value?: number): void;
    // (undocumented)
    unstable__getServerQueriesData(renderData: ComponentRenderData, $ctx: Record<string, any>): Promise<any>;
}

// @public @deprecated (undocumented)
export function plasmicPrepass(element: React.ReactElement): Promise<void>;

// @public
export function PlasmicRootProvider(props: {
    loader: PlasmicComponentLoader;
    globalVariants?: GlobalVariantSpec[];
    children?: React_2.ReactNode;
    skipCss?: boolean;
    skipFonts?: boolean;
    prefetchedData?: ComponentRenderData;
    prefetchedQueryData?: Record<string, any>;
    suspenseForQueryData?: boolean;
    globalContextsProps?: Record<string, any>;
    variation?: Record<string, string>;
    translator?: PlasmicTranslator;
    Head?: React_2.ComponentType<any>;
    Link?: React_2.ComponentType<any>;
    pageRoute?: string;
    pageParams?: Record<string, string | string[] | undefined>;
    pageQuery?: Record<string, string | string[] | undefined>;
    disableLoadingBoundary?: boolean;
    disableRootLoadingBoundary?: boolean;
    suspenseFallback?: React_2.ReactNode;
} & PlasmicDataSourceContextValue): React_2.JSX.Element;

// @public (undocumented)
export type PlasmicTranslator = (str: string, opts?: {
    components?: {
        [key: string]: React_2.ReactElement | React_2.ReactFragment;
    };
}) => React_2.ReactNode;

export { PlasmicTranslatorContext }

export { PropType }

// @public (undocumented)
export function renderToElement(loader: PlasmicComponentLoader, target: HTMLElement, lookup: ComponentLookupSpec, opts?: {
    prefetchedData?: ComponentRenderData;
    componentProps?: any;
    globalVariants?: GlobalVariantSpec[];
    prefetchedQueryData?: Record<string, any>;
    pageParams?: Record<string, any>;
    pageQuery?: Record<string, any>;
}): Promise<void>;

// @public (undocumented)
export function renderToString(loader: PlasmicComponentLoader, lookup: ComponentLookupSpec, opts?: {
    prefetchedData?: ComponentRenderData;
    componentProps?: any;
    globalVariants?: GlobalVariantSpec[];
    prefetchedQueryData?: Record<string, any>;
}): string;

export { repeatedElement }

export { TokenRegistration }

export { useDataEnv }

export { usePlasmicCanvasComponentInfo }

export { usePlasmicCanvasContext }

// @public
export function usePlasmicComponent<P extends React_2.ComponentType = any>(spec: ComponentLookupSpec, opts?: {
    forceOriginal?: boolean;
}): P;

export { usePlasmicQueryData }

export { useSelector }

export { useSelectors }

// (No @packageDocumentation comment for this package)

```
