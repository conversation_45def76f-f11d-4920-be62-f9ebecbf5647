#!/usr/bin/env python3

# GET routes that perform DB writes (need withTransaction)
WRITE_GET_ROUTES = {
    "/api/v1/app-auth/token",
    "/api/v1/app-ctx",
    "/api/v1/auth/oidc/guidewire-apac/callback",
    "/api/v1/auth/oidc/guidewire-eu/callback",
    "/api/v1/auth/oidc/guidewire/callback",
    "/api/v1/auth/oidc/test/callback",
    "/api/v1/hosting-hit",
    "/api/v1/oauth2/authorize",
    "/api/v1/saml/sso/:tenantId",
    "/api/v1/auth/google/callback",
    "/api/v1/auth/sso/test/callback",
    "/api/v1/auth/sso/:tenantId",
    "/api/v1/auth/google-sheets/callback",
    "/api/v1/auth/airtable/callback",
    "/api/v1/auth/email-verification",
    "/api/v1/auth/email-verification-link",
    "/api/v1/auth/confirmation",
    "/api/v1/auth/reset/:resetPasswordToken",
    "/api/v1/auth/logout",
    "/api/v1/auth/forgot",
    "/api/v1/data/source/:dataSourceId/execute/:operationName",
    "/api/v1/actions/:actionId/execute",
    "/api/v1/cmse/views",
    "/api/v1/authorize/viewer",
    "/api/v1/analytics/:teamId",
    "/api/v1/github/install",
    "/api/v1/copilot-feedback",
    "/api/v1/billing/subscription/:teamId"
}

import re
import sys

def check_missing_transactions(filepath):
    with open(filepath, 'r') as f:
        content = f.read()
    
    missing = []
    for route_path in WRITE_GET_ROUTES:
        # Escape the route path for regex
        escaped_route = re.escape(route_path)
        # Search for this route with app.get
        pattern = rf'app\.get\s*\(\s*["\']' + escaped_route + r'["\'][^,]*,'
        if re.search(pattern, content):
            # Check if it has withTransaction
            trans_pattern = rf'app\.get\s*\(\s*["\']' + escaped_route + r'["\'][^)]*withTransaction\s*\('
            if not re.search(trans_pattern, content):
                missing.append(route_path)
    
    if missing:
        print("GET routes that write to DB but are NOT using withTransaction:")
        for route in missing:
            print(f"  {route}")
    else:
        print("All GET routes that write to DB are properly using withTransaction in AppServer.ts")
    
    return missing

if __name__ == "__main__":
    missing = check_missing_transactions("/workspace/platform/wab/src/wab/server/AppServer.ts")
    
    # Also check custom-routes.ts for the hosting-hit route
    with open("/workspace/platform/wab/src/wab/server/routes/custom-routes.ts", 'r') as f:
        content = f.read()
    
    if '/api/v1/hosting-hit' in WRITE_GET_ROUTES:
        pattern = r'app\.get\s*\(\s*["\']\/api\/v1\/hosting-hit["\'][^)]*\)'
        if re.search(pattern, content):
            trans_pattern = r'app\.get\s*\(\s*["\']\/api\/v1\/hosting-hit["\'][^)]*withTransaction\s*\('
            if re.search(trans_pattern, content):
                print("✓ /api/v1/hosting-hit in custom-routes.ts has withTransaction")
            else:
                print("✗ /api/v1/hosting-hit in custom-routes.ts MISSING withTransaction")
