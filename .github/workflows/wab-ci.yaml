name: WAB CI

on:
  pull_request:
    types:
      - opened
      - synchronize
      - reopened
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  BUILD_NUMBER: ${{ github.run_id }}
  VERDACCIO_PORT: "4873"
  VERDACCIO_HOST: localhost

permissions:
  contents: read
  id-token: write
  pull-requests: read

jobs:
  check-unused:
    runs-on: arc-runner-set-4cpus-16gb
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: 24.4.0

      - run: npm i -g yarn

      - name: Install Root dependencies
        uses: nick-fields/retry@v3
        with:
          command: yarn
          max_attempts: 3
          timeout_minutes: 10

      - name: Install Wab dependencies
        uses: nick-fields/retry@v3
        with:
          command: |
            cd platform/wab
            yarn
          max_attempts: 3
          timeout_minutes: 10

      - name: Run Wab Make
        working-directory: platform/wab
        run: make

      - name: Check unused deps
        run: yarn knip:deps

  changes:
    runs-on: ubuntu-latest
    outputs:
      loader_tests: ${{ steps.filter.outputs.loader_tests }}
      studio_tests: ${{ steps.filter.outputs.studio_tests }}
      storybook_tests: ${{ steps.filter.outputs.storybook_tests }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 100
          fetch-tags: true

      - name: Filter files changed
        uses: dorny/paths-filter@v3
        id: filter
        with:
          base: master
          initial-fetch-depth: 50
          filters: |
            loader_tests:
              - ".github/workflows/wab-ci.yaml"
              - "packages/**"
              - "plasmicpkgs/**"
              - "platform/loader-tests/**"
              - "platform/loader-html-hydrate/**"
              - "platform/loader-bundle-env/**"
              - "platform/wab/**"
            studio_tests:
              - ".github/workflows/wab-ci.yaml"
              - "packages/**"
              - "plasmicpkgs/**"
              - "platform/live-frame/**"
              - "platform/sub/**"
              - "platform/wab/**"
            storybook_tests:
              - ".github/workflows/wab-ci.yaml"
              - "packages/react-web/**"
              - "plasmicpkgs/antd5/**"
              - "plasmicpkgs/plasmic-basic-components/**"
              - "plasmicpkgs/react-aria/**"

  storybook-tests:
    needs: [changes]
    if: ${{ needs.changes.outputs.storybook_tests == 'true' }}
    runs-on: arc-runner-set-tests-storybook
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: 24.4.0

      - name: Install yarn
        run: npm i -g yarn

      - name: Install Root dependencies
        uses: nick-fields/retry@v3
        with:
          command: yarn
          max_attempts: 3
          timeout_minutes: 10

      - name: Install Playwright
        run: yarn playwright install chromium

      - name: Build packages
        run: |
          yarn lerna run build \
            --scope=@plasmicapp/react-web  \
            --scope=@plasmicpkgs/antd5 \
            --scope=@plasmicpkgs/plasmic-basic-components \
            --scope=@plasmicpkgs/react-aria

      - name: Run storybook tests for @plasmicapp/react-web
        working-directory: packages/react-web
        run: |
          yarn build-storybook --quiet
          npx concurrently -k -s first -n "SB,TEST" -c "magenta,blue" \
            "npx http-server storybook-static --port 6006 --silent" \
            "npx wait-on tcp:127.0.0.1:6006 --timeout 60000 && yarn test-storybook"

      - name: Run storybook tests for @plasmicpkgs/antd5
        working-directory: plasmicpkgs/antd5
        run: |
          yarn build-storybook --quiet
          npx concurrently -k -s first -n "SB,TEST" -c "magenta,blue" \
            "npx http-server storybook-static --port 6006 --silent" \
            "npx wait-on tcp:127.0.0.1:6006 --timeout 60000 && yarn test-storybook"

      - name: Run storybook tests for @plasmicpkgs/plasmic-basic-components
        working-directory: plasmicpkgs/plasmic-basic-components
        run: |
          yarn build-storybook --quiet
          npx concurrently -k -s first -n "SB,TEST" -c "magenta,blue" \
            "npx http-server storybook-static --port 6006 --silent" \
            "npx wait-on tcp:127.0.0.1:6006 --timeout 60000 && yarn test-storybook"

      - name: Run storybook tests for @plasmicpkgs/react-aria
        working-directory: plasmicpkgs/react-aria
        run: |
          yarn build-storybook --quiet
          npx concurrently -k -s first -n "SB,TEST" -c "magenta,blue" \
            "npx http-server storybook-static --port 6006 --silent" \
            "npx wait-on tcp:127.0.0.1:6006 --timeout 60000 && yarn test-storybook"

  loader-tests-playwright:
    env:
      NPM_CONFIG_REGISTRY: http://localhost:4873
      YARN_REGISTRY: http://localhost:4873
    needs: [changes]
    if: ${{ needs.changes.outputs.loader_tests == 'true' }}
    runs-on: arc-runner-set-loader-tests-playwright
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USER }}
          password: ${{ secrets.DOCKER_HUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: Authenticate with GCP
        id: auth
        uses: "google-github-actions/auth@v2"
        with:
          token_format: access_token
          service_account: <EMAIL>
          project_id: registries-442916
          workload_identity_provider: projects/************/locations/global/workloadIdentityPools/github-actions/providers/github-actions

      - name: Login to GCR
        uses: docker/login-action@v3
        with:
          registry: us-central1-docker.pkg.dev
          username: oauth2accesstoken
          password: ${{ steps.auth.outputs.access_token }}

      - name: Set up docker compose
        run: |
          # Remove deprecated Google's GPG Key
          wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | gpg --dearmor | sudo tee /etc/apt/trusted.gpg.d/google.gpg >/dev/null
          # Add Docker's official GPG key:
          sudo apt-get update
          sudo apt-get install -y ca-certificates curl
          sudo install -m 0755 -d /etc/apt/keyrings
          sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
          sudo chmod a+r /etc/apt/keyrings/docker.asc

          # Add the repository to Apt sources:
          echo \
            "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
            $(. /etc/os-release && echo "$VERSION_CODENAME") stable" | \
            sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
          sudo apt-get update
          sudo apt-get install -y docker-compose-plugin

      - name: Install xvfb for cypress
        run: sudo apt-get install -y xvfb

      - name: Remove IPv6 localhost from /etc/hosts
        run: |
          # Cannot directly sed -i the file since it is managed by Docker, need to write into it instead of moving it
          cat /etc/hosts | grep -v '::1' | tee /tmp/hosts.new
          sudo cp -f /tmp/hosts.new /etc/hosts

      - name: Set pgpass
        run: |
          cp host-pgpass ~/.pgpass
          chmod 600 ~/.pgpass

      - name: Spinup DB
        run: docker compose -f devops/cicd/docker-compose.services.yaml up -d

      - uses: actions/setup-node@v4
        with:
          node-version: 24.4.0

      - name: Install yarn
        run: npm i -g yarn

      - name: Install pm2
        run: npm i -g pm2

      - name: Install Root dependencies
        uses: nick-fields/retry@v3
        with:
          command: yarn
          max_attempts: 3
          timeout_minutes: 10

      - name: Build packages
        run: yarn lerna run build --since

      - name: Publish to verdaccio
        run: yarn lerna publish patch --ignore-scripts --no-git-reset --no-push --no-git-tag-version --yes

      - name: Install Wab dependencies
        uses: nick-fields/retry@v3
        with:
          command: |
            cd platform/wab
            yarn
          max_attempts: 3
          timeout_minutes: 10

      - name: Run Wab Make
        working-directory: platform/wab
        run: make

      - name: Run migrations
        working-directory: platform/wab
        run: yarn typeorm migration:run

      - name: Run seed
        working-directory: platform/wab
        run: yarn seed

      - name: Upgrade internal
        uses: nick-fields/retry@v3
        with:
          command: bash scripts/upgrade-internal.bash
          max_attempts: 3
          timeout_minutes: 10

      - name: Run Setup All
        uses: nick-fields/retry@v3
        with:
          command: yarn setup-all
          max_attempts: 3
          timeout_minutes: 10

      - name: Start services
        working-directory: platform/wab
        run: pm2 start pm2-dev.config.js --wait-ready

      - name: Install loader tests dependencies
        uses: nick-fields/retry@v3
        with:
          command: |
            cd platform/loader-tests
            yarn
          max_attempts: 3
          timeout_minutes: 10

      - name: Run tests
        working-directory: platform/loader-tests
        run: WAB_HOST=http://localhost:3004 yarn test-playwright
        env:
          DISPLAY: ":99"

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        if: ${{ always() }}
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: Upload test results
        working-directory: platform/loader-tests
        if: ${{ always() }}
        run: |
          zip -r playwright-test-results.zip test-results
          aws s3 cp playwright-test-results.zip "s3://plasmic-cypress/${{ github.run_id }}/playwright-test-results.zip" --acl public-read
          echo "https://plasmic-cypress.s3-us-west-2.amazonaws.com/${{ github.run_id }}/playwright-test-results.zip"

  loader-tests-jest:
    env:
      NPM_CONFIG_REGISTRY: http://localhost:4873
      YARN_REGISTRY: http://localhost:4873
    needs: [changes]
    if: ${{ needs.changes.outputs.loader_tests == 'true' }}
    runs-on: arc-runner-set-loader-tests-jest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USER }}
          password: ${{ secrets.DOCKER_HUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: Authenticate with GCP
        id: auth
        uses: "google-github-actions/auth@v2"
        with:
          token_format: access_token
          service_account: <EMAIL>
          project_id: registries-442916
          workload_identity_provider: projects/************/locations/global/workloadIdentityPools/github-actions/providers/github-actions

      - name: Login to GCR
        uses: docker/login-action@v3
        with:
          registry: us-central1-docker.pkg.dev
          username: oauth2accesstoken
          password: ${{ steps.auth.outputs.access_token }}

      - name: Set up docker compose
        run: |
          # Remove deprecated Google's GPG Key
          wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | gpg --dearmor | sudo tee /etc/apt/trusted.gpg.d/google.gpg >/dev/null
          # Add Docker's official GPG key:
          sudo apt-get update
          sudo apt-get install -y ca-certificates curl
          sudo install -m 0755 -d /etc/apt/keyrings
          sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
          sudo chmod a+r /etc/apt/keyrings/docker.asc

          # Add the repository to Apt sources:
          echo \
            "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
            $(. /etc/os-release && echo "$VERSION_CODENAME") stable" | \
            sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
          sudo apt-get update
          sudo apt-get install -y docker-compose-plugin

      - name: Install xvfb for cypress
        run: sudo apt-get install -y xvfb

      # Xvfb is a cypress dependency used to control virtual screens.
      # Initiating it like this avoids each cypress run to spin up an instance of it
      # which avoids each instance of the Xvfb process to clash with each other.
      #
      # This way, all cypress instances will share the same Xvfb process, similar to what
      # happens with network ports and connections.
      - name: Start Xvfb
        run: Xvfb :99 &

      - name: Remove IPv6 localhost from /etc/hosts
        run: |
          # Cannot directly sed -i the file since it is managed by Docker, need to write into it instead of moving it
          cat /etc/hosts | grep -v '::1' | tee /tmp/hosts.new
          sudo cp -f /tmp/hosts.new /etc/hosts

      - name: Set pgpass
        run: |
          cp host-pgpass ~/.pgpass
          chmod 600 ~/.pgpass

      - name: Spinup DB
        run: docker compose -f devops/cicd/docker-compose.services.yaml up -d

      - uses: actions/setup-node@v4
        with:
          node-version: 22.17.1 # Gatsby is not compatible with Node 24

      - name: Install yarn
        run: npm i -g yarn

      - name: Install pm2
        run: npm i -g pm2

      - name: Install Root dependencies
        uses: nick-fields/retry@v3
        with:
          command: yarn
          max_attempts: 3
          timeout_minutes: 10

      - name: Build packages
        run: yarn lerna run build --since

      - name: Publish to verdaccio
        run: yarn lerna publish patch --ignore-scripts --no-git-reset --no-push --no-git-tag-version --yes

      - name: Install Wab dependencies
        uses: nick-fields/retry@v3
        with:
          command: |
            cd platform/wab
            yarn
          max_attempts: 3
          timeout_minutes: 10

      - name: Run Wab Make
        working-directory: platform/wab
        run: make

      - name: Run migrations
        working-directory: platform/wab
        run: yarn typeorm migration:run

      - name: Run seed
        working-directory: platform/wab
        run: yarn seed

      - name: Upgrade internal
        uses: nick-fields/retry@v3
        with:
          command: bash scripts/upgrade-internal.bash
          max_attempts: 3
          timeout_minutes: 10

      - name: Run Setup All
        uses: nick-fields/retry@v3
        with:
          command: yarn setup-all
          max_attempts: 3
          timeout_minutes: 10

      - name: Start services
        working-directory: platform/wab
        run: pm2 start pm2-dev.config.js --wait-ready

      - name: Install loader tests dependencies
        uses: nick-fields/retry@v3
        with:
          command: |
            cd platform/loader-tests
            yarn
          max_attempts: 3
          timeout_minutes: 10

      - name: Run tests
        working-directory: platform/loader-tests
        run: WAB_HOST=http://localhost:3004 yarn jest
        env:
          DISPLAY: ":99"

      - name: Stop Xvfb
        run: pkill Xvfb

  studio-tests-root:
    env:
      NPM_CONFIG_REGISTRY: http://localhost:4873
      YARN_REGISTRY: http://localhost:4873
    needs: [changes]
    # use always() function in conjunction with the platform-changes filter output allows the
    # job to run when there are changes in the dependent files but no changes in the packages
    # without the always() directive this job would be skipped even if there were changes that
    # require it to run, but no package was build and released.
    if: ${{ needs.changes.outputs.studio_tests == 'true' }}
    runs-on: arc-runner-set-loader-tests-root
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USER }}
          password: ${{ secrets.DOCKER_HUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: Authenticate with GCP
        id: auth
        uses: "google-github-actions/auth@v2"
        with:
          token_format: access_token
          service_account: <EMAIL>
          project_id: registries-442916
          workload_identity_provider: projects/************/locations/global/workloadIdentityPools/github-actions/providers/github-actions

      - name: Login to GCR
        uses: docker/login-action@v3
        with:
          registry: us-central1-docker.pkg.dev
          username: oauth2accesstoken
          password: ${{ steps.auth.outputs.access_token }}

      - name: Set up docker compose
        run: |
          # Remove deprecated Google's GPG Key
          wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | gpg --dearmor | sudo tee /etc/apt/trusted.gpg.d/google.gpg >/dev/null
          # Add Docker's official GPG key:
          sudo apt-get update
          sudo apt-get install -y ca-certificates curl
          sudo install -m 0755 -d /etc/apt/keyrings
          sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
          sudo chmod a+r /etc/apt/keyrings/docker.asc

          # Add the repository to Apt sources:
          echo \
            "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
            $(. /etc/os-release && echo "$VERSION_CODENAME") stable" | \
            sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
          sudo apt-get update
          sudo apt-get install -y docker-compose-plugin

      - name: Set pgpass
        run: |
          cp host-pgpass ~/.pgpass
          chmod 600 ~/.pgpass

      - name: Spinup DB
        run: docker compose -f devops/cicd/docker-compose.services.yaml up -d

      - uses: actions/setup-node@v4
        with:
          node-version: 24.4.0

      - name: Install yarn
        run: npm i -g yarn

      - name: Install Root dependencies
        uses: nick-fields/retry@v3
        with:
          command: yarn
          max_attempts: 3
          timeout_minutes: 10

      - name: Build packages
        run: yarn lerna run build --since

      - name: Publish to verdaccio
        run: yarn lerna publish patch --ignore-scripts --no-git-reset --no-push --no-git-tag-version --yes

      - name: Upgrade internal
        uses: nick-fields/retry@v3
        with:
          command: bash scripts/upgrade-internal.bash
          max_attempts: 3
          timeout_minutes: 10

      - name: Wab Make
        working-directory: platform/wab
        run: make

      - name: Install Wab dependencies
        uses: nick-fields/retry@v3
        with:
          command: |
            cd platform/wab
            yarn
          max_attempts: 3
          timeout_minutes: 10

      - name: Run db migrations
        working-directory: platform/wab
        run: yarn typeorm migration:run

      - name: Run Seed
        working-directory: platform/wab
        run: yarn seed

      - name: Run type-checking tests
        working-directory: platform/wab
        run: |
          NODE_OPTIONS='--max-old-space-size=8192' yarn run tsc

      - name: Run linting tests
        working-directory: platform/wab
        run: |
          NODE_OPTIONS='--max-old-space-size=4096' yarn eslint-all --quiet

      - name: Run jest test
        working-directory: platform/wab
        run: |
          NODE_OPTIONS=--max_old_space_size=8192 yarn test

  studio-test-cypress:
    env:
      NPM_CONFIG_REGISTRY: http://localhost:4873
      YARN_REGISTRY: http://localhost:4873
    needs: [changes]
    if: ${{ needs.changes.outputs.studio_tests == 'true' }}
    runs-on: arc-runner-set-cypress
    timeout-minutes: 60
    strategy:
      fail-fast: false
      matrix:
        # to increase parallelism just add more sequential numbers to the array.
        # example: [1, 2, 3, 4, 5, 6] will split the tests in 6 machines.
        containers: [1, 2, 3, 4, 5, 6, 7, 8]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USER }}
          password: ${{ secrets.DOCKER_HUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Authenticate with GCP
        id: auth
        uses: "google-github-actions/auth@v2"
        with:
          token_format: access_token
          service_account: <EMAIL>
          project_id: registries-442916
          workload_identity_provider: projects/************/locations/global/workloadIdentityPools/github-actions/providers/github-actions

      - name: Login to GCR
        uses: docker/login-action@v3
        with:
          registry: us-central1-docker.pkg.dev
          username: oauth2accesstoken
          password: ${{ steps.auth.outputs.access_token }}

      - name: Set up docker compose
        run: |
          # Remove deprecated Google's GPG Key
          wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | gpg --dearmor | sudo tee /etc/apt/trusted.gpg.d/google.gpg >/dev/null
          # Add Docker's official GPG key:
          sudo apt-get update
          sudo apt-get install -y ca-certificates curl
          sudo install -m 0755 -d /etc/apt/keyrings
          sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
          sudo chmod a+r /etc/apt/keyrings/docker.asc

          # Add the repository to Apt sources:
          echo \
            "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
            $(. /etc/os-release && echo "$VERSION_CODENAME") stable" | \
            sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
          sudo apt-get update
          sudo apt-get install -y docker-compose-plugin

      - name: Remove IPv6 localhost from /etc/hosts
        run: |
          # Cannot directly sed -i the file since it is managed by Docker, need to write into it instead of moving it
          cat /etc/hosts | grep -v '::1' | tee /tmp/hosts.new
          sudo cp -f /tmp/hosts.new /etc/hosts

      - name: Set pgpass
        run: |
          cp host-pgpass ~/.pgpass
          chmod 600 ~/.pgpass

      - name: Spinup DB
        run: docker compose -f devops/cicd/docker-compose.services.yaml up -d

      - uses: actions/setup-node@v4
        with:
          node-version: 24.4.0

      - name: Install yarn
        run: npm i -g yarn

      - name: Install pm2
        run: npm i -g pm2

      - name: Install Root dependencies
        uses: nick-fields/retry@v3
        with:
          command: yarn
          max_attempts: 3
          timeout_minutes: 10

      - name: Build packages
        run: yarn lerna run build --since

      - name: Publish to verdaccio
        run: yarn lerna publish patch --ignore-scripts --no-git-reset --no-push --no-git-tag-version --yes

      - name: Install Wab dependencies
        uses: nick-fields/retry@v3
        with:
          command: |
            cd platform/wab
            yarn
          max_attempts: 3
          timeout_minutes: 10

      - name: Run Wab Make
        working-directory: platform/wab
        run: make

      - name: Run migrations
        working-directory: platform/wab
        run: yarn typeorm migration:run

      - name: Run seed
        working-directory: platform/wab
        run: yarn seed

      - name: Upgrade internal
        uses: nick-fields/retry@v3
        with:
          command: bash scripts/upgrade-internal.bash
          max_attempts: 3
          timeout_minutes: 10

      - name: Run Setup All
        uses: nick-fields/retry@v3
        with:
          command: yarn setup-all
          max_attempts: 3
          timeout_minutes: 10

      - name: Install host-test dependencies
        working-directory: platform/host-test
        run: yarn

      - name: Build host-test
        working-directory: platform/host-test
        run: yarn build

      - name: Start Dev Services
        working-directory: platform/wab
        run: pm2 start pm2-dev.config.js --wait-ready

      - name: Start Test Only Services
        working-directory: platform/wab
        run: pm2 start pm2-test.config.js --wait-ready

      - name: Run split Cypress tests 🧪
        working-directory: platform/wab
        run: yarn cypress run --browser chrome
        # pass the machine index and the total number
        env:
          SPLIT: ${{ strategy.job-total }}
          SPLIT_INDEX: ${{ strategy.job-index }}
          CYPRESS_CUSTOM_HOST_PORT: "3011"
          CYPRESS_trashAssetsBeforeRuns: "false"
          failFast: "true"

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        if: ${{ always() }}
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: Upload test results
        working-directory: platform/wab
        if: ${{ always() }}
        run: |
          zip -r test-results-${{ strategy.job-index }}.zip cypress/videos cypress/screenshots
          aws s3 cp "test-results-${{ strategy.job-index }}.zip" "s3://plasmic-cypress/cypress/${{ github.run_id }}/test-results-${{ strategy.job-index }}.zip" --acl public-read
          echo "https://plasmic-cypress.s3-us-west-2.amazonaws.com/cypress/${{ github.run_id }}/test-results-${{ strategy.job-index }}.zip"

  results:
    if: ${{ always() }}
    runs-on: ubuntu-latest
    name: Cypress Result
    needs: [studio-test-cypress]
    steps:
      - run: |
          result="${{ needs.studio-test-cypress.result }}"
          if [[ $result == "success" || $result == "skipped" ]]; then
            exit 0
          else
            exit 1
          fi
