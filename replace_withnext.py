#!/usr/bin/env python3
import re
import os
from pathlib import Path

def replace_withnext_with_withtransaction(file_path):
    """Replace withNext(..., { withTransaction: true }) with withTransaction(...)"""
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    original_content = content
    
    # Pattern to match withNext(functionName, { withTransaction: true })
    # This handles both single-line and multi-line cases
    pattern = r'withNext\s*\(\s*([^,]+?)\s*,\s*\{\s*withTransaction\s*:\s*true\s*\}\s*\)'
    
    # Replace with withTransaction(...)
    content = re.sub(pattern, r'withTransaction(\1)', content)
    
    if content != original_content:
        with open(file_path, 'w') as f:
            f.write(content)
        return True
    return False

def main():
    # Find all TypeScript files in the server directory
    server_dir = Path('/workspace/platform/wab/src/wab/server')
    
    changed_files = []
    
    for ts_file in server_dir.rglob('*.ts'):
        if replace_withnext_with_withtransaction(ts_file):
            changed_files.append(ts_file)
    
    print(f"Modified {len(changed_files)} files:")
    for file in changed_files:
        print(f"  - {file.relative_to(server_dir)}")

if __name__ == '__main__':
    main()