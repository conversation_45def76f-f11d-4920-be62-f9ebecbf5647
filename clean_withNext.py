#!/usr/bin/env python3

import re

# GET routes that actually perform DB writes according to the refactor document
WRITE_GET_ROUTES = {
    "/api/v1/app-auth/token",
    "/api/v1/app-ctx",
    "/api/v1/auth/oidc/guidewire-apac/callback",
    "/api/v1/auth/oidc/guidewire-test/callback",
    "/api/v1/auth/oidc/guidewire/callback",
    "/api/v1/auth/oidc/guidewire-eu/callback",
    "/api/v1/auth/sso/:tenantId/consume",
    "/api/v1/billing/subscription/:teamId",
    "/api/v1/data-source/airtable/bases",
    "/api/v1/end-user/app/:projectId/app-user",
    "/api/v1/end-user/app/:projectId/app-users",
    "/api/v1/end-user/app/:projectId/user-props-config",
    "/api/v1/guidewire/users/:gwId",
    "/api/v1/hosting-hit",
    "/api/v1/loader/code/preview",
    "/api/v1/loader/code/published",
    "/api/v1/loader/code/versioned",
    "/api/v1/loader/html/versioned/:projectId/:component",
    "/api/v1/loader/repr-v3/preview/:projectId",
    "/api/v1/oauth2/airtable/callback",
    "/api/v1/oauth2/google/callback",
    "/api/v1/pkgs/:pkgId",
    "/api/v1/plume-pkg",
    "/api/v1/projects",
    "/api/v1/projects/:projectBranchId",
    "/api/v1/projects/:projectId/repositories",
    "/api/v1/projects/:projectId/versions",
    "/api/v1/teams",
    "/api/v1/workspaces",
}

def process_file(filepath):
    with open(filepath, 'r') as f:
        content = f.read()
    
    lines = content.split('\n')
    changes = []
    
    for i in range(len(lines)):
        line = lines[i]
        
        # Skip comments and imports
        if '//' in line or 'import' in line or 'export' in line:
            continue
            
        # Look for withNext calls
        if 'withNext(' in line:
            # Check the preceding lines to find the route
            route_line = None
            for j in range(max(0, i-5), i):
                if 'app.get' in lines[j] or 'app.post' in lines[j] or 'app.put' in lines[j] or 'app.delete' in lines[j] or 'app.patch' in lines[j]:
                    route_line = lines[j]
                    break
            
            if route_line:
                # Extract the HTTP method and route path
                method_match = re.search(r'app\.(get|post|put|delete|patch)', route_line)
                route_match = re.search(r'["\']([^"\']+)["\']', route_line)
                
                if method_match and route_match:
                    method = method_match.group(1).upper()
                    route = route_match.group(1)
                    
                    # Decide what to do
                    if method in ['POST', 'PUT', 'DELETE', 'PATCH']:
                        # Write operations always need transactions
                        lines[i] = lines[i].replace('withNext(', 'withTransaction(')
                        changes.append(f"Line {i+1}: {method} {route} -> Changed to withTransaction")
                    elif method == 'GET' and route in WRITE_GET_ROUTES:
                        # Special GET routes that write
                        lines[i] = lines[i].replace('withNext(', 'withTransaction(')
                        changes.append(f"Line {i+1}: {method} {route} -> Changed to withTransaction (GET that writes)")
                    elif method == 'GET':
                        # Read-only GET routes - remove withNext
                        # This is more complex as we need to handle the parentheses properly
                        # For now, just mark for manual review
                        changes.append(f"Line {i+1}: {method} {route} -> Should remove withNext (read-only)")
    
    if changes:
        print(f"\n{filepath}:")
        for change in changes:
            print(f"  {change}")
    
    return '\n'.join(lines), changes

# Process AppServer.ts
content, changes = process_file('/workspace/platform/wab/src/wab/server/AppServer.ts')

# Write summary
print("\n\nSummary:")
print(f"Total changes to review: {len(changes)}")
print("\nRecommendation: Manually remove withNext wrapper from read-only GET routes.")