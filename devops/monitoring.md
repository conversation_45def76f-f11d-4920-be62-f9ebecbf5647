# How to use Google stack for logging and monitoring?

In this doc, we will see how to login, move between different projects and use the monitoring stack that Google offers.

## How to login in the console

In order to start using Google Cloud Monitoring service, we should authenticate ourselves in the console. For that you need to go here https://console.cloud.google.com/ and use your Plasmic email. Once you're in you should see something like this:
![Screenshot](gcloud-console.png)

## How to move between projects

If you look at the picture above, at the upper left corner you will see this:

![Screenshot](plasmic-app.png)

Which means that we're at the organization level. For we to start working using different services, we need to switch to a project. You simply hit on that plasmic.app button and you will see all folders and projects under the plasmic.app organization. You just need to hit the one that you want to use. The 'monitoring' project will be the one to use for logging and monitoring porpuses.
![Screenshot](plasmic-projects.png)

## How to instrument your code

### Logging

To add logging to your application, you can use the `logger()` function from `@/wab/server/observability`. Here are some examples of how to use it:

1. Basic logging:

```typescript
import { logger } from "@/wab/server/observability";

// Simple log message
logger().info("This is an informational message");

// Log with template literals
const userEmail = getUser(req, { allowUnverifiedEmail: true }).email;
logger().info(`User logged in: ${userEmail}`);

// Log with context object
logger().error("An error occurred", {
  error: err,
  userId: user.id,
  requestId: req.id,
});
```

2. Log levels available:

- `logger().debug()` - For detailed debug information
- `logger().info()` - For general application flow
- `logger().warn()` - For potentially harmful situations
- `logger().error()` - For error events that might still allow the application to continue running

### Distributed Tracing

To add distributed tracing to your application, use the `withSpan` function from `@/wab/server/util/apm-util`. This helps in tracking requests across service boundaries.

```typescript
import { withSpan } from "@/wab/server/util/apm-util";

// Wrap an async operation in a span
await withSpan("Operation name", async () => {
  // Your code here
  await someAsyncOperation();
  logger().info("Operation completed");
});

// Example from app-backend-real.ts
cron.schedule("*/10 * * * *", async () => {
  await withSpan("[Comments] notifications emails", async () => {
    await sendCommentsNotificationEmails(config);
    logger().info("Notification emails sent");
  });
});
```

#### Auto-instrumentation with OpenTelemetry Operator

The application is configured with OpenTelemetry Operator for Kubernetes, which provides automatic instrumentation for various components. The operator is installed via Helm in the `addons` namespace and manages the collection of distributed traces.

Key components:

- **OpenTelemetry Operator**: Manages the lifecycle of OpenTelemetry collectors and auto-instrumentation
- **Collector**: Aggregates and processes telemetry data before exporting it to the monitoring backend
- **Auto-instrumentation**: Automatically injects tracing into supported applications without code changes

The operator is configured to use the OpenTelemetry Collector version 0.124.1, which processes and exports telemetry data to the configured backend.

### Best Practices

1. **Logging**

   - Use appropriate log levels (debug, info, warn, error)
   - Include relevant context in log messages
   - Avoid logging sensitive information
   - Use structured logging when possible (passing objects)

2. **Tracing**
   - Use descriptive span names that indicate the operation being performed
   - Keep spans focused on a single logical operation
   - Nest spans to show parent-child relationships in distributed traces
   - Add relevant tags to spans for better filtering and analysis

## Accessing Logs in the Platform Project

Our production environment uses project-based logging, with the main application logs stored in the 'platform' GCP project. This approach provides better isolation and more straightforward access to logs for the production environment.

To access the logs:

1. Navigate to the 'platform' project in GCP Console
2. Open the Logging service
3. Use the Logs Explorer to query and analyze logs

![choosing-platform-project.png](choosing-platform-project.png)

This setup ensures that all production application logs are easily accessible in one place, while maintaining clear separation from other environments and projects.

Now we're in the right project, it's time to start querying logs and analyzing metrics!

### Tracing Requests in GCP

When debugging distributed systems, you can follow a request's journey across services using Google Cloud's distributed tracing. Here's how to find and analyze traces from your logs:

1. **Locate a Log Entry**
   - Search for relevant log messages in the Logs Explorer
   - Each log entry contains metadata including a trace ID

![getting-the-traceid-by-log-explorer.png](getting-the-traceid-by-log-explorer.png)

2. **Find the Trace**
   - Use the trace ID to search in the Trace Explorer
   - This shows the complete request flow across services

![using-traceid-inside-trace-explorer.png](using-traceid-inside-trace-explorer.png)

3. **Analyze the Trace**
   - The trace view displays the full request lifecycle
   - Identify performance bottlenecks between services
   - View detailed timing for each operation

![viewing-detailed-information.png](viewing-detailed-information.png)

### How to use pre-built GCP Dashboards?

GCP offers a bunch of pre-built dashboards for multiple metrics that are the most commonly used. We can take advantage of those dashboards if we want to have a broader view without having to do much.

1. Right above "Metrics explorer" you'll see an option called "Dashboards". Once we're in, we can see all pre-defined dashboards available to use.
   ![Screenshot](dashboards.png)
2. Let's say that we want to take a look at the cluster resources, we simply clic on "GKE Compute Resources - Cluster View" amd we will see a dashboard with information about the CPU utilization, request and limit as well for memory.
   ![Screenshot](cluster-dashboard.png)

### How to search for custom metrics?

So far we've been working aroung querying and selecting the log type needed and with pre-defined metrics using Dashboards, now let's do the same for specific metrics!

1. In the same page as the logging service, you'll see 'Metrics Explorer' there.

![Screenshot](metrics-service.png)

2. Once in, you need to hit on 'Select a metric' and then select the metric that you're looking for, in this case we will like the see the CPU limit utilization of the containers inside a Kubernetes cluster.
   ![Screenshot](plasmic-metrics.png)
3. You'll see the metrics displayed.
   ![Screenshot](metrics-example.png)
4. If needed we can add a filter to just look an specific metric. For example I wan to see the CPU limit utilization of the kustomize-controller pod, which is part of the new FluxCD system.
   ![Screenshot](kustomize-example.png)
