locals {
  region_vars = read_terragrunt_config(find_in_parent_folders("region.hcl"))
  region      = local.region_vars.locals.region
  dev_ip      = "************"
  prod_ip     = "************"
}

terraform {
  source = "tfr:///terraform-aws-modules/route53/aws//modules/records?version=5.0.0"
}

include "root" {
  path = find_in_parent_folders("root.hcl")
}

dependency "zones" {
  config_path = "../../zones/"
}

inputs = {
  zone_name = dependency.zones.outputs.route53_zone_name["plasmic.app"]

  records = [
    {
      name = "codegen.dev"
      type = "A"
      ttl  = 300
      records = [
        local.dev_ip
      ]
    },
    {
      name = "a.dev"
      type = "A"
      ttl  = 300
      records = [
        local.dev_ip
      ]
    },
    {
      name = "data.dev"
      type = "A"
      ttl  = 300
      records = [
        local.dev_ip
      ]
    },
    {
      name = "flux-webhook.dev"
      type = "A"
      ttl  = 300
      records = [
        local.dev_ip
      ]
    },
    {
      name = "img.dev"
      type = "A"
      ttl  = 300
      records = [
        local.dev_ip
      ]
    },
    {
      name = "site-assets.dev"
      type = "A"
      ttl  = 300
      records = [
        local.dev_ip
      ]
    },
    {
      name = "static1.dev"
      type = "A"
      ttl  = 300
      records = [
        local.dev_ip
      ]
    },
    {
      name = "studio.dev"
      type = "A"
      ttl  = 300
      records = [
        local.dev_ip
      ]
    },
    {
      name = "analytics.dev"
      type = "A"
      ttl  = 300
      records = [
        local.dev_ip
      ]
    },
    {
      name = "codegen"
      type = "CNAME"
      ttl  = 300
      records = [
        "d2675tnxl66y4d.cloudfront.net"
      ]
    },
    {
      name = "a"
      type = "A"
      ttl  = 300
      records = [
        local.prod_ip
      ]
    },
    {
      name = "analytics"
      type = "CNAME"
      alias = {
        evaluate_target_health = true
        name                   = "posthog.plasmic.app"
        zone_id                = dependency.zones.outputs.route53_zone_zone_id["plasmic.app"]
        ttl                    = 0
      }
    },
    {
      name = "data"
      type = "CNAME"
      ttl  = 300
      records = [
        "d1gadqr0d7oip1.cloudfront.net"
      ]
    },
    {
      name = "flux-webhook"
      type = "A"
      ttl  = 300
      records = [
        local.prod_ip
      ]
    },
    {
      name = "img"
      type = "CNAME"
      ttl  = 300
      records = [
        "d395dbw2yrm2nr.cloudfront.net"
      ]
    },
    {
      name = "site-assets"
      type = "CNAME"
      ttl  = 300
      records = [
        "dycylxv34gsu1.cloudfront.net"
      ]
    },
    {
      name = "static1"
      type = "CNAME"
      ttl  = 300
      records = [
        "d3mptpzf6vzrya.cloudfront.net"
      ]
    },
    {
      name = "studio"
      type = "CNAME"
      ttl  = 300
      records = [
        "d1mcc4war3cl3d.cloudfront.net"
      ]
    },
  ]
}
