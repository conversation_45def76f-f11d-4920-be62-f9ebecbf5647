locals {
  org        = read_terragrunt_config(find_in_parent_folders("globals.hcl"))
  project    = read_terragrunt_config(find_in_parent_folders("project.hcl"))
  project_id = local.project.locals.project_id
}

terraform {
  source = "**************:plasmicapp/plasmic-internal.git//devops/infra-modules/gcp/global-lb?ref=master"
}

include {
  path = find_in_parent_folders()
}

dependency "cloud-armor-backend-policy" {
  config_path = "../cloud-armor/platform-backend-policy"
}

dependency "cert-manager" {
  config_path = "../cert-manager/"
}

inputs = {
  project               = local.project_id
  name                  = "platform"
  load_balancing_scheme = "EXTERNAL_MANAGED"
  certificate_map       = dependency.cert-manager.outputs.certificate_maps["platform-lb"].id

  ssl            = true
  https_redirect = true

  backends = {
    analytics-rproxy = {
      port            = "8787"
      protocol        = "HTTP"
      port_name       = "analytics-rproxy"
      timeout_sec     = 10
      security_policy = dependency.cloud-armor-backend-policy.outputs.policy.name

      health_check = {
        request_path        = "/healthcheck"
        port                = "8787"
        unhealthy_threshold = 1
      }

      enable_cdn = false

      log_config = {
        enable      = true
        sample_rate = 1
      }

      groups = [
        {
          group          = "https://www.googleapis.com/compute/v1/projects/platform-442916/zones/us-central1-a/networkEndpointGroups/analytics-rproxy"
          balancing_mode = "RATE"
          max_rate       = 1000
        }
      ]
      iap_config = {
        enable = false
      }
    }
    analytics-200 = {
      port        = "80"
      protocol    = "HTTP"
      port_name   = "analytics-200"
      timeout_sec = 10

      health_check = {
        request_path        = "/healthcheck"
        port                = "80"
        unhealthy_threshold = 1
      }

      enable_cdn = false

      log_config = {
        enable      = true
        sample_rate = 0.01
      }

      groups = [
        {
          group          = "https://www.googleapis.com/compute/v1/projects/platform-442916/zones/us-central1-a/networkEndpointGroups/analytics-200"
          balancing_mode = "RATE"
          max_rate       = 1000
        }
      ]
      iap_config = {
        enable = false
      }
    }
    app-01 = {
      description     = "app no cache backend"
      port            = "3000"
      protocol        = "HTTP"
      port_name       = "app-http"
      timeout_sec     = 180
      enable_cdn      = false
      security_policy = dependency.cloud-armor-backend-policy.outputs.policy.name

      health_check = {
        request_path        = "/healthcheck"
        port                = "3000"
        unhealthy_threshold = 1
      }

      log_config = {
        enable      = true
        sample_rate = 1
      }

      groups = [
        {
          group          = "https://www.googleapis.com/compute/v1/projects/platform-442916/zones/us-central1-a/networkEndpointGroups/app"
          balancing_mode = "RATE"
          max_rate       = 1000
        }
      ]

      iap_config = {
        enable = false
      }
    },
    app-hosting-hit-01 = {
      port             = "3000"
      protocol         = "HTTP"
      port_name        = "app-http"
      timeout_sec      = 180
      compression_mode = "AUTOMATIC"
      security_policy  = dependency.cloud-armor-backend-policy.outputs.policy.name

      health_check = {
        request_path        = "/healthcheck"
        port                = "3000"
        unhealthy_threshold = 1
      }

      enable_cdn = true
      cdn_policy = {
        cache_mode        = "FORCE_CACHE_ALL"
        default_ttl       = 86400
        serve_while_stale = 600
        cache_key_policy = {
          include_host         = true
          include_protocol     = true
          include_query_string = true
        }
      }

      log_config = {
        enable      = true
        sample_rate = 1
      }

      groups = [
        {
          group          = "https://www.googleapis.com/compute/v1/projects/platform-442916/zones/us-central1-a/networkEndpointGroups/app"
          balancing_mode = "RATE"
          max_rate       = 1000
        }
      ]

      iap_config = {
        enable = false
      }
    }
    app-hosting-01 = {
      port             = "3000"
      protocol         = "HTTP"
      port_name        = "app-http"
      timeout_sec      = 180
      compression_mode = "AUTOMATIC"
      security_policy  = dependency.cloud-armor-backend-policy.outputs.policy.name

      health_check = {
        request_path        = "/healthcheck"
        port                = "3000"
        unhealthy_threshold = 1
      }

      enable_cdn = true
      cdn_policy = {
        cache_mode        = "FORCE_CACHE_ALL"
        default_ttl       = 30
        serve_while_stale = 600
        cache_key_policy = {
          include_host         = true
          include_protocol     = true
          include_query_string = true
        }
      }

      log_config = {
        enable      = true
        sample_rate = 1
      }

      groups = [
        {
          group          = "https://www.googleapis.com/compute/v1/projects/platform-442916/zones/us-central1-a/networkEndpointGroups/app"
          balancing_mode = "RATE"
          max_rate       = 1000
        }
      ]

      iap_config = {
        enable = false
      }
    }
    app-data-proxy-01 = {
      port             = "3000"
      protocol         = "HTTP"
      port_name        = "app-http"
      timeout_sec      = 180
      compression_mode = "AUTOMATIC"
      security_policy  = dependency.cloud-armor-backend-policy.outputs.policy.name

      health_check = {
        request_path        = "/healthcheck"
        port                = "3000"
        unhealthy_threshold = 1
      }

      enable_cdn = true
      cdn_policy = {
        cache_mode        = "USE_ORIGIN_HEADERS"
        serve_while_stale = 600
        cache_key_policy = {
          include_host         = true
          include_protocol     = true
          include_query_string = true
        }
      }

      log_config = {
        enable      = true
        sample_rate = 1
      }

      groups = [
        {
          group          = "https://www.googleapis.com/compute/v1/projects/platform-442916/zones/us-central1-a/networkEndpointGroups/app"
          balancing_mode = "RATE"
          max_rate       = 1000
        }
      ]

      iap_config = {
        enable = false
      }
    }
    codegen-loader-01 = {
      port             = "3004"
      protocol         = "HTTP"
      port_name        = "codegen-http"
      timeout_sec      = 300
      compression_mode = "AUTOMATIC"
      security_policy  = dependency.cloud-armor-backend-policy.outputs.policy.name

      health_check = {
        request_path        = "/healthcheck"
        port                = "3004"
        unhealthy_threshold = 1
      }

      enable_cdn = true
      cdn_policy = {
        cache_mode        = "USE_ORIGIN_HEADERS"
        serve_while_stale = 0
        cache_key_policy = {
          include_host         = true
          include_protocol     = true
          include_query_string = true
          include_http_headers = [
            "x-plasmic-api-project-tokens",
            "x-plasmic-uptime-check",
            "x-plasmic-loader-version",
            "x-plasmic-api-user",
            "x-plasmic-api-token",
          ]
        }
        negative_caching = true
        negative_caching_policy = {
          code = 302
          ttl  = 1800
        }
      }

      log_config = {
        enable      = true
        sample_rate = 1
      }

      groups = [
        {
          group          = "https://www.googleapis.com/compute/v1/projects/platform-442916/zones/us-central1-a/networkEndpointGroups/codegen-heavy-ops"
          balancing_mode = "RATE"
          max_rate       = 1000
        }
      ]

      iap_config = {
        enable = false
      }
    }
    codegen-loader-light-ops-01 = {
      port             = "3004"
      protocol         = "HTTP"
      port_name        = "codegen-http"
      timeout_sec      = 180
      compression_mode = "AUTOMATIC"
      security_policy  = dependency.cloud-armor-backend-policy.outputs.policy.name

      health_check = {
        request_path        = "/healthcheck"
        port                = "3004"
        unhealthy_threshold = 1
      }

      enable_cdn = true
      cdn_policy = {
        cache_mode        = "USE_ORIGIN_HEADERS"
        serve_while_stale = 3600
        cache_key_policy = {
          include_host         = true
          include_protocol     = true
          include_query_string = true
          include_http_headers = [
            "x-plasmic-api-project-tokens",
            "x-plasmic-uptime-check",
            "x-plasmic-loader-version",
            "x-plasmic-api-user",
            "x-plasmic-api-token",
          ]
        }
        negative_caching = true
        negative_caching_policy = {
          code = 302
          ttl  = 1800
        }
      }

      log_config = {
        enable      = true
        sample_rate = 1
      }

      groups = [
        {
          group          = "https://www.googleapis.com/compute/v1/projects/platform-442916/zones/us-central1-a/networkEndpointGroups/codegen-light-ops"
          balancing_mode = "RATE"
          max_rate       = 1000
        }
      ]

      iap_config = {
        enable = false
      }
    }
    codegen-01 = {
      port            = "3004"
      protocol        = "HTTP"
      port_name       = "codegen-http"
      timeout_sec     = 360
      security_policy = dependency.cloud-armor-backend-policy.outputs.policy.name

      health_check = {
        request_path        = "/healthcheck"
        port                = "3004"
        unhealthy_threshold = 1
      }

      enable_cdn = false

      log_config = {
        enable      = true
        sample_rate = 1
      }

      groups = [
        {
          group          = "https://www.googleapis.com/compute/v1/projects/platform-442916/zones/us-central1-a/networkEndpointGroups/codegen-heavy-ops"
          balancing_mode = "RATE"
          max_rate       = 1000
        }
      ]

      iap_config = {
        enable = false
      }
    }
    integrations-cms-01 = {
      port             = "3000"
      protocol         = "HTTP"
      port_name        = "integrations-http"
      timeout_sec      = 180
      compression_mode = "AUTOMATIC"
      security_policy  = dependency.cloud-armor-backend-policy.outputs.policy.name

      health_check = {
        request_path        = "/healthcheck"
        port                = "3000"
        unhealthy_threshold = 1
      }

      enable_cdn = true
      cdn_policy = {
        cache_mode        = "USE_ORIGIN_HEADERS"
        serve_while_stale = 600
        cache_key_policy = {
          include_host         = true
          include_protocol     = true
          include_query_string = true
          include_http_headers = [
            "x-plasmic-api-cms-tokens",
          ]
        }
      }

      log_config = {
        enable      = true
        sample_rate = 1
      }

      groups = [
        {
          group          = "https://www.googleapis.com/compute/v1/projects/platform-442916/zones/us-central1-a/networkEndpointGroups/integrations"
          balancing_mode = "RATE"
          max_rate       = 1000
        }
      ]

      iap_config = {
        enable = false
      }
    }
    integrations-data-proxy-01 = {
      port             = "3000"
      protocol         = "HTTP"
      port_name        = "integrations-http"
      timeout_sec      = 180
      compression_mode = "AUTOMATIC"
      security_policy  = dependency.cloud-armor-backend-policy.outputs.policy.name

      health_check = {
        request_path        = "/healthcheck"
        port                = "3000"
        unhealthy_threshold = 1
      }

      enable_cdn = true
      cdn_policy = {
        cache_mode        = "USE_ORIGIN_HEADERS"
        serve_while_stale = 600
        cache_key_policy = {
          include_host         = true
          include_protocol     = true
          include_query_string = true
        }
      }

      log_config = {
        enable      = true
        sample_rate = 1
      }

      groups = [
        {
          group          = "https://www.googleapis.com/compute/v1/projects/platform-442916/zones/us-central1-a/networkEndpointGroups/integrations"
          balancing_mode = "RATE"
          max_rate       = 1000
        }
      ]

      iap_config = {
        enable = false
      }
    }
    integrations-hosting-01 = {
      port             = "3000"
      protocol         = "HTTP"
      port_name        = "integrations-http"
      timeout_sec      = 180
      compression_mode = "AUTOMATIC"
      security_policy  = dependency.cloud-armor-backend-policy.outputs.policy.name

      health_check = {
        request_path        = "/healthcheck"
        port                = "3000"
        unhealthy_threshold = 1
      }

      enable_cdn = true
      cdn_policy = {
        cache_mode        = "FORCE_CACHE_ALL"
        default_ttl       = 86400
        serve_while_stale = 3600
        cache_key_policy = {
          include_host         = true
          include_protocol     = true
          include_query_string = true
        }
      }

      log_config = {
        enable      = true
        sample_rate = 1
      }

      groups = [
        {
          group          = "https://www.googleapis.com/compute/v1/projects/platform-442916/zones/us-central1-a/networkEndpointGroups/integrations"
          balancing_mode = "RATE"
          max_rate       = 1000
        }
      ]

      iap_config = {
        enable = false
      }
    }
    img-optimizer = {
      port             = "3009"
      protocol         = "HTTP"
      port_name        = "img-optimizer-http"
      timeout_sec      = 180
      compression_mode = "DISABLED"
      security_policy  = dependency.cloud-armor-backend-policy.outputs.policy.name

      health_check = {
        request_path        = "/healthcheck"
        port                = "3009"
        unhealthy_threshold = 1
      }

      enable_cdn = true
      cdn_policy = {
        cache_mode  = "CACHE_ALL_STATIC"
        default_ttl = 86400
        max_ttl     = 31536000
        cache_key_policy = {
          include_host         = true
          include_protocol     = true
          include_query_string = true
        }
      }

      log_config = {
        enable      = true
        sample_rate = 1
      }

      groups = [
        {
          group          = "https://www.googleapis.com/compute/v1/projects/platform-442916/zones/us-central1-a/networkEndpointGroups/img-optimizer"
          balancing_mode = "RATE"
          max_rate       = 1000
        }
      ]

      iap_config = {
        enable = false
      }
    }
    flux-webhook-receiver = {
      port            = "80"
      protocol        = "HTTP"
      port_name       = "flux-webhook-receiver"
      timeout_sec     = 180
      security_policy = dependency.cloud-armor-backend-policy.outputs.policy.name

      health_check = {
        request_path = "/healthz"
        port         = "9440"
      }

      enable_cdn = false

      log_config = {
        enable = false
      }

      groups = [
        {
          group          = "https://www.googleapis.com/compute/v1/projects/platform-442916/zones/us-central1-a/networkEndpointGroups/flux-webhook-receiver"
          balancing_mode = "RATE"
          max_rate       = 1000
        }
      ]

      iap_config = {
        enable = false
      }
    }
  }

  backend_buckets = {
    studio = {
      bucket_name      = "studio-plasmic-app"
      description      = "studio spa"
      enable_cdn       = true
      compression_mode = "AUTOMATIC"
      cdn_policy = {
        cache_mode  = "CACHE_ALL_STATIC"
        default_ttl = 86400
        max_ttl     = 31536000
      }
      custom_response_headers = [
        "Content-Security-Policy: base-uri 'self'; object-src 'none'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://studio.plasmic.app https://a.plasmic.app https://js.stripe.com https://cdn.segment.com https://cdn.amplitude.com https://www.google-analytics.com https://www.googletagmanager.com https://*.posthog.com; style-src 'self' 'unsafe-inline' 'unsafe-eval' https://studio.plasmic.app https://fonts.googleapis.com https://*.posthog.com; worker-src 'self' 'unsafe-eval' blob: data:; form-action 'self'; frame-ancestors 'self' https://app.flyingcommerce.io https://app.blinkshop.io https://jutrowebappsui-jutroplatform.dev.ccs.guidewire.net https://jutrowebappsui-jutroplatform.int.ccs.guidewire.net https://cloud.dev.ccs.guidewire.net https://cloud.int.ccs.guidewire.net https://cloud.guidewire.net https://gch-gcc-migration.int.ccs.guidewire.net https://cloud.staging.ccs.guidewire.net https://cloud.omega2-circinus.guidewire.net https://cloud.omega2-cartwheel.guidewire.net https://cloud.omega2-butterfly.guidewire.net https://cloud.omega2-milkyway.guidewire.net https://cloud.omega2-whirlpool.guidewire.net http://localhost:3000 http://localhost:3003"
      ]
    },
    figma = {
      bucket_name      = "studio-plasmic-app"
      description      = "figma-plugin"
      enable_cdn       = true
      compression_mode = "AUTOMATIC"
      cdn_policy = {
        cache_mode  = "CACHE_ALL_STATIC"
        default_ttl = 86400
        max_ttl     = 31536000
      }
    },
    studio-static = {
      bucket_name      = "studio-plasmic-app"
      description      = "studio spa static path. Exists so we are able to set special CORS headers for it"
      enable_cdn       = true
      compression_mode = "AUTOMATIC"
      cdn_policy = {
        cache_mode  = "CACHE_ALL_STATIC"
        default_ttl = 86400
        max_ttl     = 31536000
      }
      custom_response_headers = [
        "Access-Control-Allow-Credentials: false",
        "Access-Control-Allow-Headers: *",
        "Access-Control-Allow-Methods: GET",
        "Access-Control-Allow-Origin: *",
        "Access-Control-Max-Age: 5",
      ]
    }
    site-assets = {
      bucket_name      = "plasmic-site-assets-prod"
      enable_cdn       = true
      compression_mode = "DISABLED"
      cdn_policy = {
        cache_mode  = "CACHE_ALL_STATIC"
        default_ttl = 86400
        max_ttl     = 31536000
      }
      custom_response_headers = [
        "Access-Control-Allow-Credentials: false",
        "Access-Control-Allow-Headers: *",
        "Access-Control-Allow-Methods: GET",
        "Access-Control-Allow-Origin: *",
        "X-Robots-Tag: noindex"
      ]
    }
    static1 = {
      bucket_name      = "plasmic-static1-prod"
      enable_cdn       = true
      compression_mode = "DISABLED"
      cdn_policy = {
        cache_mode  = "CACHE_ALL_STATIC"
        default_ttl = 86400
        max_ttl     = 31536000
      }
      custom_response_headers = [
        "Access-Control-Allow-Credentials: false",
        "Access-Control-Allow-Headers: *",
        "Access-Control-Allow-Methods: GET",
        "Access-Control-Allow-Origin: *",
        "X-Robots-Tag: noindex"
      ]
    }
  }

  url_map = {
    default_service = "studio"
    host_rules = [
      {
        hosts        = ["studio.plasmic.app"]
        path_matcher = "studio"
      },
      {
        hosts        = ["codegen.plasmic.app"]
        path_matcher = "codegen"
      },
      {
        hosts        = ["img.plasmic.app"]
        path_matcher = "img-origin"
      },
      {
        hosts        = ["site-assets.plasmic.app"]
        path_matcher = "site-assets"
      },
      {
        hosts        = ["data.plasmic.app"]
        path_matcher = "data"
      },
      {
        hosts        = ["static1.plasmic.app"]
        path_matcher = "static1"
      },
      {
        hosts        = ["host.plasmicdev.com"]
        path_matcher = "host"
      },
      {
        hosts        = ["flux-webhook.plasmic.app"]
        path_matcher = "flux-webhook-receiver"
      },
      {
        hosts        = ["a.plasmic.app"]
        path_matcher = "analytics-rproxy"
      }
      , {
        hosts        = ["analytics.plasmic.app"]
        path_matcher = "analytics-200"
      }
    ]
    path_matchers = [
      {
        name            = "studio"
        default_service = "studio"
        path_rules = [
          {
            paths   = ["/*"]
            service = "studio"
            custom_error_response_policy = [
              {
                match_response_codes   = ["404"]
                override_response_code = 200
                path                   = "/index.html"
                error_service          = "studio"
              }
            ]
          },
          {
            paths   = ["/static/*"]
            service = "studio-static"
          },
          {
            paths   = ["/api/*"]
            service = "app-01"
            route_action = {
              retry_policy = {
                num_retries = 3
                per_try_timeout = {
                  seconds = 180
                }
                retry_conditions = [
                  "gateway-error",
                  "connect-failure"
                ]
              }
            }
          },
          {
            paths   = ["/api/v1/hosting-hit"]
            service = "app-hosting-hit-01"
            route_action = {
              retry_policy = {
                num_retries = 3
                per_try_timeout = {
                  seconds = 180
                }
                retry_conditions = [
                  "gateway-error",
                  "connect-failure"
                ]
              }
            }
          },
          {
            paths   = ["/api/v1/project-token-for-domain"]
            service = "app-hosting-01"
            route_action = {
              retry_policy = {
                num_retries = 3
                per_try_timeout = {
                  seconds = 180
                }
                retry_conditions = [
                  "gateway-error",
                  "connect-failure"
                ]
              }
            }
          },
          {
            paths   = ["/api/v1/app-auth/*"]
            service = "app-data-proxy-01"
            route_action = {
              retry_policy = {
                num_retries = 3
                per_try_timeout = {
                  seconds = 180
                }
                retry_conditions = [
                  "gateway-error",
                  "connect-failure"
                ]
              }
            }
          },
          {
            paths   = ["/api/v1/loader/*"]
            service = "codegen-loader-01"
            route_action = {
              retry_policy = {
                num_retries = 3
                per_try_timeout = {
                  seconds = 300
                }
                retry_conditions = [
                  "gateway-error",
                  "connect-failure"
                ]
              }
            }
          },
          {
            paths   = ["/api/v1/cms/*"]
            service = "integrations-cms-01"
            route_action = {
              retry_policy = {
                num_retries = 3
                per_try_timeout = {
                  seconds = 180
                }
                retry_conditions = [
                  "gateway-error",
                  "connect-failure"
                ]
              }
            }
          },
          {
            paths   = ["/figma-plugin-app/*"]
            service = "figma"
            custom_error_response_policy = [
              {
                match_response_codes   = ["404"]
                override_response_code = 200
                path                   = "/figma-plugin-app/index.html"
                error_service          = "studio"
              }
            ]
          }
        ]
      },
      {
        name            = "codegen"
        default_service = "codegen-01"
        path_rules = [
          {
            paths   = ["/api/v1/loader/code/published"]
            service = "codegen-loader-light-ops-01"
            route_action = {
              retry_policy = {
                num_retries = 3
                per_try_timeout = {
                  seconds = 180
                }
                retry_conditions = [
                  "gateway-error",
                  "connect-failure"
                ]
              }
            }
          },
          {
            paths   = ["/api/v1/loader/*"]
            service = "codegen-loader-01"
            route_action = {
              retry_policy = {
                num_retries = 3
                per_try_timeout = {
                  seconds = 180
                }
                retry_conditions = [
                  "gateway-error",
                  "connect-failure"
                ]
              }
            }
          },
        ]
      },
      {
        name            = "img-origin"
        default_service = "img-optimizer"
        path_rules = [
          {
            paths   = ["/*"]
            service = "img-optimizer"
            route_action = {
              retry_policy = {
                num_retries = 3
                per_try_timeout = {
                  seconds = 180
                }
                retry_conditions = [
                  "gateway-error",
                  "connect-failure"
                ]
              }
            }
          }
        ]
      },
      {
        name            = "site-assets"
        default_service = "site-assets"
        path_rules      = []
      },
      {
        name            = "data"
        default_service = "integrations-cms-01"
        path_rules = [
          {
            paths   = ["/api/v1/project-token-for-domain"]
            service = "integrations-hosting-01"
            route_action = {
              retry_policy = {
                num_retries = 3
                per_try_timeout = {
                  seconds = 180
                }
                retry_conditions = [
                  "gateway-error",
                  "connect-failure"
                ]
              }
            }
          },
          {
            paths   = ["/api/v1/app-auth/*"]
            service = "integrations-data-proxy-01"
            route_action = {
              retry_policy = {
                num_retries = 3
                per_try_timeout = {
                  seconds = 180
                }
                retry_conditions = [
                  "gateway-error",
                  "connect-failure"
                ]
              }
            }
          },
          {
            paths   = ["/api/v1/cms/*"]
            service = "integrations-cms-01"
            route_action = {
              retry_policy = {
                num_retries = 3
                per_try_timeout = {
                  seconds = 180
                }
                retry_conditions = [
                  "gateway-error",
                  "connect-failure"
                ]
              }
            }
          }
        ]
      },
      {
        name            = "static1"
        default_service = "static1"
        path_rules      = []
      },
      {
        name            = "host"
        default_service = "studio-static"
        path_rules      = []
      },
      {
        name            = "flux-webhook-receiver"
        default_service = "flux-webhook-receiver"
        path_rules      = []
      },
      {
        name            = "analytics-rproxy"
        default_service = "analytics-rproxy"
        path_rules = [
          {
            paths   = ["/*"]
            service = "analytics-rproxy"
            route_action = {
              retry_policy = {
                num_retries = 3
                per_try_timeout = {
                  seconds = 180
                }
                retry_conditions = [
                  "gateway-error",
                  "connect-failure"
                ]
              }
            }
          }
        ]
      }
      , {
        name            = "analytics-200"
        default_service = "analytics-200"
        path_rules = [
          {
            paths   = ["/*"]
            service = "analytics-200"
            route_action = {
              retry_policy = {
                num_retries = 3
                per_try_timeout = {
                  seconds = 10
                }
                retry_conditions = [
                  "gateway-error",
                  "connect-failure"
                ]
              }
            }
          }
        ]
      }
    ]
  }
}
