# This file is maintained automatically by "tofu init".
# Manual edits may be lost in future updates.

provider "registry.opentofu.org/hashicorp/google" {
  version     = "6.48.0"
  constraints = ">= 6.14.0, < 7.0.0"
  hashes = [
    "h1:v4d3msmCatVBRTQsVIcbEHVu8kmE37ucQboF52/p0lI=",
    "zh:32e18025c1a1cc440d511de362ff0d066f03014c6f1aa874a4ce3c368d90ea4b",
    "zh:554a50ef629ee68c8e61e2c7180e0da4a052937cd1ce9ea01f419ebe4967ff96",
    "zh:5c844e6ef60cb6a344ed532df764e01134e28f25c4dca4ff39dd7324c0627774",
    "zh:5f81516d97b9cde9443ebedfeec33c7791746d0f387b6cd9a426a4330ec9d942",
    "zh:afe41760bf21ba081a7d14db4cf384877e840f66d9a7957c0f234181ddee09df",
    "zh:cccba2a7120af1f1641198ee37fcba9aae51a9dc7369ab7981370ffd53ddbba6",
    "zh:e246b3fee0edb334ca973177d3d05977075400fb61d2be632444bf9776b36c97",
    "zh:e3289e5f4e4d41ce2e519650bef3550bfee3310e1d3b697408cb4896a266fb43",
    "zh:f89a1f356e2ba9ee38b1e2d2d2b3c3820a2373349e90a563d089beb9d3950c57",
    "zh:ffd5b247da54beaa83b4cf38dc009a1ec950e61a3d325648abd37d648f74c43d",
  ]
}

provider "registry.opentofu.org/hashicorp/google-beta" {
  version     = "6.48.0"
  constraints = ">= 6.14.0, < 7.0.0"
  hashes = [
    "h1:tKQt/GxDJWDSGH3EpdJgT3+XSDk0d5ibp/siD+fkmJs=",
    "zh:0cc3a8a02677cffb19638f117ebf7b8bc4f94c2ad1be656578e1f93e0436dfae",
    "zh:6c5c91a9d3a4d13cecfdcc4c376fc83f956170b94cfc89d75ddd07139d5285cc",
    "zh:78c5b7c2fef03a558ca2db0821ac24114bd0e4c11fb0fed88b2c6aef27382211",
    "zh:7b5b86582270d571490e0e70f47506a92ab5eec453eae0324debcf8efce2d263",
    "zh:7f8f3cc1702d42c27516a6535a7528277892666350a1328fdc34fe9ab53a5b30",
    "zh:7feb2b7341ebf2c80259867aaf97a2fa5701f152ff4f82580c16bebf3980e4b5",
    "zh:c5ccf729ce59b4c70b744ddc3286d1b694c07d6c07c72bda7eafa9f2381a5691",
    "zh:e895533522928935754f5f342b0745f41a4da29847bc6462d8e758166040c533",
    "zh:f3f77e6e20f241801200913fe06985e2f417852ea6478223d5b8fff771d19b6d",
    "zh:fe4b2d0560f07501584849b8037579e7fca2b86f0c44a62c340802a7649a7191",
  ]
}
