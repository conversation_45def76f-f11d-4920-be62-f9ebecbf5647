locals {
  project    = read_terragrunt_config(find_in_parent_folders("project.hcl"))
  project_id = local.project.locals.project_id
}

terraform {
  source = "tfr:///GoogleCloudPlatform/cloud-armor/google//?version=5.0.0"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  project_id                           = local.project_id
  name                                 = "platform"
  description                          = "Policy to protect backend services"
  default_rule_action                  = "allow"
  type                                 = "CLOUD_ARMOR"
  layer_7_ddos_defense_enable          = true
  layer_7_ddos_defense_rule_visibility = "STANDARD"
  json_parsing                         = "STANDARD"

  // XFF_IP was chosen over IP because in case of proxies it will block each individual
  // client behind that proxy since it will consider the ip set in the request header by
  // the proxy, and if it doesn't exist it will fallback to IP.
  // This will avoid accounting as the same client multiple people connected to the same
  // network. Although we can argue that malicious proxies may take advantage of this,
  // penalizing legitimate proxies would be worse. We can cover DDoS attacks with other
  // rules.
  custom_rules = {
    "very-high-rate-limit" = {
      action        = "throttle"
      priority      = 8000
      description   = "Rate limiter for high volume endpoints"
      preview       = false
      src_ip_ranges = ["*"]
      expression    = <<-EOT
        request.path.lower().matches('(?:execute-studio)?')
      EOT

      rate_limit_options = {
        exceed_action                        = "deny(429)"
        rate_limit_http_request_count        = 1500
        rate_limit_http_request_interval_sec = 60
        enforce_on_key_configs = [
          {
            enforce_on_key_type = "HTTP_PATH"
            enforce_on_key_name = ""
          },
          {
            enforce_on_key_type = "XFF_IP"
            enforce_on_key_name = ""
          }
        ]
      }
    }
    "high-rate-limiter" = {
      action        = "throttle"
      priority      = 9000
      description   = "Rate Limiter for high volume endpoints"
      preview       = false
      src_ip_ranges = ["*"]
      expression    = <<-EOT
        request.headers['host'].lower().matches('(?:a|data)\\.plasmic\\.app')
      EOT

      rate_limit_options = {
        exceed_action                        = "deny(429)"
        rate_limit_http_request_count        = 1000
        rate_limit_http_request_interval_sec = 60
        enforce_on_key_configs = [
          {
            enforce_on_key_type = "HTTP_PATH"
            enforce_on_key_name = ""
          },
          {
            enforce_on_key_type = "XFF_IP"
            enforce_on_key_name = ""
          }
        ]
      }
    }
    "default-rate-limiter" = {
      action        = "throttle"
      priority      = 10000
      description   = "normal endpoints rate limiter"
      preview       = false
      src_ip_ranges = ["*"]
      expression    = <<-EOT
        true
      EOT

      rate_limit_options = {
        exceed_action                        = "deny(429)"
        rate_limit_http_request_count        = 500
        rate_limit_http_request_interval_sec = 60
        enforce_on_key_configs = [
          {
            enforce_on_key_type = "HTTP_PATH"
            enforce_on_key_name = ""
          },
          {
            enforce_on_key_type = "XFF_IP"
            enforce_on_key_name = ""
          }
        ]
      }
    }
  }
}
