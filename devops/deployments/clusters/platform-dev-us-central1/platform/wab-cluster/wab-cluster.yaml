kind: "postgresql"
apiVersion: "acid.zalan.do/v1"
metadata:
  name: "postgres-wab-cluster"
  namespace: "platform"
  labels:
    team: acid
spec:
  teamId: "acid"
  postgresql:
    version: "15"
    parameters:
      wal_keep_segments: "200"
  numberOfInstances: 1
  users:
    wab: []
    superwab:
      - superuser
      - createdb
  databases:
    wab: wab
  usersIgnoringSecretRotation:
    - wab
    - suberwab
  resources:
    requests:
      cpu: 100m
      memory: 100Mi
    limits:
      cpu: 500m
      memory: 500Mi
  volume:
    size: "5Gi"
    storageClass: "standard-rwo"
  env:
    - name: ALLOW_NOSSL
      value: "true"
    - name: USE_WALG_BACKUP
      value: "true"
    - name: USE_WALG_RESTORE
      value: "true"
    - name: BACKUP_SCHEDULE
      value: "0 * * * *"
