apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres-wab-cluster-exporter
  labels:
    app: postgres-wab-cluster-exporter
    pg-cluster-name: postgres-wab-cluster
spec:
  selector:
    matchLabels:
      app: postgres-wab-cluster-exporter
  template:
    metadata:
      labels:
        app: postgres-wab-cluster-exporter
        pg-cluster-name: postgres-wab-cluster
    spec:
      containers:
        - name: prom-exporter
          image: quay.io/prometheuscommunity/postgres-exporter:v0.17.1
          ports:
            - containerPort: 9187
              name: metrics
          readinessProbe:
            httpGet:
              path: /metrics
              port: metrics
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
          env:
            - name: DATA_SOURCE_URI
              value: postgres-wab-cluster:5432/postgres?sslmode=disable
            - name: DATA_SOURCE_USER
              valueFrom:
                secretKeyRef:
                  name: postgres.postgres-wab-cluster.credentials.postgresql.acid.zalan.do
                  key: username
            - name: D<PERSON><PERSON>_SOURCE_PASS
              valueFrom:
                secretKeyRef:
                  name: postgres.postgres-wab-cluster.credentials.postgresql.acid.zalan.do
                  key: password
            - name: PG_EXPORTER_EXTEND_QUERY_PATH
              value: /etc/postgres-exporter/queries/queries.yaml
          volumeMounts:
            - name: queries
              mountPath: /etc/postgres-exporter/queries
              readOnly: true
          resources:
            requests:
              cpu: 100m
              memory: 100Mi
            limits:
              cpu: 500m
              memory: 500Mi
      volumes:
        - name: queries
          configMap:
            name: postgres-exporter-queries
