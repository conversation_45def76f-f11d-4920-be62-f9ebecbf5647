apiVersion: apps/v1
kind: Deployment
metadata:
  name: img-optimizer
spec:
  selector:
    matchLabels:
      app: img-optimizer
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: img-optimizer
      annotations:
        prometheus.io/scrape: "true"
    spec:
      containers:
        - name: img-optimizer
          image: us-central1-docker.pkg.dev/registries-442916/plasmic-prod-us-central1/img_optimizer:dev-85-7a2c47
          imagePullPolicy: Always
          ports:
            - containerPort: 3009
          env:
            - name: NODE_OPTIONS
              value: --max-old-space-size=900
            - name: S3_ENDPOINT
              value: https://storage.googleapis.com
            - name: IMAGE_OPTIMIZER_HOST
              value: img.dev.plasmic.app
            - name: SITE_ASSETS_BUCKET
              value: plasmic-site-assets-dev
          readinessProbe:
            httpGet:
              path: /healthcheck
              port: 3009
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
          livenessProbe:
            httpGet:
              path: /healthcheck
              port: 3009
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
          volumeMounts:
            - name: secrets-volume
              mountPath: /home/<USER>/.aws/credentials
              subPath: appserver-aws-credentials
          resources:
            requests:
              cpu: 100m
              memory: "128Mi"
      volumes:
        - name: secrets-volume
          secret:
            secretName: prod-secrets
      terminationGracePeriodSeconds: 180
# Add this back in if we want to force these to run on an "untrusted" node group.
#      nodeSelector:
#        alpha.eksctl.io/nodegroup-name: untrusted
#      tolerations:
#        - key: "nodegroup"
#          operator: "Equal"
#          value: "untrusted"
#          effect: "NoSchedule"
