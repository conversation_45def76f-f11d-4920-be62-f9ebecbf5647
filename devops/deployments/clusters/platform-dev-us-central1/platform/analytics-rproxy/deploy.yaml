apiVersion: apps/v1
kind: Deployment
metadata:
  name: analytics-rproxy
spec:
  selector:
    matchLabels:
      app: analytics-rproxy
  # Wait for 20 minutes for deployment to finish; most of the time is spent
  # pulling the image down from ecr
  progressDeadlineSeconds: 1200
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: analytics-rproxy
      annotations:
        instrumentation.opentelemetry.io/inject-nodejs: "true"
        prometheus.io/scrape: "true"
    spec:
      securityContext:
        fsGroup: 1000
      containers:
        - name: analytics-rproxy
          image: us-central1-docker.pkg.dev/registries-442916/plasmic-prod-us-central1/analytics-rproxy:latest
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 8787
          env:
            - name: NATS_SERVERS
              value: nats://nats:4222
            - name: POSTHOG_KEY
              value: phc_eaI1hFsPRIZkmwrXaSGRNDh4H9J3xdh1j9rgNy27NgP
            - name: POSTHOG_API_HOST
              value: us.i.posthog.com
            - name: POSTHOG_ASSETS_HOST
              value: us-assets.i.posthog.com
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
          readinessProbe:
            httpGet:
              path: /healthcheck
              port: 8787
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
      dnsConfig:
        # Reduce DNS queries traffic:
        # https://pracucci.com/kubernetes-dns-resolution-ndots-options-and-why-it-may-affect-analytics-rproxylication-performances.html
        options:
          - name: ndots
            value: "2"
