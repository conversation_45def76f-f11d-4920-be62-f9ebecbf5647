apiVersion: helm.toolkit.fluxcd.io/v2beta1
kind: HelmRelease
metadata:
  name: placeholder
spec:
  values:
    listenerMetrics:
      counters:
        gha_started_jobs_total:
          labels:
            [
              "repository",
              "organization",
              "enterprise",
              "job_name",
              "event_name",
              "job_workflow_ref",
            ]
        gha_completed_jobs_total:
          labels:
            [
              "repository",
              "organization",
              "enterprise",
              "job_name",
              "event_name",
              "job_result",
              "job_workflow_ref",
            ]
      gauges:
        gha_assigned_jobs:
          labels:
            ["name", "namespace", "repository", "organization", "enterprise"]
        gha_running_jobs:
          labels:
            ["name", "namespace", "repository", "organization", "enterprise"]
        gha_registered_runners:
          labels:
            ["name", "namespace", "repository", "organization", "enterprise"]
        gha_busy_runners:
          labels:
            ["name", "namespace", "repository", "organization", "enterprise"]
        gha_min_runners:
          labels:
            ["name", "namespace", "repository", "organization", "enterprise"]
        gha_max_runners:
          labels:
            ["name", "namespace", "repository", "organization", "enterprise"]
        gha_desired_runners:
          labels:
            ["name", "namespace", "repository", "organization", "enterprise"]
        gha_idle_runners:
          labels:
            ["name", "namespace", "repository", "organization", "enterprise"]
      histograms:
        gha_job_startup_duration_seconds:
          labels:
            [
              "repository",
              "organization",
              "enterprise",
              "job_name",
              "event_name",
              "job_workflow_ref",
            ]
          buckets:
            [
              0.01,
              0.05,
              0.1,
              0.5,
              1.0,
              2.0,
              3.0,
              4.0,
              5.0,
              6.0,
              7.0,
              8.0,
              9.0,
              10.0,
              12.0,
              15.0,
              18.0,
              20.0,
              25.0,
              30.0,
              40.0,
              50.0,
              60.0,
              70.0,
              80.0,
              90.0,
              100.0,
              110.0,
              120.0,
              150.0,
              180.0,
              210.0,
              240.0,
              300.0,
              360.0,
              420.0,
              480.0,
              540.0,
              600.0,
              900.0,
              1200.0,
              1800.0,
              2400.0,
              3000.0,
              3600.0,
            ]
        gha_job_execution_duration_seconds:
          labels:
            [
              "repository",
              "organization",
              "enterprise",
              "job_name",
              "event_name",
              "job_result",
              "job_workflow_ref",
            ]
          buckets:
            [
              0.01,
              0.05,
              0.1,
              0.5,
              1.0,
              2.0,
              3.0,
              4.0,
              5.0,
              6.0,
              7.0,
              8.0,
              9.0,
              10.0,
              12.0,
              15.0,
              18.0,
              20.0,
              25.0,
              30.0,
              40.0,
              50.0,
              60.0,
              70.0,
              80.0,
              90.0,
              100.0,
              110.0,
              120.0,
              150.0,
              180.0,
              210.0,
              240.0,
              300.0,
              360.0,
              420.0,
              480.0,
              540.0,
              600.0,
              900.0,
              1200.0,
              1800.0,
              2400.0,
              3000.0,
              3600.0,
            ]
