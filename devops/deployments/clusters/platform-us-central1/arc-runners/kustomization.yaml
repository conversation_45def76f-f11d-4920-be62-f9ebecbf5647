apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: arc-runners
resources:
  - cypress/
  - loader-playwright-tests/
  - loader-jest-tests/
  - studio-root-tests/
  - 4cpus-16gb-runner/
  - tiny-runner/
  - migrate-bundles/
  - tests-storybook/
  - pod-monitor.yaml

patches:
  - path: listener-metrics-patch.yaml
    target:
      kind: HelmRelease
      version: v2beta1
      group: helm.toolkit.fluxcd.io

secretGenerator:
  - name: arc-runner-secrets
    files:
      - values.yaml=secrets.enc.yaml
configurations:
  - kustomizeconfig.yaml
