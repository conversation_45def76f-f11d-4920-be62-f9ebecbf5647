apiVersion: apps/v1
kind: Deployment
metadata:
  name: codegen-heavy-ops
spec:
  selector:
    matchLabels:
      app: codegen-heavy-ops
  # Wait for 20 minutes for deployment to finish; most of the time is spent
  # pulling the image down from ecr
  progressDeadlineSeconds: 1200
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: codegen-heavy-ops
      annotations:
        instrumentation.opentelemetry.io/inject-nodejs: "true"
        prometheus.io/scrape: "true"
    spec:
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
          labelSelector:
            matchLabels:
              app: codegen-heavy-ops
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: cloud.google.com/machine-family
                    operator: In
                    values:
                      - c4
                  - key: workload-type
                    operator: In
                    values:
                      - platform
      tolerations:
        - key: workload-type
          operator: Equal
          value: platform
          effect: NoSchedule
      securityContext:
        fsGroup: 1000
      containers:
        - name: codegen-server
          image: us-central1-docker.pkg.dev/registries-442916/plasmic-prod-us-central1/codegen:dev-85-7a2c47
          # Would love to figure out how to clean this up.
          command:
            - bash
            - -lc
            - cp ~/pgpass ~/.pgpass && chmod 600 ~/.pgpass && node -r esbuild-register -r dotenv/config src/wab/server/codegen-backend.ts
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3004
          env:
            - name: NODE_OPTIONS
              value: --max-old-space-size=5500
            - name: NQ_SQLJS
              value: "1"
            - name: TERMINATION_GRACE_PERIOD_MS
              value: "10500"
            - name: REACT_APP_PUBLIC_URL
              value: https://studio.plasmic.app
            - name: SITE_ASSETS_BUCKET
              value: plasmic-site-assets-prod
            - name: SITE_ASSETS_BASE_URL
              value: https://site-assets.plasmic.app/
            - name: NODE_ENV
              value: production
            - name: HOST
              value: https://codegen.plasmic.app
            - name: MAIL_CONFIG
              valueFrom:
                configMapKeyRef:
                  name: mail-config
                  key: config.json
            - name: DATABASE_URI
              value: postgres://wab@postgres-wab-cluster/wab
            - name: CODEGEN_HOST
              value: https://codegen.plasmic.app
            - name: CODEGEN_ORIGIN_HOST
              value: http://codegen-heavy-ops
            - name: INTEGRATIONS_HOST
              value: https://data.plasmic.app
            - name: INTEGRATIONS_ORIGIN_HOST
              value: http://integrations
            - name: LOADER_ASSETS_BUCKET
              value: plasmic-loader-assets-prod
            - name: SESSION_SECRET
              valueFrom:
                secretKeyRef:
                  name: prod-secrets
                  key: session-secret
            - name: SENTRY_DSN
              value: "https://<EMAIL>/5918913"
            - name: BWRAP_ARGS
              value: "--ro-bind /app/wab /app/wab"
            - name: S3_ENDPOINT
              value: https://storage.googleapis.com
            - name: GENERIC_WORKER_POOL_SIZE
              value: "2"
            - name: LOADER_WORKER_POOL_SIZE
              value: "1"
          resources:
            requests:
              cpu: "1.5"
              memory: "5Gi"
          readinessProbe:
            httpGet:
              path: /healthcheck
              port: 3004
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
          # TODO: add a better solution giving only bwrap the necessary permissions inside the dockerfile
          securityContext:
            privileged: true
          volumeMounts:
            # Names must match the volume names below
            - name: secrets-volume
              mountPath: /home/<USER>/pgpass
              subPath: pgpass
            - name: secrets-volume
              mountPath: /home/<USER>/.plasmic/secrets.json
              subPath: secrets.json
            - name: secrets-volume
              mountPath: /home/<USER>/.aws/credentials
              subPath: appserver-aws-credentials
        - name: nginx
          image: nginx:alpine
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
          resources:
            requests:
              cpu: "100m"
              memory: "30Mi"
          readinessProbe:
            httpGet:
              path: /healthcheck
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
          livenessProbe:
            httpGet:
              path: /healthcheck
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
          volumeMounts:
            - name: nginx-proxy-config
              mountPath: /etc/nginx/nginx.conf
              subPath: nginx.conf
          lifecycle:
            preStop:
              exec:
                command:
                  - sh
                  - -c
                  - sleep 10s && nginx -s quit # wait 10s before initiate nginx shutdown to give gke time to remove the pod from lb target list
      dnsConfig:
        # Reduce DNS queries traffic:
        # https://pracucci.com/kubernetes-dns-resolution-ndots-options-and-why-it-may-affect-application-performances.html
        options:
          - name: ndots
            value: "2"
      terminationGracePeriodSeconds: 120
      volumes:
        - name: secrets-volume
          secret:
            secretName: prod-secrets
            # chmod 600 - but this doesn't seem to be working?
            defaultMode: 384
        - name: nginx-proxy-config
          configMap:
            name: nginx-proxy-config
