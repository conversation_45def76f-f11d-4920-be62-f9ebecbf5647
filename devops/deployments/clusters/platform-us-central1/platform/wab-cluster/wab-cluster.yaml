kind: "postgresql"
apiVersion: "acid.zalan.do/v1"
metadata:
  name: "postgres-wab-cluster"
  namespace: "platform"
  labels:
    team: acid
spec:
  teamId: "acid"
  postgresql:
    version: "15"
    parameters:
      wal_keep_segments: "200"
      work_mem: "8192"
      # logging configs
      ## log level
      log_min_messages: warning
      log_min_error_statement: error
      ## sample log long queries that take at least the configured amount of time
      log_min_duration_sample: 1000ms
      log_statement_sample_rate: "0.1"
      ## log when a query waits for a lock for more then the configured amount of time
      log_lock_waits: "on"
      deadlock_timeout: 200ms
      ## log when a temp file bigger then the amunt configured in bytes is created
      log_temp_files: "10240" # 10MB
      ## logs when auto vacuum takes more then the amount of time configured to run
      log_autovacuum_min_duration: 10s
      log_checkpoints: "on"
      ## noisy log configs off
      log_connections: "off"
      log_disconnections: "off"
      log_statement: "none"
      ## log formatting
      log_destination: "stderr"
      logging_collector: "off"
  numberOfInstances: 2
  allowedSourceRanges:
    - 0.0.0.0/0
  users:
    wab: []
    superwab:
      - superuser
      - createdb
  databases:
    wab: wab
  usersIgnoringSecretRotation:
    - wab
    - suberwab
  resources:
    requests:
      cpu: "3"
      memory: 16Gi
    limits:
      memory: 30Gi
  volume:
    size: "4096G"
    storageClass: hyperdisk-balanced
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
        - matchExpressions:
            - key: workload-type
              operator: In
              values:
                - database
            - key: cloud.google.com/machine-family
              operator: In
              values:
                - c4
  tolerations:
    - key: workload-type
      operator: Equal
      value: database
      effect: NoSchedule
  env:
    - name: ALLOW_NOSSL
      value: "true"
