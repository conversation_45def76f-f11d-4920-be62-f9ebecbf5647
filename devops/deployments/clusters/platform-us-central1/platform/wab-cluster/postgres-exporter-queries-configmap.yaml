apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-exporter-queries
data:
  queries.yaml: |
    # Top 20 slowest queries (aggregated by queryid) for database 'wab'
    wab_top_slowest_queries:
      query: |
        WITH agg AS (
          SELECT
            d.datname AS database,
            s.queryid,
            SUM(s.calls) AS calls,
            SUM(s.total_exec_time) AS total_exec_time,
            MAX(s.max_exec_time) AS max_exec_time,
            (SUM(s.total_exec_time) / NULLIF(SUM(s.calls), 0)) AS mean_exec_time,
            SUM(s.rows) AS rows,
            MIN(s.query) AS query
          FROM metric_helpers.pg_stat_statements s
          JOIN pg_database d ON d.oid = s.dbid
          WHERE s.toplevel
            AND s.dbid = (
              SELECT oid
              FROM pg_database
              WHERE datname = 'wab'
            )
          GROUP BY d.datname, s.queryid
        )
        SELECT
          database,
          calls,
          ROUND(mean_exec_time::numeric, 2) AS mean_exec_ms,
          ROUND(max_exec_time::numeric, 2) AS max_exec_ms,
          ROUND((total_exec_time / 1000.0)::numeric, 3) AS total_exec_seconds,
          rows,
          LEFT(query, 120) AS sample_query,
          queryid::text AS queryid
        FROM agg
        WHERE calls >= 10
        ORDER BY max_exec_time DESC
        LIMIT 20;
      metrics:
        - database:
            usage: "LABEL"
            description: "Database name"
        - queryid:
            usage: "LABEL"
            description: "Query identifier"
        - sample_query:
            usage: "LABEL"
            description: "Truncated normalized query (for context)"
        - calls:
            usage: "COUNTER"
            description: "Total calls (aggregated over users)"
        - mean_exec_ms:
            usage: "GAUGE"
            description: "Weighted mean execution time in ms"
        - max_exec_ms:
            usage: "GAUGE"
            description: "Max single-call execution time in ms"
        - total_exec_seconds:
            usage: "COUNTER"
            description: "Total execution time in seconds"
        - rows:
            usage: "COUNTER"
            description: "Total rows returned"
