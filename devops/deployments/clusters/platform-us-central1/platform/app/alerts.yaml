apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: app-alerts
spec:
  chart:
    spec:
      chart: gcp-alerts
      version: "0.1.3"
      sourceRef:
        kind: HelmRepository
        name: plasmic
        namespace: flux-system
  interval: 10m
  values:
    serviceName: app
    backendHighLatency:
      # at the moment this service has huge latencies.
      # The quick requests are in the minutes range in p95, going up to hours.
      enabled: false
      message: ""
      duration: 300s
      threshold: 1000
      evaluationInterval: 60s
      stabilizationWindow: 1m

    backendHighRequestRate:
      enabled: true
      message: ""
      threshold: 10
      duration: 900s
      evaluationInterval: 60s
      stabilizationWindow: 1m

    backendLowRequestRate:
      enabled: true
      message: ""
      threshold: 0.1
      duration: 900s
      evaluationInterval: 60s
      stabilizationWindow: 1m
