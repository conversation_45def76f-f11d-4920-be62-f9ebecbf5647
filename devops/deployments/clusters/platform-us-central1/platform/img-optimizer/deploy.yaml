apiVersion: apps/v1
kind: Deployment
metadata:
  name: img-optimizer
spec:
  selector:
    matchLabels:
      app: img-optimizer
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: img-optimizer
      annotations:
        instrumentation.opentelemetry.io/inject-nodejs: "true"
        prometheus.io/scrape: "true"
    spec:
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
          labelSelector:
            matchLabels:
              app: img-optimizer
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: cloud.google.com/machine-family
                    operator: In
                    values:
                      - c4
                  - key: workload-type
                    operator: In
                    values:
                      - platform
      tolerations:
        - key: workload-type
          operator: Equal
          value: platform
          effect: NoSchedule
      containers:
        - name: img-optimizer
          image: us-central1-docker.pkg.dev/registries-442916/plasmic-prod-us-central1/img_optimizer:dev-85-7a2c47
          imagePullPolicy: Always
          ports:
            - containerPort: 3009
          env:
            - name: NODE_OPTIONS
              value: --max-old-space-size=900
            - name: S3_ENDPOINT
              value: https://storage.googleapis.com
            - name: IMAGE_OPTIMIZER_HOST
              value: img.plasmic.app
            - name: SITE_ASSETS_BUCKET
              value: plasmic-site-assets-prod
            - name: SENTRY_DSN
              value: https://<EMAIL>/4507115734630400
            - name: NODE_ENV
              value: production
          readinessProbe:
            httpGet:
              path: /healthcheck
              port: 3009
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
          livenessProbe:
            httpGet:
              path: /healthcheck
              port: 3009
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
          volumeMounts:
            - name: secrets-volume
              mountPath: /home/<USER>/.aws/credentials
              subPath: appserver-aws-credentials
          resources:
            requests:
              cpu: "1"
              memory: "1Gi"
            limits:
              memory: "1Gi"
      volumes:
        - name: secrets-volume
          secret:
            secretName: prod-secrets
# Add this back in if we want to force these to run on an "untrusted" node group.
#      nodeSelector:
#        alpha.eksctl.io/nodegroup-name: untrusted
#      tolerations:
#        - key: "nodegroup"
#          operator: "Equal"
#          value: "untrusted"
#          effect: "NoSchedule"
