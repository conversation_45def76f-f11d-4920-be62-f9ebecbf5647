apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: platform
## This 'images' section is used for kustomize to update any matching string as 'name' with the desired tag.
## The commented line along 'newTag' tells flux to update it just by fetching the tag, and it references the imagePolicy
images:
- name: us-central1-docker.pkg.dev/registries-442916/plasmic-prod-us-central1/codegen
  newTag: "625-296c78" # {"$imagepolicy": "flux-system:codegen:tag"}
- name: us-central1-docker.pkg.dev/registries-442916/plasmic-prod-us-central1/img_optimizer
  newTag: "494-0caac2" # {"$imagepolicy": "flux-system:img-optimizer:tag"}
- name: us-central1-docker.pkg.dev/registries-442916/plasmic-prod-us-central1/analytics-rproxy
  newTag: "431-6176e8" # {"$imagepolicy": "flux-system:analytics-rproxy:tag"}
resources:
# shared configuration resources
- namespace.yaml
- secrets.yaml
- config-connector-context.yaml
- nginx-config-cm.yaml
- mail-config-cm.yaml
# services
- app/
- codegen/
- socket-server/
- integrations/
- img-optimizer/
- analytics-rproxy/
- analytics-200/
# databases
- wab-cluster/
- nats/
# due to gcp limitations we are managing loadblanacer in terraform
# otel-resources
- otel-collector/
patches:
# ensure all deployments punder platform auto restart when any
# of the configmaps or secrets has an update
- patch: |
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: PLACEHOLDER
      annotations:
        reloader.stakater.com/auto: "true"
  target:
    kind: Deployment
