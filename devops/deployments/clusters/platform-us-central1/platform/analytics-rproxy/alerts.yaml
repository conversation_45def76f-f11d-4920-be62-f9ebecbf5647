apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: analytics-rproxy-alerts
spec:
  chart:
    spec:
      chart: gcp-alerts
      version: "0.1.3"
      sourceRef:
        kind: HelmRepository
        name: plasmic
        namespace: flux-system
  interval: 10m
  values:
    serviceName: analytics-rproxy
    backendHighLatency:
      message: ""
      duration: 300s
      threshold: 5000
      evaluationInterval: 60s
      stabilizationWindow: 1m

    backendHighRequestRate:
      enabled: true
      message: ""
      threshold: 15
      duration: 900s
      evaluationInterval: 60s
      stabilizationWindow: 1m

    backendLowRequestRate:
      # request rate is still too low to have an alert for low request rate
      enabled: false
      message: ""
      threshold: 0.5
      duration: 900s
      evaluationInterval: 60s
      stabilizationWindow: 1m
