# Infrastructure documentation overview

The following documentation will be used as an index, here you will find a technical overview of the new solutions and also the documentation created for each of these.

## New folder structure and source of truth

'devops' folder will be the place where we centralize everything new related to Terraform/grunt and FluxCD. These two new folders called 'infra-live' and 'deployments' are the ones that you should check if you're planning on deploying new infrastructure or new applications to the cluster.

```bash
devops
├── deployments
├── infra-live
```

## Getting started

- [Google Cloud](gcloud.md)

## infra-live

Terragrunt is the tool we're using to create Infrastructure resources such as the cluster, iam service_accounts, projects, etc. If you need to add new resources, first you should intall opentofu and terragrunt.

- Opentofu: <https://opentofu.org/docs/intro/install/>
- Terragrunt: <https://terragrunt.gruntwork.io/docs/getting-started/install/>

When you install the tools needed for this, you can check this documentation for further understanding and examples.
[infra-live](infra-live/README.md)

## deployments

FluxCD will be the tools used to deploy applications in the cluster. For this to happen, we need to add new resources in 'deployments' folder and push it to GitHub, FluxCD will be aware of this changes once they're in 'master' branch. You wont need to install any cli or extra tool to do it, just following this documentation for a better understanding and, once again, clear examples and explanations about every single step.
[deployments](deployments/README.md)

## Monitoring stack

For monitoring we're using Google services such as Monitoring Dashboards, Metrics and Logging. Also we're using a centralized project to host all logs and metrics coming from all projects, but you can check logs and metrics on every project as well. With this documentation you can check how to authenticate, understand the difference and benefits for using a centralized monitoring project, check and filter logs and metrics and finally, uses pre-built dashboards from GCP to visualize everything.
[Monitoring](monitoring.md)

## Getting access

### gcloud

```shell
gcloud auth login
```

Note that this is only needed the first time to authenticate our local environment with GCP.

Once we got gcloud authenticated, we can follow this documentation to add required plugins to authenticate against GKE <https://cloud.google.com/kubernetes-engine/docs/how-to/cluster-access-for-kubectl#install_plugin>.

### Kubernetes

The following command will get credentials to interact with platform cluster, if needed we can run the same command replacing 'platform-us-central1' with 'platform-dev-us-central1' to fetch creds for dev cluster. Remember that dev cluster is behind WireGuard, so you will need to be in the VPN to interact with it.

```shell
gcloud container clusters get-credentials platform-us-central1 --region us-central1
kubectl cluster-info # check
```

# Run book and Troubleshooting

## PostgreSQL Database

Our PG database runs in Kubernetes. To get access, let's first decode the credentials:

```shell
kubectl get secret -n platform postgres.postgres-wab-cluster.credentials.postgresql.acid.zalan.do -o yaml
```

This will show the username and password in base 64. Use `echo '<base64>' -n | base64 -d'` to decode.

Now we forward the port to our local machine:

```shell
kubectl port-forward -n platform postgres-wab-cluster-0 <local-port>:5432
```

### Graceful Primary Switch Over

We have a standby replica that is ready to take on the load at any time.
At any moment that we see memory pressure, or disk issues in the primary replica
we can execute these steps to manually switch over to the replica and start investigating
the primary without waiting for it to explode or restarting the pod, giving us a better
view of what happened inside the pod for the issue to happen.

1. Connect via kubectl exec to any of the database pods

  ```bash
  k exec -it -n platform postgres-wab-cluster-0 bash
  ```

2. Execute the `patronictl` command to switch over. You should see something like this:

  ```bash
  root@postgres-wab-cluster-0:/home/<USER>
  Current cluster topology
  + Cluster: postgres-wab-cluster (7493339010031489083) ------+----+-----------+
  | Member                 | Host       | Role    | State     | TL | Lag in MB |
  +------------------------+------------+---------+-----------+----+-----------+
  | postgres-wab-cluster-0 | ********** | Leader  | running   | 11 |           |
  | postgres-wab-cluster-1 | ********** | Replica | streaming | 11 |         0 |
  +------------------------+------------+---------+-----------+----+-----------+
  Primary [postgres-wab-cluster-0]: 
  Candidate ['postgres-wab-cluster-1'] []: 
  When should the switchover take place (e.g. 2025-08-14T15:23 )  [now]: 
  Are you sure you want to switchover cluster postgres-wab-cluster, demoting current leader postgres-wab-cluster-0? [y/N]: y
  2025-08-14 14:23:20.97482 Successfully switched over to "postgres-wab-cluster-1"
  + Cluster: postgres-wab-cluster (7493339010031489083) ----+----+-----------+
  | Member                 | Host       | Role    | State   | TL | Lag in MB |
  +------------------------+------------+---------+---------+----+-----------+
  | postgres-wab-cluster-0 | ********** | Replica | stopped |    |   unknown |
  | postgres-wab-cluster-1 | ********** | Leader  | running | 11 |           |
  +------------------------+------------+---------+---------+----+-----------+
  ```

## Using Kubernetes GUI

There're multiple GUI to manage Kubernetes resources. One of the most lightweight and popular is k9s. You can install it by following this doc: <https://k9scli.io/topics/install/>

Once you've installed it, simply run 'k9s' in your terminal and you will be able to interact with Kubernetes API.

This is a demo video from k9s developers showing how to use k9s <https://k9scli.io/#:~:text=Tutorials-,Previews,-Pulses%20-%20A>

### Backup and restore

We use [velero.md](velero.md) to backup and restore our PG database.

## Arc Runners

Check [arc-runners.md](arc-runners.md) for all arc runners documented issues and troubleshooting guides.
