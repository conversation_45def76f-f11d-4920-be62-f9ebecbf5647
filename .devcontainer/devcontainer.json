{"name": "Claude Code Sandbox", "build": {"dockerfile": "Dockerfile", "args": {"TZ": "${localEnv:TZ:America/Los_Angeles}", "CLAUDE_CODE_VERSION": "latest", "GIT_DELTA_VERSION": "0.18.2", "ZSH_IN_DOCKER_VERSION": "1.2.0"}}, "runArgs": ["--cap-add=NET_ADMIN", "--cap-add=NET_RAW"], "customizations": {"vscode": {"settings": {"terminal.integrated.defaultProfile.linux": "zsh", "terminal.integrated.profiles.linux": {"bash": {"path": "bash", "icon": "terminal-bash"}, "zsh": {"path": "zsh"}}}}}, "remoteUser": "node", "mounts": ["source=claude-code-bashhistory-${devcontainerId},target=/commandhistory,type=volume", "source=claude-code-config-${devcontainerId},target=/home/<USER>/.claude,type=volume", "source=nodemods,target=/workspace/node_modules,type=volume", "source=plasmic_platform_nodemods,target=/workspace/platform/node_modules,type=volume", "source=platform_ai_tests_nodemods,target=/workspace/platform/ai-tests/node_modules,type=volume", "source=platform_analytics_rproxy_nodemods,target=/workspace/platform/analytics-rproxy/node_modules,type=volume", "source=platform_canvas_nodemods,target=/workspace/platform/canvas-packages/node_modules,type=volume", "source=platform_hosting_nodemods,target=/workspace/platform/hosting/node_modules,type=volume", "source=platform_img_optimizer_nodemods,target=/workspace/platform/img-optimizer/node_modules,type=volume", "source=platform_live_frame_nodemods,target=/workspace/platform/live-frame/node_modules,type=volume", "source=platform_loader_bundle_env_nodemods,target=/workspace/platform/loader-bundle-env/node_modules,type=volume", "source=platform_loader_html_hydrate_nodemods,target=/workspace/platform/loader-html-hydrate/node_modules,type=volume", "source=platform_loader_tests_nodemods,target=/workspace/platform/loader-tests/node_modules,type=volume", "source=platform_react_web_bundle_nodemods,target=/workspace/platform/react-web-bundle/node_modules,type=volume", "source=platform_sub_nodemods,target=/workspace/platform/sub/node_modules,type=volume", "source=platform_wab_nodemods,target=/workspace/platform/wab/node_modules,type=volume"], "containerEnv": {"NODE_OPTIONS": "--max-old-space-size=16384", "CLAUDE_CONFIG_DIR": "/home/<USER>/.claude", "POWERLEVEL9K_DISABLE_GITSTATUS": "true"}, "workspaceMount": "source=${localWorkspaceFolder},target=/workspace,type=bind,consistency=delegated", "workspaceFolder": "/workspace", "postCreateCommand": "sudo /usr/local/bin/init-firewall.sh"}