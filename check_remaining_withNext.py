#!/usr/bin/env python3

# GET routes that perform DB writes (need withTransaction)
WRITE_GET_ROUTES = [
    "/api/v1/app-auth/token",
    "/api/v1/app-ctx",
    "/api/v1/auth/oidc/guidewire-apac/callback",
    "/api/v1/auth/oidc/guidewire-test/callback",
    "/api/v1/auth/oidc/guidewire/callback",
    "/api/v1/auth/oidc/guidewire-eu/callback",
    "/api/v1/auth/sso/:tenantId/consume",
    "/api/v1/billing/subscription/:teamId",
    "/api/v1/data-source/airtable/bases",
    "/api/v1/end-user/app/:projectId/app-user",
    "/api/v1/end-user/app/:projectId/app-users",
    "/api/v1/end-user/app/:projectId/user-props-config",
    "/api/v1/guidewire/users/:gwId",
    "/api/v1/hosting-hit",
    "/api/v1/loader/code/preview",
    "/api/v1/loader/code/published",
    "/api/v1/loader/code/versioned",
    "/api/v1/loader/html/versioned/:projectId/:component",
    "/api/v1/loader/repr-v3/preview/:projectId",
    "/api/v1/oauth2/airtable/callback",
    "/api/v1/oauth2/google/callback",
    "/api/v1/pkgs/:pkgId",
    "/api/v1/plume-pkg",
    "/api/v1/projects",
    "/api/v1/projects/:projectBranchId",
    "/api/v1/projects/:projectId/repositories",
    "/api/v1/projects/:projectId/versions",
    "/api/v1/teams",
    "/api/v1/workspaces",
]

# Routes found with withNext in AppServer.ts
routes_with_withNext = [
    ("GET", "/api/v1/cmse/databases/:dbId", "getCmsDatabaseAndSecretTokenById"),
    ("GET", "/api/v1/wl/:whiteLabelName/users/:externalUserId", "getWhiteLabelUser"),
    ("GET", "/api/v1/data-source/sources/:dataSourceId", "getDataSourceById"),
    ("GET", "/api/v1/analytics/team/:teamId/project/:projectId", "getAnalyticsForProject"),
    ("GET", "/api/v1/analytics/team/:teamId/project/:projectId/meta", "getAnalyticsProjectMeta"),
    ("GET", "/api/v1/analytics/team/:teamId/billing", "getAnalyticsBillingInfoForTeam"),
    ("GET", "/api/v1/end-user/app/:projectId/pub-config", "getAppAuthPubConfig"),
    ("GET", "/api/v1/end-user/app/:projectId/access-rules", "listAppAccessRules"),
    ("GET", "/api/v1/end-user/app/:projectId/user-role/:endUserId", "getUserRoleInApp"),
    ("GET", "/api/v1/end-user/directories/:directoryId", "getEndUserDirectory"),
    ("GET", "/api/v1/end-user/directories/:directoryId/apps", "getEndUserDirectoryApps"),
    ("GET", "/api/v1/end-user/directories/:directoryId/users", "getDirectoryUsers"),
    ("GET", "/api/v1/end-user/teams/:teamId/directories", "listTeamEndUserDirectories"),
    ("GET", "/api/v1/end-user/directories/:directoryId/groups", "listDirectoryGroups"),
    ("GET", "/api/v1/end-user/app/:projectId/access-registry", "listAppAccessRegistries"),
    ("GET", "/api/v1/loader/html/preview/:projectId/:component", "buildLatestLoaderHtml"),
    ("GET", "/api/v1/loader/repr-v2/preview/:projectId", "buildLatestLoaderReprV2"),
    ("GET", "/static/js/loader-hydrate.:hash.js", "getHydrationScriptVersioned"),
    ("GET", "/api/v1/auth/getEmailVerificationToken", "getEmailVerificationToken"),
    ("GET", "/api/v1/oauth2/google-sheets/callback", "googleSheetsCallback"),
    ("GET", "/api/v1/auth/integrations", "getUserAuthIntegrations"),
    ("GET", "/api/v1/admin/feature-tiers", "listAllFeatureTiers"),
    ("GET", "/api/v1/admin/devflags", "getDevFlagOverrides"),
    ("GET", "/api/v1/admin/devflags/versions", "getDevFlagVersions"),
    ("GET", "/api/v1/admin/get-sso", "getSsoByTeam"),
    ("GET", "/api/v1/admin/get-team-by-white-label-name", "getTeamByWhiteLabelName"),
    ("GET", "/api/v1/admin/app-auth-metrics", "getAppAuthMetrics"),
    ("GET", "/api/v1/admin/project/:projectId/app-meta", "getProjectAppMeta"),
    ("GET", "/api/v1/admin/project/:projectId/rev", "getLatestProjectRevision"),
    ("GET", "/api/v1/admin/pkg-version/data", "getPkgVersion"),
    ("GET", "/api/v1/admin/teams/:teamId/discourse-info", "getTeamDiscourseInfo"),
    ("GET", "/api/v1/admin/project-branches-metadata/:projectId", "getProjectBranchesMetadata"),
    ("GET", "/api/v1/pkgs/projectId/:projectId", "getPkgVersionByProjectId"),
    ("GET", "/api/v1/pkgs/:pkgId/versions-without-data", "listPkgVersionsWithoutData"),
    ("GET", "/api/v1/projects/:projectId/meta", "getProjectMeta"),
    ("GET", "/api/v1/projects/:projectId/branches", "listBranchesForProject"),
    ("GET", "/api/v1/projects/:projectId/revision-without-data", "getProjectRevWithoutData"),
    ("GET", "/api/v1/project-data/:projectId", "getFullProjectData"),
    ("GET", "/api/v1/projects/:projectId/pkgs/:pkgVersionId/status", "getPkgVersionPublishStatus"),
    ("GET", "/api/v1/teams/:teamId", "getTeamById"),
    ("GET", "/api/v1/teams/:teamId/meta", "getTeamMeta"),
    ("GET", "/api/v1/teams/:teamId/projects", "getTeamProjects"),
    ("GET", "/api/v1/teams/:teamId/workspaces", "getTeamWorkspaces"),
    ("GET", "/api/v1/feature-tiers", "listCurrentFeatureTiers"),
    ("GET", "/api/v1/projects/:projectId/webhooks/events", "getProjectWebhookEvents"),
    ("GET", "/api/v1/project_repositories/:projectRepositoryId/latest-run", "getLatestWorkflowRun"),
    ("GET", "/api/v1/project_repositories/:projectRepositoryId/runs/:workflowRunId", "getGitWorkflowJob"),
    ("GET", "/api/v1/hosts", "getTrustedHostsForSelf"),
    ("GET", "/api/v1/promo-code/:promoCodeId", "getPromotionCodeById"),
]

# Check which ones need transaction
for method, route, handler in routes_with_withNext:
    needs_transaction = False
    for write_route in WRITE_GET_ROUTES:
        # Simple pattern matching (not perfect but good enough)
        if write_route in route or route in write_route:
            needs_transaction = True
            break
    
    if needs_transaction:
        print(f"NEEDS TRANSACTION: {route} -> {handler}")
    else:
        print(f"REMOVE withNext: {route} -> {handler}")
