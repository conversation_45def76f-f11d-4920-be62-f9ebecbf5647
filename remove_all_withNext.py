#!/usr/bin/env python3

import re

# GET routes that perform DB writes and need withTransaction
WRITE_GET_ROUTES = {
    "/api/v1/app-auth/token",
    "/api/v1/app-ctx",
    "/api/v1/auth/oidc/guidewire-apac/callback",
    "/api/v1/auth/oidc/guidewire-test/callback",
    "/api/v1/auth/oidc/guidewire/callback",
    "/api/v1/auth/oidc/guidewire-eu/callback",
    "/api/v1/auth/sso/:tenantId/consume",
    "/api/v1/billing/subscription/:teamId",
    "/api/v1/data-source/airtable/bases",
    "/api/v1/end-user/app/:projectId/app-user",
    "/api/v1/end-user/app/:projectId/app-users",
    "/api/v1/end-user/app/:projectId/user-props-config",
    "/api/v1/guidewire/users/:gwId",
    "/api/v1/hosting-hit",
    "/api/v1/loader/code/preview",
    "/api/v1/loader/code/published",
    "/api/v1/loader/code/versioned",
    "/api/v1/loader/html/versioned/:projectId/:component",
    "/api/v1/loader/repr-v3/preview/:projectId",
    "/api/v1/oauth2/airtable/callback",
    "/api/v1/oauth2/google/callback",
    "/api/v1/pkgs/:pkgId",
    "/api/v1/plume-pkg",
    "/api/v1/projects",
    "/api/v1/projects/:projectBranchId",
    "/api/v1/projects/:projectId/repositories",
    "/api/v1/projects/:projectId/versions",
    "/api/v1/teams",
    "/api/v1/workspaces",
}

def remove_withNext_wrapper(line):
    """Remove withNext wrapper from a line, keeping just the handler."""
    # Pattern to match withNext(handler) and extract handler
    match = re.search(r'withNext\s*\(([^)]+)\)', line)
    if match:
        handler = match.group(1)
        return line.replace(match.group(0), handler)
    return line

def process_appserver():
    with open('/workspace/platform/wab/src/wab/server/AppServer.ts', 'r') as f:
        lines = f.readlines()
    
    modified = False
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # Skip comments
        if '//' in line and line.strip().startswith('//'):
            i += 1
            continue
        
        # Check if this line has withNext
        if 'withNext(' in line:
            # Look backwards to find the route definition
            route_path = None
            method = None
            for j in range(i-1, max(0, i-10), -1):
                if 'app.' in lines[j]:
                    # Extract method
                    method_match = re.search(r'app\.(get|post|put|delete|patch)', lines[j])
                    if method_match:
                        method = method_match.group(1).upper()
                    
                    # Extract route path
                    route_match = re.search(r'["\']([^"\']+)["\']', lines[j])
                    if route_match:
                        route_path = route_match.group(1)
                    break
            
            # Decide what to do
            if method == 'GET' and route_path:
                if route_path not in WRITE_GET_ROUTES:
                    # Remove withNext wrapper for read-only GET routes
                    lines[i] = remove_withNext_wrapper(lines[i])
                    print(f"Removed withNext from GET {route_path}")
                    modified = True
        
        i += 1
    
    if modified:
        with open('/workspace/platform/wab/src/wab/server/AppServer.ts', 'w') as f:
            f.writelines(lines)
        print("\nFile updated successfully!")
    
    return modified

if __name__ == "__main__":
    process_appserver()
